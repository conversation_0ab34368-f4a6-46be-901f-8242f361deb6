import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import BLEService, { DiscoveredDevice, ConnectedDevice } from '@/services/BLEService';
import { post, del } from '@/helpers/http';

interface DeviceConnectionState {
  isConnecting: boolean;
  connectedDevices: ConnectedDevice[];
  connectionError: string | null;
}

interface UseDeviceConnectionReturn extends DeviceConnectionState {
  connectToDevice: (device: DiscoveredDevice) => Promise<void>;
  disconnectFromDevice: (deviceId: string) => Promise<void>;
  addDeviceToAccount: (device: DiscoveredDevice) => Promise<void>;
  removeDeviceFromAccount: (deviceId: number) => Promise<void>;
  isDeviceConnected: (deviceId: string) => boolean;
  getConnectedDevice: (deviceId: string) => ConnectedDevice | undefined;
  refreshConnections: () => void;
}

export const useDeviceConnection = (): UseDeviceConnectionReturn => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  
  const [state, setState] = useState<DeviceConnectionState>({
    isConnecting: false,
    connectedDevices: [],
    connectionError: null,
  });

  // Handle connected devices updates
  const handleConnectedDevices = useCallback((devices: ConnectedDevice[]) => {
    setState(prev => ({
      ...prev,
      connectedDevices: devices,
    }));
  }, []);

  // Setup BLE connection listener
  useEffect(() => {
    BLEService.addConnectionListener(handleConnectedDevices);
    
    // Get initial connected devices
    const initialDevices = BLEService.getConnectedDevices();
    setState(prev => ({
      ...prev,
      connectedDevices: initialDevices,
    }));

    return () => {
      BLEService.removeConnectionListener(handleConnectedDevices);
    };
  }, [handleConnectedDevices]);

  // Add device to user account mutation
  const addDeviceMutation = useMutation({
    mutationFn: async (device: DiscoveredDevice) => {
      const response = await post('user/devices', {
        name: device.name || device.localName || 'Unknown Device',
        deviceId: device.id,
        deviceType: device.isBrewtool ? 'BREWTOOL' : 'GENERIC',
        manufacturerData: device.manufacturerData,
        serviceUUIDs: device.serviceUUIDs,
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    },
    onError: (error) => {
      Alert.alert(
        t('ble.addDeviceError'),
        error instanceof Error ? error.message : t('ble.unknownError')
      );
    },
  });

  // Remove device from user account mutation
  const removeDeviceMutation = useMutation({
    mutationFn: async (deviceId: number) => {
      await del(`user/devices/${deviceId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    },
    onError: (error) => {
      Alert.alert(
        t('ble.removeDeviceError'),
        error instanceof Error ? error.message : t('ble.unknownError')
      );
    },
  });

  // Connect to device
  const connectToDevice = useCallback(async (device: DiscoveredDevice) => {
    setState(prev => ({
      ...prev,
      isConnecting: true,
      connectionError: null,
    }));

    try {
      const connectedDevice = await BLEService.connectToDevice(device.id);
      
      setState(prev => ({
        ...prev,
        isConnecting: false,
      }));

      Alert.alert(
        t('ble.connectionSuccess'),
        t('ble.deviceConnected', { 
          name: device.name || device.localName || t('ble.unknownDevice') 
        }),
        [
          {
            text: t('ble.addToMyDevices'),
            onPress: () => addDeviceMutation.mutate(device),
          },
          { text: t('common.ok') },
        ]
      );

      return connectedDevice;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('ble.unknownError');
      
      setState(prev => ({
        ...prev,
        isConnecting: false,
        connectionError: errorMessage,
      }));

      Alert.alert(t('ble.connectionError'), errorMessage);
      throw error;
    }
  }, [addDeviceMutation, t]);

  // Disconnect from device
  const disconnectFromDevice = useCallback(async (deviceId: string) => {
    try {
      await BLEService.disconnectFromDevice(deviceId);
      
      Alert.alert(
        t('ble.disconnectionSuccess'),
        t('ble.deviceDisconnected')
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('ble.unknownError');
      
      setState(prev => ({
        ...prev,
        connectionError: errorMessage,
      }));

      Alert.alert(t('ble.disconnectionError'), errorMessage);
      throw error;
    }
  }, [t]);

  // Add device to user account
  const addDeviceToAccount = useCallback(async (device: DiscoveredDevice) => {
    try {
      await addDeviceMutation.mutateAsync(device);
      
      Alert.alert(
        t('ble.deviceAdded'),
        t('ble.deviceAddedMessage', { 
          name: device.name || device.localName || t('ble.unknownDevice') 
        })
      );
    } catch (error) {
      // Error is handled by the mutation
      throw error;
    }
  }, [addDeviceMutation, t]);

  // Remove device from user account
  const removeDeviceFromAccount = useCallback(async (deviceId: number) => {
    try {
      await removeDeviceMutation.mutateAsync(deviceId);
      
      Alert.alert(
        t('ble.deviceRemoved'),
        t('ble.deviceRemovedMessage')
      );
    } catch (error) {
      // Error is handled by the mutation
      throw error;
    }
  }, [removeDeviceMutation, t]);

  // Check if device is connected
  const isDeviceConnected = useCallback((deviceId: string): boolean => {
    return state.connectedDevices.some(device => device.id === deviceId);
  }, [state.connectedDevices]);

  // Get connected device by ID
  const getConnectedDevice = useCallback((deviceId: string): ConnectedDevice | undefined => {
    return state.connectedDevices.find(device => device.id === deviceId);
  }, [state.connectedDevices]);

  // Refresh connections
  const refreshConnections = useCallback(() => {
    const currentDevices = BLEService.getConnectedDevices();
    setState(prev => ({
      ...prev,
      connectedDevices: currentDevices,
    }));
  }, []);

  return {
    ...state,
    connectToDevice,
    disconnectFromDevice,
    addDeviceToAccount,
    removeDeviceFromAccount,
    isDeviceConnected,
    getConnectedDevice,
    refreshConnections,
  };
};
