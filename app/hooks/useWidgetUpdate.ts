import { useEffect } from 'react';
import { useWidget } from '@/providers/WidgetProvider';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook to automatically update widget data when fermentation data changes
 * This should be used in components that display fermentation data
 */
export const useWidgetUpdate = () => {
  const { refreshWidgetData, widgetData, isLoading, error } = useWidget();
  const { isAuthenticated } = useAuth();

  // Manual refresh function
  const refreshWidget = async () => {
    if (isAuthenticated) {
      await refreshWidgetData();
    }
  };

  return {
    widgetData,
    isLoading,
    error,
    refreshWidget,
    isAuthenticated,
  };
};

/**
 * Hook to trigger widget updates when specific data changes
 * Use this in screens that modify fermentation data
 */
export const useWidgetDataSync = (dependencies: any[] = []) => {
  const { refreshWidgetData } = useWidget();
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && dependencies.some(dep => dep !== undefined)) {
      // Debounce the update to avoid too frequent calls
      const timeoutId = setTimeout(() => {
        refreshWidgetData();
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [isAuthenticated, refreshWidgetData, ...dependencies]);
};

export default useWidgetUpdate;
