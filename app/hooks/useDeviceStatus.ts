import { useMemo } from "react";
import { Brewtool } from "@/services/BrewtoolService";

export type DeviceStatus = "fermentation" | "coldCrash" | "standby" | "offline";

export const COLD_CRUSH = 4;

export default function useDeviceStatus(device: Brewtool): DeviceStatus {
  return useMemo(() => {
    // Check if device is offline (no recent data)
    if (!device.statistic?.updatedAt) {
      return "offline";
    }

    // Check if data is older than 5 minutes
    const lastUpdate = new Date(device.statistic.updatedAt);
    const now = new Date();
    const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
    
    if (diffMinutes > 5) {
      return "offline";
    }

    // Check if device has active temperature configuration
    if (!device.config?.temperature) {
      return "standby";
    }

    // Check if it's cold crash temperature
    if (device.config.temperature === COLD_CRUSH) {
      return "coldCrash";
    }

    // Active fermentation
    return "fermentation";
  }, [device.statistic?.updatedAt, device.config?.temperature]);
}
