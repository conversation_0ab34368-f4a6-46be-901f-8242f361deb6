import { useMemo } from "react";

type DataEntry = {
  id: number;
  createdAt: string | Date;
  temperature?: number;
  gravity?: number;
  [key: string]: string | number | Date | undefined;
};

type ChangeType = "increase" | "decrease" | "stable";

export default function useCalculateDataForLastHour(
  data: DataEntry[] | undefined,
  field: "temperature" | "gravity"
) {
  return useMemo(() => {
    if (!data || data.length === 0) {
      return {
        changeType: "stable" as ChangeType,
        changesForLastHour: null,
      };
    }

    // Filter data from the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentData = data.filter((entry) => {
      const entryDate = new Date(entry.createdAt);
      return entryDate >= oneHourAgo && entry[field] !== undefined;
    });

    if (recentData.length < 2) {
      return {
        changeType: "stable" as ChangeType,
        changesForLastHour: null,
      };
    }

    // Sort by date (oldest first)
    recentData.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );

    const firstValue = recentData[0][field] as number;
    const lastValue = recentData[recentData.length - 1][field] as number;
    const change = lastValue - firstValue;

    let changeType: ChangeType = "stable";
    if (Math.abs(change) > 0.1) {
      // Only consider significant changes
      changeType = change > 0 ? "increase" : "decrease";
    }

    return {
      changeType,
      changesForLastHour: Math.abs(change).toFixed(1),
    };
  }, [data, field]);
}
