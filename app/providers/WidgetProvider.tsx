import React, { createContext, useContext, useEffect, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import WidgetService, { WidgetData } from '@/services/WidgetService';
import { useAuth } from '@/contexts/AuthContext';

interface WidgetContextType {
  widgetData: WidgetData | null;
  isLoading: boolean;
  error: string | null;
  refreshWidgetData: () => Promise<void>;
  lastUpdated: Date | null;
}

const WidgetContext = createContext<WidgetContextType | undefined>(undefined);

interface WidgetProviderProps {
  children: React.ReactNode;
}

export const WidgetProvider: React.FC<WidgetProviderProps> = ({ children }) => {
  const [widgetData, setWidgetData] = useState<WidgetData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const { isAuthenticated } = useAuth();

  // Fetch widget data
  const fetchWidgetData = async (force = false) => {
    if (!isAuthenticated) {
      setWidgetData(null);
      setError(null);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const data = force 
        ? await WidgetService.refreshWidgetData()
        : await WidgetService.getWidgetData();

      setWidgetData(data);
      setLastUpdated(new Date());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch widget data';
      setError(errorMessage);
      console.error('Widget data fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh widget data (force refresh)
  const refreshWidgetData = async () => {
    await fetchWidgetData(true);
  };

  // Initialize widget data on mount and auth change
  useEffect(() => {
    if (isAuthenticated) {
      fetchWidgetData();
    } else {
      setWidgetData(null);
      setError(null);
    }
  }, [isAuthenticated]);

  // Set up periodic updates
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      fetchWidgetData();
    }, 5 * 60 * 1000); // Update every 5 minutes

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && isAuthenticated) {
        // App became active, refresh widget data
        fetchWidgetData();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [isAuthenticated]);

  // Clear widget data on logout
  useEffect(() => {
    if (!isAuthenticated) {
      WidgetService.clearWidgetData();
    }
  }, [isAuthenticated]);

  const value: WidgetContextType = {
    widgetData,
    isLoading,
    error,
    refreshWidgetData,
    lastUpdated,
  };

  return (
    <WidgetContext.Provider value={value}>
      {children}
    </WidgetContext.Provider>
  );
};

export const useWidget = (): WidgetContextType => {
  const context = useContext(WidgetContext);
  if (context === undefined) {
    throw new Error('useWidget must be used within a WidgetProvider');
  }
  return context;
};
