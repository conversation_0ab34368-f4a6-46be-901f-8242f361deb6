import React from "react";
import { useTranslation } from "react-i18next";
import { FlatList } from "react-native";
import { Box } from "@/components/ui/box";
import { Card } from "@/components/ui/card";
import { Text } from "@/components/ui/text";
import { Center } from "@/components/ui/center";
import { Brewtool } from "@/services/BrewtoolService";
import DeviceCard from "./DeviceCard";

type DeviceListProps = {
  devices?: Brewtool[];
};

const DeviceList = ({ devices }: DeviceListProps) => {
  const { t } = useTranslation();

  if (!devices || devices.length === 0) {
    return (
      <Card className="py-10 mx-4">
        <Center>
          <Text className="text-gray-600">
            {t("fermentation.noDevicesFound")}
          </Text>
        </Center>
      </Card>
    );
  }

  return (
    <FlatList
      data={devices}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => <DeviceCard device={item} />}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 20 }}
    />
  );
};

export default DeviceList;
