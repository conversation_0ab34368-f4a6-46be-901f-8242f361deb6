import React from "react";
import { useTranslation } from "react-i18next";
import { Device } from "@prisma/client";
import { Box } from "@/components/ui/box";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { useGetSpindelStatistic } from "@/services/SpindelService";
import useCalculateDataForLastHour from "@/hooks/useCalculateDataForLastHour";
import { formatDistance } from "date-fns";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  DropletIcon,
} from "react-native-heroicons/solid";

const formatGravity = (gravity?: number) => {
  if (gravity === undefined || gravity === null) return "- °P";
  return `${gravity.toFixed(1)} °P`;
};

type SpindelDisplayProps = {
  spindle: Device | null | undefined;
  onConnectSpindle: () => void;
};

const SpindelDisplay = ({ spindle, onConnectSpindle }: SpindelDisplayProps) => {
  const { t } = useTranslation();
  const { data: spindleStatistic } = useGetSpindelStatistic(spindle?.id ?? 0);

  const { changeType, changesForLastHour: gravityChangesForLastHour } =
    useCalculateDataForLastHour([spindleStatistic].filter(Boolean), "gravity");

  if (!spindle) {
    return (
      <Card className="p-4 mt-4">
        <VStack space="md" className="items-center">
          <DropletIcon size={32} color="#9ca3af" />
          <Text className="text-gray-600 text-center">
            {t("fermentation.noSpindleConnected")}
          </Text>
          <Button variant="outline" onPress={onConnectSpindle}>
            <ButtonText>{t("fermentation.connectSpindle")}</ButtonText>
          </Button>
        </VStack>
      </Card>
    );
  }

  const getGravityChangeColor = () => {
    return changeType === "decrease" ? "text-green-600" : "text-red-600";
  };

  const getGravityChangeIcon = () => {
    const color = changeType === "decrease" ? "#10b981" : "#ef4444";
    return changeType === "decrease" ? (
      <ArrowDownIcon size={16} color={color} />
    ) : (
      <ArrowUpIcon size={16} color={color} />
    );
  };

  return (
    <Card className="p-4 mt-4">
      <VStack space="md">
        <HStack space="sm" className="items-center">
          <DropletIcon size={20} color="#3b82f6" />
          <Heading size="md">{t("fermentation.spindleData")}</Heading>
        </HStack>

        <VStack space="sm">
          <HStack className="justify-between items-center">
            <Text className="text-gray-600">
              {t("fermentation.currentGravity")}:
            </Text>
            <HStack space="xs" className="items-center">
              <Text className="text-xl font-bold text-gray-800">
                {formatGravity(spindleStatistic?.gravity)}
              </Text>
              {gravityChangesForLastHour && (
                <HStack space="xs" className={`items-center ${getGravityChangeColor()}`}>
                  {getGravityChangeIcon()}
                  <Text className={`text-sm font-semibold ${getGravityChangeColor()}`}>
                    {gravityChangesForLastHour}°P
                  </Text>
                </HStack>
              )}
            </HStack>
          </HStack>

          {spindleStatistic?.temperature && (
            <HStack className="justify-between">
              <Text className="text-gray-600">
                {t("fermentation.spindleTemperature")}:
              </Text>
              <Text className="font-medium">
                {spindleStatistic.temperature.toFixed(1)}°C
              </Text>
            </HStack>
          )}

          {spindleStatistic?.battery && (
            <HStack className="justify-between">
              <Text className="text-gray-600">
                {t("fermentation.battery")}:
              </Text>
              <Text className="font-medium">
                {spindleStatistic.battery.toFixed(0)}%
              </Text>
            </HStack>
          )}

          {spindleStatistic?.createdAt && (
            <Text className="text-xs text-gray-500">
              {t("fermentation.lastUpdate")}:{" "}
              {formatDistance(new Date(spindleStatistic.createdAt), new Date(), {
                addSuffix: true,
              })}
            </Text>
          )}
        </VStack>
      </VStack>
    </Card>
  );
};

export default SpindelDisplay;
