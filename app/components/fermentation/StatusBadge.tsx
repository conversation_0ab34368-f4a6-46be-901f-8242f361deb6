import React from "react";
import { useTranslation } from "react-i18next";
import { Badge, BadgeText } from "@/components/ui/badge";
import { HStack } from "@/components/ui/hstack";
import { DeviceStatus } from "@/hooks/useDeviceStatus";
import { SnowflakeIcon, WifiSlashIcon } from "react-native-heroicons/solid";

type StatusProps = {
  status: DeviceStatus;
  large?: boolean;
};

const StatusBadge = ({ status, large = false }: StatusProps) => {
  const { t } = useTranslation();

  let variant: "success" | "info" | "muted" | "error" = "muted";
  let label: string;
  let icon: React.ReactNode = null;

  switch (status) {
    case "fermentation":
      variant = "success";
      label = t("fermentation.name");
      break;
    case "coldCrash":
      variant = "info";
      label = t("fermentation.coldCrash");
      icon = <SnowflakeIcon size={large ? 16 : 12} color="white" />;
      break;
    case "standby":
      variant = "muted";
      label = t("common.standBy");
      break;
    case "offline":
    default:
      variant = "error";
      label = t("common.offline");
      icon = <WifiSlashIcon size={large ? 16 : 12} color="white" />;
      break;
  }

  const size = large ? "lg" : "sm";

  return (
    <Badge variant={variant} size={size}>
      <HStack space="xs" className="items-center">
        {icon}
        <BadgeText>{label}</BadgeText>
      </HStack>
    </Badge>
  );
};

export default StatusBadge;
