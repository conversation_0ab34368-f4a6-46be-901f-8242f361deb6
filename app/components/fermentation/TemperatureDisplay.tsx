import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Box } from "@/components/ui/box";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Brewtool } from "@/services/BrewtoolService";
import { useTemperatureStatistic } from "@/services/FermentationService";
import useCalculateDataForLastHour from "@/hooks/useCalculateDataForLastHour";
import {
  ArrowUpIcon,
  ArrowDownIcon,
  DocumentTextIcon,
} from "react-native-heroicons/solid";
import { formatDistance } from "date-fns";

const formatTemperature = (temp?: number) => {
  if (temp === undefined || temp === null) return "- °C";
  return `${temp.toFixed(1)} °C`;
};

type TemperatureDisplayProps = {
  brewtool: Brewtool;
};

const TemperatureDisplay = ({ brewtool }: TemperatureDisplayProps) => {
  const { t } = useTranslation();
  const { data } = useTemperatureStatistic(brewtool.id);

  const { changeType, changesForLastHour: temperatureChangesForLastHour } =
    useCalculateDataForLastHour(data, "temperature");

  const shouldBe: "minus" | "plus" | "neutral" = useMemo(() => {
    if (!brewtool.config?.temperature || !brewtool.statistic?.temperature) {
      return "neutral";
    }
    if (brewtool.statistic?.temperature < brewtool.config?.temperature) {
      return "plus";
    } else {
      return "minus";
    }
  }, [brewtool]);

  const getTextColor = () => {
    if (shouldBe === "neutral") {
      return "text-yellow-600";
    } else if (shouldBe === "plus") {
      return changeType === "increase" ? "text-green-600" : "text-red-600";
    } else {
      return changeType === "increase" ? "text-red-600" : "text-green-600";
    }
  };

  const getIconColor = () => {
    if (shouldBe === "neutral") {
      return "#eab308"; // yellow-500
    } else if (shouldBe === "plus") {
      return changeType === "increase" ? "#10b981" : "#ef4444"; // green-500 : red-500
    } else {
      return changeType === "increase" ? "#ef4444" : "#10b981"; // red-500 : green-500
    }
  };

  return (
    <VStack space="md">
      {/* Current temperature */}
      <Card className="p-4 bg-gray-50">
        <VStack space="sm">
          <HStack space="sm" className="items-center">
            <Text className="text-sm font-medium text-gray-500">
              {t("fermentation.currentTemperature")}
            </Text>
          </HStack>
          <HStack className="items-center">
            <Heading size="3xl" className="text-gray-800">
              {formatTemperature(brewtool.statistic?.temperature)}
            </Heading>
            {temperatureChangesForLastHour && (
              <HStack space="xs" className={`ml-2 items-center ${getTextColor()}`}>
                {changeType === "increase" ? (
                  <ArrowUpIcon size={16} color={getIconColor()} />
                ) : changeType === "decrease" ? (
                  <ArrowDownIcon size={16} color={getIconColor()} />
                ) : null}
                <Text className={`text-sm font-semibold ${getTextColor()}`}>
                  {temperatureChangesForLastHour}°C
                </Text>
              </HStack>
            )}
          </HStack>
        </VStack>
      </Card>

      {/* Target temperature */}
      {brewtool.config?.temperature && (
        <Card className="p-4 bg-gray-50">
          <VStack space="sm">
            <Text className="text-sm font-medium text-gray-500">
              {t("fermentation.targetTemperature")}
            </Text>
            <Heading size="2xl" className="text-gray-700">
              {formatTemperature(brewtool.config?.temperature)}
            </Heading>
            {brewtool.config?.startDate && (
              <Text className="text-sm text-gray-500">
                {formatDistance(new Date(brewtool.config?.startDate), new Date(), {
                  addSuffix: true,
                })}
              </Text>
            )}
          </VStack>
        </Card>
      )}

      {/* Recipe name */}
      {brewtool.config?.recipeName && (
        <Card className="p-4 bg-gray-50">
          <VStack space="sm">
            <HStack space="sm" className="items-center">
              <DocumentTextIcon size={20} color="#f97316" />
              <Text className="text-sm font-medium text-gray-500">
                {t("fermentation.recipeName")}
              </Text>
            </HStack>
            <Heading size="xl" className="text-gray-700">
              {brewtool.config.recipeName}
            </Heading>
          </VStack>
        </Card>
      )}
    </VStack>
  );
};

export default TemperatureDisplay;
