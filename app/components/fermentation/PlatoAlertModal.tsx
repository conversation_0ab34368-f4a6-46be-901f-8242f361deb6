import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
} from "@/components/ui/modal";
import { Button, ButtonText } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { XMarkIcon } from "react-native-heroicons/solid";

type PlatoAlertModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (threshold: number) => void;
  currentPlato?: number;
};

const PlatoAlertModal = ({
  isOpen,
  onClose,
  onSubmit,
  currentPlato,
}: PlatoAlertModalProps) => {
  const { t } = useTranslation();
  const [threshold, setThreshold] = useState(
    currentPlato ? (currentPlato - 2).toString() : "10"
  );

  const handleSubmit = () => {
    const thresholdValue = parseFloat(threshold);
    if (!isNaN(thresholdValue)) {
      onSubmit(thresholdValue);
    }
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent>
        <ModalHeader>
          <Heading size="lg">{t("fermentation.setPlatoAlert")}</Heading>
          <ModalCloseButton>
            <XMarkIcon size={20} color="#6b7280" />
          </ModalCloseButton>
        </ModalHeader>

        <ModalBody>
          <VStack space="lg">
            <Text className="text-gray-600">
              {t("fermentation.platoAlertDescription")}
            </Text>

            {currentPlato && (
              <Text className="text-sm text-gray-500">
                {t("fermentation.currentPlato")}: {currentPlato}°P
              </Text>
            )}

            <VStack space="sm">
              <Text className="font-medium text-gray-700">
                {t("fermentation.alertThreshold")} (°P)
              </Text>
              <Input>
                <InputField
                  placeholder="10.0"
                  value={threshold}
                  onChangeText={setThreshold}
                  keyboardType="numeric"
                />
              </Input>
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack space="md" className="w-full">
            <Button variant="outline" className="flex-1" onPress={onClose}>
              <ButtonText>{t("common.cancel")}</ButtonText>
            </Button>
            <Button className="flex-1 bg-blue-600" onPress={handleSubmit}>
              <ButtonText>{t("fermentation.setAlert")}</ButtonText>
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PlatoAlertModal;
