import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { FlatList } from "react-native";
import { Box } from "@/components/ui/box";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Button, ButtonText } from "@/components/ui/button";
import { Device } from "@prisma/client";
import { useTemperatureStatistic } from "@/services/FermentationService";
import { useGetSpindelStatistics } from "@/services/SpindelService";
import { formatDistance } from "date-fns";
import { BarChart3Icon, TableIcon } from "react-native-heroicons/solid";

type ViewMode = "graph" | "table";

type DataEntry = {
  id: number;
  createdAt: string | Date;
  temperature?: number;
  gravity?: number;
  [key: string]: string | number | Date | undefined;
};

const formatTemperature = (temp?: number) => {
  if (temp === undefined || temp === null) return "- °C";
  return `${temp.toFixed(1)} °C`;
};

const formatGravity = (gravity?: number) => {
  if (gravity === undefined || gravity === null) return "- °P";
  return `${gravity.toFixed(1)} °P`;
};

type StatisticsSectionProps = {
  device: Device;
  spindle?: Device;
};

const StatisticsSection = ({ device, spindle }: StatisticsSectionProps) => {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<ViewMode>("table");

  const { data: temperatureData, isLoading } = useTemperatureStatistic(device.id);
  const { data: spindleData } = useGetSpindelStatistics(spindle?.id ?? 0);

  // Combine and format data for display
  const chartData = useMemo(() => {
    const tempData = temperatureData?.slice(-20) || []; // Last 20 entries
    const spindleEntries = spindleData?.slice(-20) || [];

    // Merge temperature and spindle data by timestamp
    const mergedData: DataEntry[] = [];
    
    tempData.forEach((temp) => {
      const entry: DataEntry = {
        id: temp.id,
        createdAt: temp.createdAt,
        temperature: temp.temperature,
      };
      
      // Find matching spindle data
      const matchingSpindle = spindleEntries.find((s) => {
        const tempTime = new Date(temp.createdAt).getTime();
        const spindleTime = new Date(s.createdAt).getTime();
        return Math.abs(tempTime - spindleTime) < 5 * 60 * 1000; // Within 5 minutes
      });
      
      if (matchingSpindle) {
        entry.gravity = matchingSpindle.gravity;
      }
      
      mergedData.push(entry);
    });

    return mergedData.reverse(); // Most recent first
  }, [temperatureData, spindleData]);

  const renderTableRow = ({ item }: { item: DataEntry }) => (
    <Card className="p-3 m-1">
      <VStack space="xs">
        <Text className="text-xs text-gray-500">
          {formatDistance(new Date(item.createdAt), new Date(), {
            addSuffix: true,
          })}
        </Text>
        <HStack className="justify-between">
          <Text className="font-medium">
            {formatTemperature(item.temperature)}
          </Text>
          {item.gravity && (
            <Text className="font-medium text-blue-600">
              {formatGravity(item.gravity)}
            </Text>
          )}
        </HStack>
      </VStack>
    </Card>
  );

  const renderGraphPlaceholder = () => (
    <Card className="p-6">
      <VStack space="md" className="items-center">
        <BarChart3Icon size={48} color="#9ca3af" />
        <Text className="text-gray-500 text-center">
          {t("fermentation.chartPlaceholder")}
        </Text>
        <Text className="text-xs text-gray-400 text-center">
          {t("fermentation.chartComingSoon")}
        </Text>
      </VStack>
    </Card>
  );

  if (isLoading) {
    return (
      <Card className="p-4">
        <Text className="text-gray-500">{t("common.loading")}</Text>
      </Card>
    );
  }

  return (
    <VStack space="md" className="mt-4 pt-4 border-t border-gray-200">
      <HStack className="justify-between items-center">
        <HStack space="sm" className="items-center">
          <BarChart3Icon size={18} color="#374151" />
          <Heading size="md">{t("fermentation.statistics")}</Heading>
        </HStack>
        
        <HStack space="xs">
          <Button
            size="sm"
            variant={viewMode === "graph" ? "solid" : "outline"}
            onPress={() => setViewMode("graph")}
          >
            <HStack space="xs" className="items-center">
              <BarChart3Icon size={14} color={viewMode === "graph" ? "white" : "#6b7280"} />
              <ButtonText>{t("common.graph")}</ButtonText>
            </HStack>
          </Button>
          
          <Button
            size="sm"
            variant={viewMode === "table" ? "solid" : "outline"}
            onPress={() => setViewMode("table")}
          >
            <HStack space="xs" className="items-center">
              <TableIcon size={14} color={viewMode === "table" ? "white" : "#6b7280"} />
              <ButtonText>{t("common.table")}</ButtonText>
            </HStack>
          </Button>
        </HStack>
      </HStack>

      {viewMode === "graph" ? (
        renderGraphPlaceholder()
      ) : (
        <Box>
          {chartData.length === 0 ? (
            <Card className="p-4">
              <Text className="text-gray-500 text-center">
                {t("fermentation.noDataAvailable")}
              </Text>
            </Card>
          ) : (
            <FlatList
              data={chartData}
              keyExtractor={(item) => item.id.toString()}
              renderItem={renderTableRow}
              showsVerticalScrollIndicator={false}
              style={{ maxHeight: 300 }}
            />
          )}
        </Box>
      )}
    </VStack>
  );
};

export default StatisticsSection;
