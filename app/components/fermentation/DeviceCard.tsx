import React from "react";
import { useTranslation } from "react-i18next";
import { Pressable } from "react-native";
import { router } from "expo-router";
import { Box } from "@/components/ui/box";
import { Card } from "@/components/ui/card";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import { Brewtool } from "@/services/BrewtoolService";
import useDeviceStatus from "@/hooks/useDeviceStatus";
import StatusBadge from "./StatusBadge";

const formatTemperature = (temp?: number) => {
  if (temp === undefined || temp === null) return "- °C";
  return `${temp.toFixed(1)} °C`;
};

type DeviceCardProps = {
  device: Brewtool;
};

const DeviceCard = ({ device }: DeviceCardProps) => {
  const { t } = useTranslation();
  const status = useDeviceStatus(device);

  const handlePress = () => {
    router.push(`/fermentation/device/${device.id}`);
  };

  return (
    <Pressable onPress={handlePress}>
      <Card className="p-4 m-2">
        <VStack space="md">
          <HStack className="justify-between items-start">
            <Heading size="md" className="flex-1" numberOfLines={1}>
              {device.name || t("fermentation.unnamedDevice")}
            </Heading>
            <StatusBadge status={status} />
          </HStack>

          <VStack space="sm">
            <HStack className="justify-between">
              <Text className="text-gray-600">
                {t("fermentation.currentTemperature")}:
              </Text>
              <Text className="font-medium">
                {formatTemperature(device.statistic?.temperature)}
              </Text>
            </HStack>

            {device.config?.temperature && (
              <HStack className="justify-between">
                <Text className="text-gray-600">
                  {t("fermentation.targetTemperature")}:
                </Text>
                <Text>{formatTemperature(device.config.temperature)}</Text>
              </HStack>
            )}

            {device.plato && (
              <HStack className="justify-between">
                <Text className="text-gray-600">
                  {t("fermentation.plato")}:
                </Text>
                <Text>{device.plato}°P</Text>
              </HStack>
            )}
          </VStack>
        </VStack>
      </Card>
    </Pressable>
  );
};

export default DeviceCard;
