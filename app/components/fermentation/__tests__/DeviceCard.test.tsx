import React from 'react';
import { render } from '@testing-library/react-native';
import DeviceCard from '../DeviceCard';
import { Brewtool } from '@/services/BrewtoolService';

// Mock the router
jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
  },
}));

// Mock the translation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

// Mock the device status hook
jest.mock('@/hooks/useDeviceStatus', () => ({
  __esModule: true,
  default: () => 'fermentation',
}));

const mockDevice: Brewtool = {
  id: 1,
  name: 'Test Brewtool',
  type: 'BREWTOOL',
  statistic: {
    id: 1,
    temperature: 20.5,
    createdAt: new Date(),
    updatedAt: new Date(),
    deviceId: 1,
  },
  config: {
    id: 1,
    temperature: 19.0,
    createdAt: new Date(),
    updatedAt: new Date(),
    deviceId: 1,
    recipeName: 'Test Recipe',
    sendPush: false,
    startDate: new Date(),
    endDate: null,
  },
  deviceConnections: [],
  plato: 12.5,
  createdAt: new Date(),
  updatedAt: new Date(),
  userId: 1,
  operator: 'BREWTOOL',
  mqttClientId: null,
  mqttUsername: null,
  mqttPassword: null,
  tuyaDeviceId: null,
  online: true,
};

describe('DeviceCard', () => {
  it('renders device information correctly', () => {
    const { getByText } = render(<DeviceCard device={mockDevice} />);
    
    expect(getByText('Test Brewtool')).toBeTruthy();
    expect(getByText('20.5 °C')).toBeTruthy();
    expect(getByText('19.0 °C')).toBeTruthy();
    expect(getByText('12.5°P')).toBeTruthy();
  });

  it('shows unnamed device when name is empty', () => {
    const deviceWithoutName = { ...mockDevice, name: '' };
    const { getByText } = render(<DeviceCard device={deviceWithoutName} />);
    
    expect(getByText('fermentation.unnamedDevice')).toBeTruthy();
  });

  it('handles missing statistics gracefully', () => {
    const deviceWithoutStats = { ...mockDevice, statistic: undefined };
    const { getByText } = render(<DeviceCard device={deviceWithoutStats} />);
    
    expect(getByText('- °C')).toBeTruthy();
  });
});
