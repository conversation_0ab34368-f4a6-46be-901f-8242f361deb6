import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Platform, Alert } from "react-native";
import * as AppleAuthentication from "expo-apple-authentication";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Divider } from "@/components/ui/divider";
import { useAuth } from "@/contexts/AuthContext";

interface SocialAuthButtonsProps {
  mode: "login" | "register";
}

const SocialAuthButtons: React.FC<SocialAuthButtonsProps> = ({ mode }) => {
  const { t } = useTranslation();
  const { loginWithApple, isLoading } = useAuth();
  const [isAppleAvailable, setIsAppleAvailable] = useState(false);

  useEffect(() => {
    checkAppleAvailability();
  }, []);

  const checkAppleAvailability = async () => {
    if (Platform.OS === "ios") {
      try {
        const isAvailable = await AppleAuthentication.isAvailableAsync();
        setIsAppleAvailable(isAvailable);
      } catch (error) {
        console.error("Error checking Apple authentication availability:", error);
      }
    }
  };

  const handleAppleAuth = async () => {
    try {
      await loginWithApple();
    } catch (error) {
      if (error instanceof Error && error.message.includes("canceled")) {
        // User canceled, don't show error
        return;
      }
      Alert.alert(
        t("auth.appleAuthFailed"),
        error instanceof Error ? error.message : t("auth.appleAuthError")
      );
    }
  };

  const showSocialButtons = isAppleAvailable;

  if (!showSocialButtons) {
    return null;
  }

  return (
    <VStack space="lg">
      <HStack className="items-center" space="md">
        <Divider className="flex-1" />
        <Text className="text-gray-500 text-sm">
          {mode === "login" ? t("auth.orSignInWith") : t("auth.orSignUpWith")}
        </Text>
        <Divider className="flex-1" />
      </HStack>

      <VStack space="md">
        {isAppleAvailable && (
          <AppleAuthentication.AppleAuthenticationButton
            buttonType={
              mode === "login"
                ? AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN
                : AppleAuthentication.AppleAuthenticationButtonType.SIGN_UP
            }
            buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
            cornerRadius={8}
            style={{
              width: "100%",
              height: 50,
            }}
            onPress={handleAppleAuth}
          />
        )}
      </VStack>
    </VStack>
  );
};

export default SocialAuthButtons;
