import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert } from "react-native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { useAuth } from "@/contexts/AuthContext";
import { EyeIcon, EyeSlashIcon } from "react-native-heroicons/solid";

interface LoginFormProps {
  onSwitchToRegister: () => void;
  onForgotPassword: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSwitchToRegister,
  onForgotPassword,
}) => {
  const { t } = useTranslation();
  const { login, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = t("auth.emailRequired");
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t("auth.emailInvalid");
    }

    if (!password.trim()) {
      newErrors.password = t("auth.passwordRequired");
    } else if (password.length < 6) {
      newErrors.password = t("auth.passwordTooShort");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      await login({ email: email.trim(), password });
    } catch (error) {
      Alert.alert(
        t("auth.loginFailed"),
        error instanceof Error ? error.message : t("auth.loginError")
      );
    }
  };

  return (
    <VStack space="lg" className="w-full">
      <Box className="text-center">
        <Heading size="2xl" className="text-gray-800 mb-2">
          {t("auth.welcomeBack")}
        </Heading>
        <Text className="text-gray-600">
          {t("auth.signInToYourAccount")}
        </Text>
      </Box>

      <VStack space="md">
        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.email")}
          </Text>
          <Input
            variant={errors.email ? "error" : "outline"}
          >
            <InputField
              placeholder={t("auth.emailPlaceholder")}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </Input>
          {errors.email && (
            <Text className="text-red-500 text-sm">{errors.email}</Text>
          )}
        </VStack>

        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.password")}
          </Text>
          <Input
            variant={errors.password ? "error" : "outline"}
          >
            <InputField
              placeholder={t("auth.passwordPlaceholder")}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <Button
              variant="ghost"
              size="sm"
              onPress={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              {showPassword ? (
                <EyeSlashIcon size={20} color="#6b7280" />
              ) : (
                <EyeIcon size={20} color="#6b7280" />
              )}
            </Button>
          </Input>
          {errors.password && (
            <Text className="text-red-500 text-sm">{errors.password}</Text>
          )}
        </VStack>

        <Button
          variant="link"
          size="sm"
          onPress={onForgotPassword}
          className="self-end"
        >
          <ButtonText className="text-blue-600">
            {t("auth.forgotPassword")}
          </ButtonText>
        </Button>
      </VStack>

      <VStack space="md">
        <Button
          size="lg"
          onPress={handleLogin}
          disabled={isLoading}
          className="bg-blue-600"
        >
          <ButtonText>
            {isLoading ? t("auth.signingIn") : t("auth.signIn")}
          </ButtonText>
        </Button>

        <Box className="flex-row justify-center">
          <Text className="text-gray-600">
            {t("auth.dontHaveAccount")}{" "}
          </Text>
          <Button variant="link" size="sm" onPress={onSwitchToRegister}>
            <ButtonText className="text-blue-600">
              {t("auth.signUp")}
            </ButtonText>
          </Button>
        </Box>
      </VStack>
    </VStack>
  );
};

export default LoginForm;
