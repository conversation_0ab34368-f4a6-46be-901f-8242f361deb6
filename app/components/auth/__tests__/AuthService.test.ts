import AuthService from '@/services/AuthService';
import { TokenStorage, UserStorage } from '@/helpers/secureStorage';

// Mock the secure storage
jest.mock('@/helpers/secureStorage', () => ({
  TokenStorage: {
    setTokens: jest.fn(),
    getTokens: jest.fn(),
    clearTokens: jest.fn(),
  },
  UserStorage: {
    setUser: jest.fn(),
    getUser: jest.fn(),
    clearUser: jest.fn(),
  },
}));

// Mock the HTTP helper
jest.mock('@/helpers/http', () => ({
  post: jest.fn(),
  get: jest.fn(),
}));

// Mock Apple Authentication
jest.mock('expo-apple-authentication', () => ({
  signInAsync: jest.fn(),
  AppleAuthenticationScope: {
    FULL_NAME: 'fullName',
    EMAIL: 'email',
  },
}));

// Mock WebAuthn
jest.mock('@simplewebauthn/browser', () => ({
  startRegistration: jest.fn(),
  startAuthentication: jest.fn(),
}));

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Token Management', () => {
    it('should get tokens from storage', async () => {
      const mockTokens = { accessToken: 'access', refreshToken: 'refresh' };
      (TokenStorage.getTokens as jest.Mock).mockResolvedValue(mockTokens);

      const tokens = await AuthService.getTokens();
      expect(tokens).toEqual(mockTokens);
      expect(TokenStorage.getTokens).toHaveBeenCalled();
    });

    it('should set tokens in storage', async () => {
      const mockTokens = { accessToken: 'access', refreshToken: 'refresh' };
      
      await AuthService.setTokens(mockTokens);
      expect(TokenStorage.setTokens).toHaveBeenCalledWith(mockTokens);
    });

    it('should clear tokens from storage', async () => {
      await AuthService.clearTokens();
      expect(TokenStorage.clearTokens).toHaveBeenCalled();
      expect(UserStorage.clearUser).toHaveBeenCalled();
    });
  });

  describe('User Management', () => {
    it('should get user from storage', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      (UserStorage.getUser as jest.Mock).mockResolvedValue(mockUser);

      const user = await AuthService.getUser();
      expect(user).toEqual(mockUser);
      expect(UserStorage.getUser).toHaveBeenCalled();
    });

    it('should set user in storage', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' };
      
      await AuthService.setUser(mockUser);
      expect(UserStorage.setUser).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('Authentication Status', () => {
    it('should return true when tokens exist', async () => {
      const mockTokens = { accessToken: 'access', refreshToken: 'refresh' };
      (TokenStorage.getTokens as jest.Mock).mockResolvedValue(mockTokens);

      const isAuthenticated = await AuthService.isAuthenticated();
      expect(isAuthenticated).toBe(true);
    });

    it('should return false when no tokens exist', async () => {
      (TokenStorage.getTokens as jest.Mock).mockResolvedValue(null);

      const isAuthenticated = await AuthService.isAuthenticated();
      expect(isAuthenticated).toBe(false);
    });
  });

  describe('WebAuthn Availability', () => {
    it('should check WebAuthn availability', async () => {
      // Mock window.PublicKeyCredential
      Object.defineProperty(window, 'PublicKeyCredential', {
        value: function() {},
        writable: true,
      });

      const isAvailable = await AuthService.isWebAuthnAvailable();
      expect(typeof isAvailable).toBe('boolean');
    });
  });

  describe('Logout', () => {
    it('should clear tokens and user on logout', async () => {
      const mockTokens = { accessToken: 'access', refreshToken: 'refresh' };
      (TokenStorage.getTokens as jest.Mock).mockResolvedValue(mockTokens);

      await AuthService.logout();
      
      expect(TokenStorage.clearTokens).toHaveBeenCalled();
      expect(UserStorage.clearUser).toHaveBeenCalled();
    });
  });
});
