import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Alert } from "react-native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Divider } from "@/components/ui/divider";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
} from "@/components/ui/modal";
import { useAuth } from "@/contexts/AuthContext";
import { FingerPrintIcon, XMarkIcon } from "react-native-heroicons/solid";

interface WebAuthnButtonProps {
  mode: "login" | "register";
}

const WebAuthnButton: React.FC<WebAuthnButtonProps> = ({ mode }) => {
  const { t } = useTranslation();
  const { loginWithWebAuthn, registerWithWebAuthn, isLoading, isWebAuthnAvailable } = useAuth();
  const [isAvailable, setIsAvailable] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");

  useEffect(() => {
    checkWebAuthnAvailability();
  }, []);

  const checkWebAuthnAvailability = async () => {
    try {
      const available = await isWebAuthnAvailable();
      setIsAvailable(available);
    } catch (error) {
      console.error("Error checking WebAuthn availability:", error);
    }
  };

  const validateEmail = (email: string) => {
    if (!email.trim()) {
      return t("auth.emailRequired");
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      return t("auth.emailInvalid");
    }
    return "";
  };

  const handleWebAuthn = async () => {
    const error = validateEmail(email);
    if (error) {
      setEmailError(error);
      return;
    }

    try {
      if (mode === "login") {
        await loginWithWebAuthn(email.trim());
      } else {
        await registerWithWebAuthn(email.trim());
      }
      setShowModal(false);
      setEmail("");
      setEmailError("");
    } catch (error) {
      Alert.alert(
        mode === "login" ? t("auth.webauthnLoginFailed") : t("auth.webauthnRegisterFailed"),
        error instanceof Error ? error.message : t("auth.webauthnError")
      );
    }
  };

  const openModal = () => {
    setShowModal(true);
    setEmail("");
    setEmailError("");
  };

  const closeModal = () => {
    setShowModal(false);
    setEmail("");
    setEmailError("");
  };

  if (!isAvailable) {
    return null;
  }

  return (
    <>
      <VStack space="lg">
        <HStack className="items-center" space="md">
          <Divider className="flex-1" />
          <Text className="text-gray-500 text-sm">
            {t("auth.orUsePasskey")}
          </Text>
          <Divider className="flex-1" />
        </HStack>

        <Button
          variant="outline"
          size="lg"
          onPress={openModal}
          disabled={isLoading}
        >
          <HStack space="sm" className="items-center">
            <FingerPrintIcon size={20} color="#6b7280" />
            <ButtonText>
              {mode === "login" ? t("auth.signInWithPasskey") : t("auth.signUpWithPasskey")}
            </ButtonText>
          </HStack>
        </Button>
      </VStack>

      <Modal isOpen={showModal} onClose={closeModal}>
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <Text className="text-lg font-semibold">
              {mode === "login" ? t("auth.signInWithPasskey") : t("auth.signUpWithPasskey")}
            </Text>
            <ModalCloseButton>
              <XMarkIcon size={20} color="#6b7280" />
            </ModalCloseButton>
          </ModalHeader>

          <ModalBody>
            <VStack space="lg">
              <Text className="text-gray-600">
                {mode === "login" 
                  ? t("auth.webauthnLoginDescription")
                  : t("auth.webauthnRegisterDescription")
                }
              </Text>

              <VStack space="sm">
                <Text className="font-medium text-gray-700">
                  {t("auth.email")}
                </Text>
                <Input variant={emailError ? "error" : "outline"}>
                  <InputField
                    placeholder={t("auth.emailPlaceholder")}
                    value={email}
                    onChangeText={(text) => {
                      setEmail(text);
                      if (emailError) setEmailError("");
                    }}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </Input>
                {emailError && (
                  <Text className="text-red-500 text-sm">{emailError}</Text>
                )}
              </VStack>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <HStack space="md" className="w-full">
              <Button variant="outline" className="flex-1" onPress={closeModal}>
                <ButtonText>{t("common.cancel")}</ButtonText>
              </Button>
              <Button
                className="flex-1 bg-blue-600"
                onPress={handleWebAuthn}
                disabled={isLoading}
              >
                <ButtonText>
                  {isLoading 
                    ? t("auth.authenticating") 
                    : mode === "login" 
                      ? t("auth.signIn") 
                      : t("auth.signUp")
                  }
                </ButtonText>
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default WebAuthnButton;
