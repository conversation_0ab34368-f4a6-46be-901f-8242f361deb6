import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert } from "react-native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Input, InputField } from "@/components/ui/input";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Heading } from "@/components/ui/heading";
import { useAuth } from "@/contexts/AuthContext";
import { EyeIcon, EyeSlashIcon } from "react-native-heroicons/solid";

interface RegisterFormProps {
  onSwitchToLogin: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSwitchToLogin }) => {
  const { t } = useTranslation();
  const { register, isLoading } = useAuth();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<{
    name?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  const validateForm = () => {
    const newErrors: {
      name?: string;
      email?: string;
      password?: string;
      confirmPassword?: string;
    } = {};

    if (!name.trim()) {
      newErrors.name = t("auth.nameRequired");
    }

    if (!email.trim()) {
      newErrors.email = t("auth.emailRequired");
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t("auth.emailInvalid");
    }

    if (!password.trim()) {
      newErrors.password = t("auth.passwordRequired");
    } else if (password.length < 6) {
      newErrors.password = t("auth.passwordTooShort");
    }

    if (!confirmPassword.trim()) {
      newErrors.confirmPassword = t("auth.confirmPasswordRequired");
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = t("auth.passwordsDoNotMatch");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    try {
      await register({
        name: name.trim(),
        email: email.trim(),
        password,
      });
    } catch (error) {
      Alert.alert(
        t("auth.registrationFailed"),
        error instanceof Error ? error.message : t("auth.registrationError")
      );
    }
  };

  return (
    <VStack space="lg" className="w-full">
      <Box className="text-center">
        <Heading size="2xl" className="text-gray-800 mb-2">
          {t("auth.createAccount")}
        </Heading>
        <Text className="text-gray-600">
          {t("auth.signUpToGetStarted")}
        </Text>
      </Box>

      <VStack space="md">
        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.name")}
          </Text>
          <Input variant={errors.name ? "error" : "outline"}>
            <InputField
              placeholder={t("auth.namePlaceholder")}
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
              autoCorrect={false}
            />
          </Input>
          {errors.name && (
            <Text className="text-red-500 text-sm">{errors.name}</Text>
          )}
        </VStack>

        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.email")}
          </Text>
          <Input variant={errors.email ? "error" : "outline"}>
            <InputField
              placeholder={t("auth.emailPlaceholder")}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </Input>
          {errors.email && (
            <Text className="text-red-500 text-sm">{errors.email}</Text>
          )}
        </VStack>

        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.password")}
          </Text>
          <Input variant={errors.password ? "error" : "outline"}>
            <InputField
              placeholder={t("auth.passwordPlaceholder")}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
            />
            <Button
              variant="ghost"
              size="sm"
              onPress={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              {showPassword ? (
                <EyeSlashIcon size={20} color="#6b7280" />
              ) : (
                <EyeIcon size={20} color="#6b7280" />
              )}
            </Button>
          </Input>
          {errors.password && (
            <Text className="text-red-500 text-sm">{errors.password}</Text>
          )}
        </VStack>

        <VStack space="sm">
          <Text className="font-medium text-gray-700">
            {t("auth.confirmPassword")}
          </Text>
          <Input variant={errors.confirmPassword ? "error" : "outline"}>
            <InputField
              placeholder={t("auth.confirmPasswordPlaceholder")}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
            />
            <Button
              variant="ghost"
              size="sm"
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              {showConfirmPassword ? (
                <EyeSlashIcon size={20} color="#6b7280" />
              ) : (
                <EyeIcon size={20} color="#6b7280" />
              )}
            </Button>
          </Input>
          {errors.confirmPassword && (
            <Text className="text-red-500 text-sm">{errors.confirmPassword}</Text>
          )}
        </VStack>
      </VStack>

      <VStack space="md">
        <Button
          size="lg"
          onPress={handleRegister}
          disabled={isLoading}
          className="bg-blue-600"
        >
          <ButtonText>
            {isLoading ? t("auth.creatingAccount") : t("auth.createAccount")}
          </ButtonText>
        </Button>

        <Box className="flex-row justify-center">
          <Text className="text-gray-600">
            {t("auth.alreadyHaveAccount")}{" "}
          </Text>
          <Button variant="link" size="sm" onPress={onSwitchToLogin}>
            <ButtonText className="text-blue-600">
              {t("auth.signIn")}
            </ButtonText>
          </Button>
        </Box>
      </VStack>
    </VStack>
  );
};

export default RegisterForm;
