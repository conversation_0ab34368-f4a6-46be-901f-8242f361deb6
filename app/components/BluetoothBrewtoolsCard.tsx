import { Box } from "@/components/ui/box";
import { ButtonText, Button, ButtonIcon } from "@/components/ui/button";
import { Center } from "@/components/ui/center";
import { Heading } from "@/components/ui/heading";
import {
  AlertCircleIcon,
  CloseCircleIcon,
  Icon,
  RepeatIcon,
} from "@/components/ui/icon";
import { Spinner } from "@/components/ui/spinner";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { getBleError, scanForBrewtools } from "@/helpers/BLE";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Device } from "react-native-ble-plx";
import { Link } from "expo-router";
import { Divider } from "@/components/ui/divider";
import { HStack } from "./ui/hstack";
import { WifiIcon } from "react-native-heroicons/solid";

export default function BluetoothBrewtoolsCard() {
  const { t } = useTranslation();

  const [brewtools, setBrewtools] = useState<Device[] | null>(null);
  const [bleError, setBleError] = useState<string | null>(null);

  const refreshBleDevices = async () => {
    try {
      const brewtools = await scanForBrewtools();

      const error = getBleError();
      if (error) {
        if (error?.message === "BluetoothLE is unsupported on this device") {
          setBleError(t("bleUnsupported"));
        } else {
          setBleError(error?.message);
        }
      } else {
        setBleError(null);
        setBrewtools(brewtools);
      }
    } catch (error) {
      console.error("Brewtools BLE", error);
    }
  };

  useEffect(() => {
    refreshBleDevices();
  }, []);

  return (
    <>
      <Heading size="xl">{t("brewtools")}</Heading>
      {bleError !== null ? (
        <Center>
          <VStack>
            <Center>
              <Icon
                as={AlertCircleIcon}
                className="color-red-600 m-2 w-10 h-10"
              />
              <Text>{t("searchingForBrewtools")}</Text>
            </Center>
          </VStack>
        </Center>
      ) : brewtools === null ? (
        <Center>
          <VStack>
            <Center>
              <Spinner />
              <Text>{t("searchingForBrewtools")}</Text>
            </Center>
          </VStack>
        </Center>
      ) : brewtools.length === 0 ? (
        <Center>
          <VStack>
            <Center>
              <Icon
                as={CloseCircleIcon}
                className="text-typography-500 m-2 w-10 h-10"
              />
              <Text>{t("notFoundDevices")}</Text>
            </Center>
          </VStack>
        </Center>
      ) : (
        brewtools.map((brewtool) => (
          <Box key={brewtool.id}>
            <HStack space="md">
              <VStack className="flex flex-grow w-max" space="md">
                <Heading>{brewtool.name}</Heading>
                <Text>{brewtool.id}</Text>
              </VStack>
              <Center>
                <Link href={"/device/" + brewtool.id} asChild>
                  <Button size="md" variant="solid" action="primary">
                    <ButtonIcon as={WifiIcon} />
                  </Button>
                </Link>
              </Center>
            </HStack>
            <Divider className="my-0.5 mt-3" />
          </Box>
        ))
      )}
      <VStack className="mt-4 p-4" space="xs">
        <Button
          size="md"
          variant="solid"
          action="primary"
          onPress={(e) => refreshBleDevices()}
        >
          <ButtonIcon as={RepeatIcon} className="mr-2" />
          <ButtonText>{t("refreshBleDevices")}</ButtonText>
        </Button>
      </VStack>
    </>
  );
}
