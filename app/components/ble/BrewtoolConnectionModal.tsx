import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView,
  Switch,
} from "react-native";
import { useTranslation } from "react-i18next";
import BLEService, { DiscoveredDevice } from "@/services/BLEService";

interface BrewtoolConnectionModalProps {
  visible: boolean;
  device: DiscoveredDevice | null;
  onClose: () => void;
  onConnect: (device: DiscoveredDevice, config?: BrewtoolConfig) => void;
  onSave: (device: DiscoveredDevice, config: BrewtoolConfig) => void;
}

interface BrewtoolConfig {
  name: string;
  wifiSSID: string;
  wifiPassword: string;
  enableWifi: boolean;
  autoConnect: boolean;
  temperatureUnit: "celsius" | "fahrenheit";
  updateInterval: number;
}

interface ConnectionStatus {
  isConnected: boolean;
  hasInternet: boolean;
  signalStrength: number;
  ipAddress: string | null;
  firmwareVersion: string | null;
}

export default function BrewtoolConnectionModal({
  visible,
  device,
  onClose,
  onConnect,
  onSave,
}: BrewtoolConnectionModalProps) {
  const { t } = useTranslation();
  const [isConnecting, setIsConnecting] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] =
    useState<ConnectionStatus | null>(null);
  const [config, setConfig] = useState<BrewtoolConfig>({
    name: "",
    wifiSSID: "",
    wifiPassword: "",
    enableWifi: true,
    autoConnect: true,
    temperatureUnit: "celsius",
    updateInterval: 30,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [availableNetworks, setAvailableNetworks] = useState<string[]>([]);
  const [isScanningWifi, setIsScanningWifi] = useState(false);

  // Initialize config when device changes
  useEffect(() => {
    if (device) {
      setConfig((prev) => ({
        ...prev,
        name: device.name || device.localName || t("ble.unknownBrewtool"),
      }));
    }
  }, [device, t]);

  // Test internet connection
  const testInternetConnection = async () => {
    if (!device) return;

    setIsTestingConnection(true);
    try {
      // First, ensure we're connected to the device
      if (!BLEService.isDeviceConnected(device.id)) {
        await BLEService.connectToDevice(device.id);
      }

      // Read WiFi status from Brewtool via BLE
      const wifiStatus = await BLEService.readBrewtoolWifiStatus(device.id);

      // Read device ID for additional info
      let deviceMac = "";
      try {
        deviceMac = await BLEService.readBrewtoolDeviceId(device.id);
      } catch (error) {
        console.log("Could not read device ID:", error);
      }

      const status: ConnectionStatus = {
        isConnected: true,
        hasInternet: wifiStatus.status,
        signalStrength: device.rssi,
        ipAddress: wifiStatus.status ? "Connected" : null,
        firmwareVersion: deviceMac || "Unknown",
      };

      setConnectionStatus(status);

      // Update config with current WiFi SSID if available
      if (wifiStatus.ssid) {
        setConfig((prev) => ({ ...prev, wifiSSID: wifiStatus.ssid }));
      }

      if (!status.hasInternet) {
        Alert.alert(
          t("ble.noInternetConnection"),
          t("ble.noInternetConnectionMessage"),
          [{ text: t("common.ok") }]
        );
      }
    } catch (error) {
      console.error("Connection test failed:", error);
      Alert.alert(
        t("ble.connectionTestFailed"),
        error instanceof Error
          ? error.message
          : t("ble.connectionTestFailedMessage"),
        [{ text: t("common.ok") }]
      );
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Scan for WiFi networks
  const scanWifiNetworks = async () => {
    if (!device) return;

    setIsScanningWifi(true);
    try {
      // Ensure we're connected to the device
      if (!BLEService.isDeviceConnected(device.id)) {
        await BLEService.connectToDevice(device.id);
      }

      // Scan for WiFi networks via BLE
      const networks = await BLEService.scanBrewtoolWifiNetworks(device.id);
      setAvailableNetworks(networks);
    } catch (error) {
      console.error("WiFi scan failed:", error);
      Alert.alert(
        t("ble.wifiScanFailed"),
        error instanceof Error ? error.message : t("ble.unknownError"),
        [{ text: t("common.ok") }]
      );
    } finally {
      setIsScanningWifi(false);
    }
  };

  // Handle connect
  const handleConnect = async () => {
    if (!device) return;

    setIsConnecting(true);
    try {
      await onConnect(device, config);
      onClose();
    } catch (error) {
      Alert.alert(
        t("ble.connectionError"),
        error instanceof Error ? error.message : t("ble.unknownError")
      );
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle save configuration
  const handleSave = async () => {
    if (!device) return;

    if (!config.name.trim()) {
      Alert.alert(t("ble.validationError"), t("ble.nameRequired"));
      return;
    }

    if (config.enableWifi && !config.wifiSSID.trim()) {
      Alert.alert(t("ble.validationError"), t("ble.wifiSSIDRequired"));
      return;
    }

    try {
      // If WiFi is enabled and we have credentials, write them to the Brewtool
      if (config.enableWifi && config.wifiSSID.trim()) {
        // Ensure we're connected to the device
        if (!BLEService.isDeviceConnected(device.id)) {
          await BLEService.connectToDevice(device.id);
        }

        // Write WiFi configuration to Brewtool
        await BLEService.writeBrewtoolWifiConfig(
          device.id,
          config.wifiSSID,
          config.wifiPassword
        );

        Alert.alert(t("ble.wifiConfigSaved"), t("ble.wifiConfigSavedMessage"), [
          { text: t("common.ok") },
        ]);
      }

      onSave(device, config);
      onClose();
    } catch (error) {
      console.error("Failed to save WiFi configuration:", error);
      Alert.alert(
        t("ble.saveError"),
        error instanceof Error ? error.message : t("ble.unknownError"),
        [{ text: t("common.ok") }]
      );
    }
  };

  // Update config field
  const updateConfig = (field: keyof BrewtoolConfig, value: any) => {
    setConfig((prev) => ({ ...prev, [field]: value }));
  };

  if (!device) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>{t("common.cancel")}</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{t("ble.connectToBrewtool")}</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>{t("common.save")}</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Device Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {t("ble.deviceInformation")}
            </Text>
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceIcon}>🍺</Text>
              <View style={styles.deviceDetails}>
                <Text style={styles.deviceName}>
                  {device.name || device.localName || t("ble.unknownBrewtool")}
                </Text>
                <Text style={styles.deviceId}>{device.id}</Text>
                <Text style={styles.deviceRssi}>
                  📶 {device.rssi} dBm ({t("ble.signalStrength")})
                </Text>
              </View>
            </View>
          </View>

          {/* Connection Status */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>
                {t("ble.connectionStatus")}
              </Text>
              <TouchableOpacity
                onPress={testInternetConnection}
                disabled={isTestingConnection}
                style={styles.testButton}
              >
                {isTestingConnection ? (
                  <ActivityIndicator size="small" color="#007AFF" />
                ) : (
                  <Text style={styles.testButtonText}>
                    {t("ble.testConnection")}
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {connectionStatus && (
              <View style={styles.statusContainer}>
                <View style={styles.statusItem}>
                  <Text style={styles.statusLabel}>
                    {t("ble.deviceConnection")}:
                  </Text>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor: connectionStatus.isConnected
                          ? "#4CAF50"
                          : "#F44336",
                      },
                    ]}
                  >
                    <Text style={styles.statusBadgeText}>
                      {connectionStatus.isConnected
                        ? t("ble.connected")
                        : t("ble.disconnected")}
                    </Text>
                  </View>
                </View>

                <View style={styles.statusItem}>
                  <Text style={styles.statusLabel}>
                    {t("ble.internetConnection")}:
                  </Text>
                  <View
                    style={[
                      styles.statusBadge,
                      {
                        backgroundColor: connectionStatus.hasInternet
                          ? "#4CAF50"
                          : "#FF9800",
                      },
                    ]}
                  >
                    <Text style={styles.statusBadgeText}>
                      {connectionStatus.hasInternet
                        ? t("ble.online")
                        : t("ble.offline")}
                    </Text>
                  </View>
                </View>

                {connectionStatus.ipAddress && (
                  <View style={styles.statusItem}>
                    <Text style={styles.statusLabel}>
                      {t("ble.ipAddress")}:
                    </Text>
                    <Text style={styles.statusValue}>
                      {connectionStatus.ipAddress}
                    </Text>
                  </View>
                )}

                {connectionStatus.firmwareVersion && (
                  <View style={styles.statusItem}>
                    <Text style={styles.statusLabel}>
                      {t("ble.firmwareVersion")}:
                    </Text>
                    <Text style={styles.statusValue}>
                      {connectionStatus.firmwareVersion}
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Basic Configuration */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {t("ble.basicConfiguration")}
            </Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{t("ble.deviceName")}</Text>
              <TextInput
                style={styles.textInput}
                value={config.name}
                onChangeText={(text) => updateConfig("name", text)}
                placeholder={t("ble.enterDeviceName")}
              />
            </View>

            <View style={styles.switchGroup}>
              <Text style={styles.switchLabel}>{t("ble.autoConnect")}</Text>
              <Switch
                value={config.autoConnect}
                onValueChange={(value) => updateConfig("autoConnect", value)}
              />
            </View>
          </View>

          {/* WiFi Configuration */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {t("ble.wifiConfiguration")}
            </Text>

            <View style={styles.switchGroup}>
              <Text style={styles.switchLabel}>{t("ble.enableWifi")}</Text>
              <Switch
                value={config.enableWifi}
                onValueChange={(value) => updateConfig("enableWifi", value)}
              />
            </View>

            {config.enableWifi && (
              <>
                <View style={styles.inputGroup}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.inputLabel}>
                      {t("ble.wifiNetwork")}
                    </Text>
                    <TouchableOpacity
                      onPress={scanWifiNetworks}
                      disabled={isScanningWifi}
                      style={styles.scanButton}
                    >
                      {isScanningWifi ? (
                        <ActivityIndicator size="small" color="#007AFF" />
                      ) : (
                        <Text style={styles.scanButtonText}>
                          {t("ble.scanNetworks")}
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>

                  {availableNetworks.length > 0 && (
                    <View style={styles.networkList}>
                      {availableNetworks.map((network, index) => (
                        <TouchableOpacity
                          key={index}
                          style={[
                            styles.networkItem,
                            config.wifiSSID === network &&
                              styles.networkItemSelected,
                          ]}
                          onPress={() => updateConfig("wifiSSID", network)}
                        >
                          <Text
                            style={[
                              styles.networkText,
                              config.wifiSSID === network &&
                                styles.networkTextSelected,
                            ]}
                          >
                            📶 {network}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}

                  <TextInput
                    style={styles.textInput}
                    value={config.wifiSSID}
                    onChangeText={(text) => updateConfig("wifiSSID", text)}
                    placeholder={t("ble.enterWifiName")}
                    autoCapitalize="none"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>{t("ble.wifiPassword")}</Text>
                  <TextInput
                    style={styles.textInput}
                    value={config.wifiPassword}
                    onChangeText={(text) => updateConfig("wifiPassword", text)}
                    placeholder={t("ble.enterWifiPassword")}
                    secureTextEntry
                    autoCapitalize="none"
                  />
                </View>
              </>
            )}
          </View>

          {/* Advanced Settings */}
          <View style={styles.section}>
            <TouchableOpacity
              style={styles.advancedToggle}
              onPress={() => setShowAdvanced(!showAdvanced)}
            >
              <Text style={styles.sectionTitle}>
                {t("ble.advancedSettings")}
              </Text>
              <Text style={styles.advancedArrow}>
                {showAdvanced ? "▼" : "▶"}
              </Text>
            </TouchableOpacity>

            {showAdvanced && (
              <>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>
                    {t("ble.temperatureUnit")}
                  </Text>
                  <View style={styles.segmentedControl}>
                    <TouchableOpacity
                      style={[
                        styles.segmentButton,
                        config.temperatureUnit === "celsius" &&
                          styles.segmentButtonActive,
                      ]}
                      onPress={() => updateConfig("temperatureUnit", "celsius")}
                    >
                      <Text
                        style={[
                          styles.segmentButtonText,
                          config.temperatureUnit === "celsius" &&
                            styles.segmentButtonTextActive,
                        ]}
                      >
                        °C
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.segmentButton,
                        config.temperatureUnit === "fahrenheit" &&
                          styles.segmentButtonActive,
                      ]}
                      onPress={() =>
                        updateConfig("temperatureUnit", "fahrenheit")
                      }
                    >
                      <Text
                        style={[
                          styles.segmentButtonText,
                          config.temperatureUnit === "fahrenheit" &&
                            styles.segmentButtonTextActive,
                        ]}
                      >
                        °F
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>
                    {t("ble.updateInterval")} ({config.updateInterval}s)
                  </Text>
                  <View style={styles.sliderContainer}>
                    <Text style={styles.sliderLabel}>10s</Text>
                    <View style={styles.slider}>
                      {/* Simple slider representation - in real app use a proper slider component */}
                      <TouchableOpacity
                        style={styles.sliderTrack}
                        onPress={() => updateConfig("updateInterval", 30)}
                      >
                        <View style={styles.sliderThumb} />
                      </TouchableOpacity>
                    </View>
                    <Text style={styles.sliderLabel}>300s</Text>
                  </View>
                </View>
              </>
            )}
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.connectButton,
              isConnecting && styles.connectButtonDisabled,
            ]}
            onPress={handleConnect}
            disabled={isConnecting}
          >
            {isConnecting ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Text style={styles.connectButtonText}>
                {t("ble.connectToBrewtool")}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
  },
  deviceInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  deviceIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  deviceDetails: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  deviceId: {
    fontSize: 12,
    color: "#666",
    fontFamily: "monospace",
    marginBottom: 2,
  },
  deviceRssi: {
    fontSize: 12,
    color: "#666",
  },
  testButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#f0f0f0",
    borderRadius: 4,
  },
  testButtonText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  statusContainer: {
    gap: 8,
  },
  statusItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  statusLabel: {
    fontSize: 14,
    color: "#666",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusBadgeText: {
    fontSize: 12,
    color: "white",
    fontWeight: "600",
  },
  statusValue: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "monospace",
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 6,
    color: "#333",
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    backgroundColor: "white",
  },
  switchGroup: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  advancedToggle: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  advancedArrow: {
    fontSize: 14,
    color: "#666",
  },
  segmentedControl: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 6,
    overflow: "hidden",
  },
  segmentButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
    backgroundColor: "white",
  },
  segmentButtonActive: {
    backgroundColor: "#007AFF",
  },
  segmentButtonText: {
    fontSize: 14,
    color: "#666",
  },
  segmentButtonTextActive: {
    color: "white",
    fontWeight: "600",
  },
  sliderContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  sliderLabel: {
    fontSize: 12,
    color: "#666",
  },
  slider: {
    flex: 1,
  },
  sliderTrack: {
    height: 4,
    backgroundColor: "#ddd",
    borderRadius: 2,
    position: "relative",
  },
  sliderThumb: {
    width: 20,
    height: 20,
    backgroundColor: "#007AFF",
    borderRadius: 10,
    position: "absolute",
    top: -8,
    left: "50%",
    marginLeft: -10,
  },
  footer: {
    padding: 16,
    backgroundColor: "white",
    borderTopWidth: 1,
    borderTopColor: "#e0e0e0",
  },
  connectButton: {
    backgroundColor: "#4CAF50",
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  connectButtonDisabled: {
    backgroundColor: "#ccc",
  },
  connectButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  scanButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#f0f0f0",
    borderRadius: 4,
  },
  scanButtonText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  networkList: {
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 6,
    backgroundColor: "white",
  },
  networkItem: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  networkItemSelected: {
    backgroundColor: "#e3f2fd",
  },
  networkText: {
    fontSize: 14,
    color: "#333",
  },
  networkTextSelected: {
    color: "#007AFF",
    fontWeight: "500",
  },
});
