import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  FlatList,
} from "react-native";
import { useTranslation } from "react-i18next";
import BLEService, { DiscoveredDevice } from "@/services/BLEService";
import BrewtoolConnectionModal from "./BrewtoolConnectionModal";

interface BrewtoolScannerProps {
  onBrewtoolSelect: (device: DiscoveredDevice) => void;
  onScanStateChange?: (isScanning: boolean) => void;
  autoScan?: boolean;
}

export default function BrewtoolScanner({
  onBrewtoolSelect,
  onScanStateChange,
  autoScan = false,
}: BrewtoolScannerProps) {
  const { t } = useTranslation();
  const [isScanning, setIsScanning] = useState(false);
  const [brewtools, setBrewtools] = useState<DiscoveredDevice[]>([]);
  const [scanDuration, setScanDuration] = useState(0);
  const [selectedDevice, setSelectedDevice] = useState<DiscoveredDevice | null>(
    null
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Handle scan results - filter for Brewtools only
  const handleScanResults = useCallback((devices: DiscoveredDevice[]) => {
    const brewtoolDevices = devices.filter((device) => device.isBrewtool);
    setBrewtools(brewtoolDevices);
  }, []);

  // Start scanning for Brewtools
  const startBrewtoolScan = useCallback(async () => {
    try {
      const enabled = await BLEService.isBluetoothEnabled();
      if (!enabled) {
        Alert.alert(
          t("ble.bluetoothDisabled"),
          t("ble.enableBluetoothMessage"),
          [{ text: t("common.ok") }]
        );
        return;
      }

      setIsScanning(true);
      setBrewtools([]);
      setScanDuration(0);
      onScanStateChange?.(true);

      // Start scan with Brewtool filter
      await BLEService.startScan(true);

      // Update scan duration every second
      const durationInterval = setInterval(() => {
        setScanDuration((prev) => prev + 1);
      }, 1000);

      // Auto-stop after 30 seconds
      setTimeout(() => {
        clearInterval(durationInterval);
        stopBrewtoolScan();
      }, 30000);
    } catch (error) {
      console.error("Brewtool scan failed:", error);
      setIsScanning(false);
      onScanStateChange?.(false);

      Alert.alert(
        t("ble.scanError"),
        error instanceof Error ? error.message : t("ble.unknownError"),
        [{ text: t("common.ok") }]
      );
    }
  }, [onScanStateChange, t]);

  // Stop scanning
  const stopBrewtoolScan = useCallback(() => {
    BLEService.stopScan();
    setIsScanning(false);
    setScanDuration(0);
    onScanStateChange?.(false);
  }, [onScanStateChange]);

  // Handle Brewtool selection
  const handleBrewtoolSelect = useCallback((device: DiscoveredDevice) => {
    setSelectedDevice(device);
    setShowConnectionModal(true);
  }, []);

  // Handle modal connect
  const handleModalConnect = useCallback(
    async (device: DiscoveredDevice, config?: any) => {
      try {
        await onBrewtoolSelect(device);
        setShowConnectionModal(false);
      } catch (error) {
        // Error handling is done in the modal
        throw error;
      }
    },
    [onBrewtoolSelect]
  );

  // Handle modal save
  const handleModalSave = useCallback(
    (device: DiscoveredDevice, config: any) => {
      // Save configuration logic here
      console.log("Saving Brewtool configuration:", {
        device: device.id,
        config,
      });
      setShowConnectionModal(false);
    },
    []
  );

  // Setup scan listener
  useEffect(() => {
    BLEService.addScanListener(handleScanResults);

    return () => {
      BLEService.removeScanListener(handleScanResults);
    };
  }, [handleScanResults]);

  // Auto-scan if requested
  useEffect(() => {
    if (autoScan) {
      startBrewtoolScan();
    }
  }, [autoScan, startBrewtoolScan]);

  // Format scan duration
  const formatScanDuration = (seconds: number) => {
    return `${seconds}s`;
  };

  // Get device signal strength indicator
  const getSignalStrengthColor = (rssi: number) => {
    if (rssi > -50) return "#4CAF50"; // Excellent
    if (rssi > -70) return "#FF9800"; // Good
    if (rssi > -80) return "#FF5722"; // Fair
    return "#F44336"; // Poor
  };

  // Render Brewtool item
  const renderBrewtoolItem = ({ item }: { item: DiscoveredDevice }) => (
    <TouchableOpacity
      style={styles.brewtoolItem}
      onPress={() => handleBrewtoolSelect(item)}
    >
      <View style={styles.brewtoolHeader}>
        <Text style={styles.brewtoolIcon}>🍺</Text>
        <View style={styles.brewtoolInfo}>
          <Text style={styles.brewtoolName}>
            {item.name || item.localName || t("ble.unknownBrewtool")}
          </Text>
          <Text style={styles.brewtoolId}>{item.id}</Text>
        </View>
        <View style={styles.brewtoolMeta}>
          <View
            style={[
              styles.signalIndicator,
              { backgroundColor: getSignalStrengthColor(item.rssi) },
            ]}
          />
          <Text style={styles.rssiText}>{item.rssi} dBm</Text>
        </View>
      </View>

      {item.serviceUUIDs && item.serviceUUIDs.length > 0 && (
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceLabel}>{t("ble.services")}:</Text>
          <Text style={styles.serviceCount}>
            {item.serviceUUIDs.length} {t("ble.servicesAvailable")}
          </Text>
        </View>
      )}

      <View style={styles.brewtoolFooter}>
        {item.isConnectable && (
          <View style={styles.connectableBadge}>
            <Text style={styles.connectableText}>
              {t("ble.readyToConnect")}
            </Text>
          </View>
        )}
        <TouchableOpacity
          style={styles.connectButton}
          onPress={() => handleBrewtoolSelect(item)}
        >
          <Text style={styles.connectButtonText}>
            {t("ble.connectToBrewtool")}
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{t("ble.brewtoolScanner")}</Text>
        <Text style={styles.subtitle}>{t("ble.scanForBrewtools")}</Text>
      </View>

      {/* Scan Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.scanButton, isScanning && styles.scanButtonActive]}
          onPress={isScanning ? stopBrewtoolScan : startBrewtoolScan}
        >
          {isScanning && (
            <ActivityIndicator
              size="small"
              color="white"
              style={styles.spinner}
            />
          )}
          <Text style={styles.scanButtonText}>
            {isScanning ? t("ble.stopScan") : t("ble.scanForBrewtools")}
          </Text>
        </TouchableOpacity>

        {isScanning && (
          <View style={styles.scanProgress}>
            <Text style={styles.scanDuration}>
              {t("ble.scanningFor")} {formatScanDuration(scanDuration)}
            </Text>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${(scanDuration / 30) * 100}%` },
                ]}
              />
            </View>
          </View>
        )}
      </View>

      {/* Results */}
      <View style={styles.results}>
        <View style={styles.resultsHeader}>
          <Text style={styles.resultsTitle}>
            {t("ble.brewtoolsFound")} ({brewtools.length})
          </Text>
          {brewtools.length > 0 && !isScanning && (
            <TouchableOpacity
              style={styles.refreshButton}
              onPress={startBrewtoolScan}
            >
              <Text style={styles.refreshButtonText}>{t("ble.refresh")}</Text>
            </TouchableOpacity>
          )}
        </View>

        {brewtools.length === 0 && !isScanning && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>🔍</Text>
            <Text style={styles.emptyTitle}>{t("ble.noBrewtoolsFound")}</Text>
            <Text style={styles.emptySubtitle}>
              {t("ble.makeBrewtoolDiscoverable")}
            </Text>
            <TouchableOpacity
              style={styles.emptyActionButton}
              onPress={startBrewtoolScan}
            >
              <Text style={styles.emptyActionText}>{t("ble.scanAgain")}</Text>
            </TouchableOpacity>
          </View>
        )}

        <FlatList
          data={brewtools}
          renderItem={renderBrewtoolItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      </View>

      {/* Scanning Overlay */}
      {isScanning && brewtools.length === 0 && (
        <View style={styles.scanningOverlay}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.scanningText}>
            {t("ble.searchingForBrewtools")}
          </Text>
          <Text style={styles.scanningSubtext}>
            {t("ble.ensureBrewtoolsOn")}
          </Text>
        </View>
      )}

      {/* Connection Modal */}
      <BrewtoolConnectionModal
        visible={showConnectionModal}
        device={selectedDevice}
        onClose={() => setShowConnectionModal(false)}
        onConnect={handleModalConnect}
        onSave={handleModalSave}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 16,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: "#666",
  },
  controls: {
    padding: 16,
    backgroundColor: "white",
    marginBottom: 8,
  },
  scanButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  scanButtonActive: {
    backgroundColor: "#FF3B30",
  },
  scanButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  spinner: {
    marginRight: 8,
  },
  scanProgress: {
    marginTop: 12,
  },
  scanDuration: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#e0e0e0",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#007AFF",
  },
  results: {
    flex: 1,
    backgroundColor: "white",
  },
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  refreshButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#f0f0f0",
    borderRadius: 4,
  },
  refreshButtonText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  listContainer: {
    padding: 16,
  },
  brewtoolItem: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#4CAF50",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  brewtoolHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  brewtoolIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  brewtoolInfo: {
    flex: 1,
  },
  brewtoolName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  brewtoolId: {
    fontSize: 12,
    color: "#666",
    fontFamily: "monospace",
  },
  brewtoolMeta: {
    alignItems: "flex-end",
  },
  signalIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 2,
  },
  rssiText: {
    fontSize: 12,
    color: "#666",
  },
  serviceInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  serviceLabel: {
    fontSize: 12,
    color: "#666",
    marginRight: 4,
  },
  serviceCount: {
    fontSize: 12,
    color: "#007AFF",
    fontWeight: "500",
  },
  brewtoolFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  connectableBadge: {
    backgroundColor: "#E8F5E8",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  connectableText: {
    fontSize: 12,
    color: "#4CAF50",
    fontWeight: "500",
  },
  connectButton: {
    backgroundColor: "#4CAF50",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  connectButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginBottom: 24,
  },
  emptyActionButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyActionText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  scanningOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  scanningText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: "500",
    textAlign: "center",
  },
  scanningSubtext: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
});
