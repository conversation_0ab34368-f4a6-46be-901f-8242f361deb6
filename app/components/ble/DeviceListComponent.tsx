import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  RefreshControl,
} from "react-native";
import { useTranslation } from "react-i18next";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import BLEService, {
  DiscoveredDevice,
  ConnectedDevice,
} from "@/services/BLEService";
import { get, post, del } from "@/helpers/http";
import BrewtoolConnectionModal from "./BrewtoolConnectionModal";

interface UserDevice {
  id: number;
  name: string;
  deviceId: string;
  deviceType: string;
  isConnected: boolean;
  lastSeen: string;
  createdAt: string;
}

interface DeviceListComponentProps {
  showDiscovered?: boolean;
  showUserDevices?: boolean;
  onDeviceSelect?: (device: DiscoveredDevice | UserDevice) => void;
  onDeviceConnect?: (device: DiscoveredDevice) => void;
  onDeviceRemove?: (device: UserDevice) => void;
}

export default function DeviceListComponent({
  showDiscovered = true,
  showUserDevices = true,
  onDeviceSelect,
  onDeviceConnect,
  onDeviceRemove,
}: DeviceListComponentProps) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [discoveredDevices, setDiscoveredDevices] = useState<
    DiscoveredDevice[]
  >([]);
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>(
    []
  );
  const [selectedDevice, setSelectedDevice] = useState<DiscoveredDevice | null>(
    null
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Fetch user devices
  const {
    data: userDevices = [],
    isLoading: isLoadingUserDevices,
    refetch: refetchUserDevices,
  } = useQuery({
    queryKey: ["userDevices"],
    queryFn: async () => {
      const response = await get("user/devices");
      return response.json() as Promise<UserDevice[]>;
    },
    enabled: showUserDevices,
  });

  // Add device mutation
  const addDeviceMutation = useMutation({
    mutationFn: async (device: DiscoveredDevice) => {
      const response = await post("user/devices", {
        name: device.name || device.localName || "Unknown Device",
        deviceId: device.id,
        deviceType: device.isBrewtool ? "BREWTOOL" : "GENERIC",
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userDevices"] });
    },
  });

  // Remove device mutation
  const removeDeviceMutation = useMutation({
    mutationFn: async (deviceId: number) => {
      await del(`user/devices/${deviceId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userDevices"] });
    },
  });

  // Handle discovered devices updates
  const handleDiscoveredDevices = useCallback((devices: DiscoveredDevice[]) => {
    setDiscoveredDevices(devices);
  }, []);

  // Handle connected devices updates
  const handleConnectedDevices = useCallback((devices: ConnectedDevice[]) => {
    setConnectedDevices(devices);
  }, []);

  // Setup BLE listeners
  useEffect(() => {
    if (showDiscovered) {
      BLEService.addScanListener(handleDiscoveredDevices);
      BLEService.addConnectionListener(handleConnectedDevices);

      // Get current devices
      setDiscoveredDevices(BLEService.getDiscoveredDevices());
      setConnectedDevices(BLEService.getConnectedDevices());
    }

    return () => {
      if (showDiscovered) {
        BLEService.removeScanListener(handleDiscoveredDevices);
        BLEService.removeConnectionListener(handleConnectedDevices);
      }
    };
  }, [showDiscovered, handleDiscoveredDevices, handleConnectedDevices]);

  // Handle device connection
  const handleConnect = useCallback(
    async (device: DiscoveredDevice) => {
      // For Brewtool devices, show the connection modal
      if (device.isBrewtool) {
        setSelectedDevice(device);
        setShowConnectionModal(true);
        return;
      }

      // For other devices, connect directly
      try {
        await BLEService.connectToDevice(device.id);
        onDeviceConnect?.(device);
      } catch (error) {
        Alert.alert(
          t("ble.connectionError"),
          error instanceof Error ? error.message : t("ble.unknownError")
        );
      }
    },
    [onDeviceConnect, t]
  );

  // Handle modal connect
  const handleModalConnect = useCallback(
    async (device: DiscoveredDevice, config?: any) => {
      try {
        await BLEService.connectToDevice(device.id);
        onDeviceConnect?.(device);
        setShowConnectionModal(false);
      } catch (error) {
        // Error handling is done in the modal
        throw error;
      }
    },
    [onDeviceConnect]
  );

  // Handle modal save
  const handleModalSave = useCallback(
    (device: DiscoveredDevice, config: any) => {
      // Save configuration logic here
      console.log("Saving Brewtool configuration:", {
        device: device.id,
        config,
      });
      setShowConnectionModal(false);
    },
    []
  );

  // Handle device disconnection
  const handleDisconnect = useCallback(
    async (deviceId: string) => {
      try {
        await BLEService.disconnectFromDevice(deviceId);
      } catch (error) {
        Alert.alert(
          t("ble.disconnectionError"),
          error instanceof Error ? error.message : t("ble.unknownError")
        );
      }
    },
    [t]
  );

  // Handle adding device to user account
  const handleAddDevice = useCallback(
    (device: DiscoveredDevice) => {
      Alert.alert(
        t("ble.addDevice"),
        t("ble.addDeviceMessage", {
          name: device.name || t("ble.unknownDevice"),
        }),
        [
          { text: t("common.cancel"), style: "cancel" },
          {
            text: t("ble.add"),
            onPress: () => addDeviceMutation.mutate(device),
          },
        ]
      );
    },
    [addDeviceMutation, t]
  );

  // Handle removing device from user account
  const handleRemoveDevice = useCallback(
    (device: UserDevice) => {
      Alert.alert(
        t("ble.removeDevice"),
        t("ble.removeDeviceMessage", { name: device.name }),
        [
          { text: t("common.cancel"), style: "cancel" },
          {
            text: t("ble.remove"),
            style: "destructive",
            onPress: () => {
              removeDeviceMutation.mutate(device.id);
              onDeviceRemove?.(device);
            },
          },
        ]
      );
    },
    [removeDeviceMutation, onDeviceRemove, t]
  );

  // Check if device is already added
  const isDeviceAdded = useCallback(
    (deviceId: string) => {
      return userDevices.some((device) => device.deviceId === deviceId);
    },
    [userDevices]
  );

  // Check if device is connected
  const isDeviceConnected = useCallback(
    (deviceId: string) => {
      return connectedDevices.some((device) => device.id === deviceId);
    },
    [connectedDevices]
  );

  // Render discovered device item
  const renderDiscoveredDevice = ({ item }: { item: DiscoveredDevice }) => {
    const isConnected = isDeviceConnected(item.id);
    const isAdded = isDeviceAdded(item.id);

    return (
      <View
        style={[styles.deviceItem, item.isBrewtool && styles.brewToolDevice]}
      >
        <TouchableOpacity
          style={styles.deviceContent}
          onPress={() => onDeviceSelect?.(item)}
        >
          <View style={styles.deviceHeader}>
            <Text style={styles.deviceIcon}>
              {item.isBrewtool ? "🍺" : "📱"}
            </Text>
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceName}>
                {item.name || item.localName || t("ble.unknownDevice")}
              </Text>
              <Text style={styles.deviceId}>{item.id}</Text>
            </View>
            <View style={styles.deviceStatus}>
              {isConnected && (
                <View style={styles.connectedBadge}>
                  <Text style={styles.connectedText}>{t("ble.connected")}</Text>
                </View>
              )}
              {item.isBrewtool && (
                <View style={styles.brewToolBadge}>
                  <Text style={styles.brewToolText}>{t("ble.brewTool")}</Text>
                </View>
              )}
            </View>
          </View>

          <View style={styles.deviceMeta}>
            <Text style={styles.rssi}>📶 {item.rssi} dBm</Text>
            {item.isConnectable && (
              <Text style={styles.connectable}>{t("ble.connectable")}</Text>
            )}
          </View>
        </TouchableOpacity>

        <View style={styles.deviceActions}>
          {!isConnected ? (
            <TouchableOpacity
              style={styles.connectButton}
              onPress={() => handleConnect(item)}
            >
              <Text style={styles.connectButtonText}>
                {item.isBrewtool
                  ? t("ble.connectToBrewtool")
                  : t("ble.connect")}
              </Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.disconnectButton}
              onPress={() => handleDisconnect(item.id)}
            >
              <Text style={styles.disconnectButtonText}>
                {t("ble.disconnect")}
              </Text>
            </TouchableOpacity>
          )}

          {!isAdded && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => handleAddDevice(item)}
              disabled={addDeviceMutation.isPending}
            >
              <Text style={styles.addButtonText}>
                {addDeviceMutation.isPending
                  ? t("common.loading")
                  : t("ble.add")}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  // Render user device item
  const renderUserDevice = ({ item }: { item: UserDevice }) => {
    const isConnected = isDeviceConnected(item.deviceId);

    return (
      <View style={[styles.deviceItem, styles.userDevice]}>
        <TouchableOpacity
          style={styles.deviceContent}
          onPress={() => onDeviceSelect?.(item)}
        >
          <View style={styles.deviceHeader}>
            <Text style={styles.deviceIcon}>
              {item.deviceType === "BREWTOOL" ? "🍺" : "📱"}
            </Text>
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceName}>{item.name}</Text>
              <Text style={styles.deviceId}>{item.deviceId}</Text>
              <Text style={styles.deviceDate}>
                {t("ble.addedOn")}{" "}
                {new Date(item.createdAt).toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.deviceStatus}>
              {isConnected ? (
                <View style={styles.connectedBadge}>
                  <Text style={styles.connectedText}>{t("ble.connected")}</Text>
                </View>
              ) : (
                <View style={styles.offlineBadge}>
                  <Text style={styles.offlineText}>{t("ble.offline")}</Text>
                </View>
              )}
            </View>
          </View>
        </TouchableOpacity>

        <View style={styles.deviceActions}>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveDevice(item)}
            disabled={removeDeviceMutation.isPending}
          >
            <Text style={styles.removeButtonText}>
              {removeDeviceMutation.isPending
                ? t("common.loading")
                : t("ble.remove")}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {showDiscovered && discoveredDevices.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t("ble.discoveredDevices")} ({discoveredDevices.length})
          </Text>
          <FlatList
            data={discoveredDevices}
            renderItem={renderDiscoveredDevice}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}

      {showUserDevices && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t("ble.myDevices")} ({userDevices.length})
          </Text>
          {isLoadingUserDevices ? (
            <View style={styles.loadingContainer}>
              <Text>{t("common.loading")}</Text>
            </View>
          ) : userDevices.length === 0 ? (
            <View style={styles.emptyState}>
              <Text style={styles.emptyText}>{t("ble.noDevicesAdded")}</Text>
              <Text style={styles.emptySubtext}>
                {t("ble.scanAndAddDevices")}
              </Text>
            </View>
          ) : (
            <FlatList
              data={userDevices}
              renderItem={renderUserDevice}
              keyExtractor={(item) => item.id.toString()}
              refreshControl={
                <RefreshControl
                  refreshing={isLoadingUserDevices}
                  onRefresh={refetchUserDevices}
                />
              }
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      )}

      {/* Connection Modal */}
      <BrewtoolConnectionModal
        visible={showConnectionModal}
        device={selectedDevice}
        onClose={() => setShowConnectionModal(false)}
        onConnect={handleModalConnect}
        onSave={handleModalSave}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  deviceItem: {
    backgroundColor: "white",
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  brewToolDevice: {
    borderLeftWidth: 4,
    borderLeftColor: "#4CAF50",
  },
  userDevice: {
    borderLeftWidth: 4,
    borderLeftColor: "#007AFF",
  },
  deviceContent: {
    padding: 16,
  },
  deviceHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  deviceIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  deviceId: {
    fontSize: 12,
    color: "#666",
    fontFamily: "monospace",
    marginBottom: 2,
  },
  deviceDate: {
    fontSize: 12,
    color: "#666",
  },
  deviceStatus: {
    alignItems: "flex-end",
  },
  connectedBadge: {
    backgroundColor: "#4CAF50",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 2,
  },
  connectedText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  offlineBadge: {
    backgroundColor: "#9E9E9E",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 2,
  },
  offlineText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  brewToolBadge: {
    backgroundColor: "#FF9800",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  brewToolText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  deviceMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rssi: {
    fontSize: 12,
    color: "#666",
  },
  connectable: {
    fontSize: 12,
    color: "#4CAF50",
    fontWeight: "500",
  },
  deviceActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 8,
  },
  connectButton: {
    backgroundColor: "#4CAF50",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  connectButtonText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  disconnectButton: {
    backgroundColor: "#FF5722",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  disconnectButtonText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  addButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  addButtonText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  removeButton: {
    backgroundColor: "#FF3B30",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  removeButtonText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
  },
  emptyState: {
    padding: 20,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
});
