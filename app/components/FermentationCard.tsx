import { Box } from "@/components/ui/box";
import { ButtonText, Button } from "@/components/ui/button";
import { Center } from "@/components/ui/center";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { useTranslation } from "react-i18next";
import {
  useActualDeviceTemperature,
  useActualTemperature,
} from "@/services/FermentationService";
import { BoltSlashIcon } from "@heroicons/react/24/solid";
import { Href, Link } from "expo-router";
import SkeletonLoading from "@/components/common/SkeletonLoading";
import { APP_URL } from "@/helpers/constants";
import { Platform } from "react-native";
import { openBrowserAsync } from "expo-web-browser";
import { router } from "expo-router";

const pathFermentation = (APP_URL + "/fermentation") as Href<string>;

export default function FermentationCard() {
  const { t } = useTranslation();

  const temperature = useActualTemperature();

  const deviceTemperature = useActualDeviceTemperature();

  return (
    <Box>
      <Heading size="xl">{t("fermentation.name")}</Heading>
      <>
        {temperature.isLoading && <SkeletonLoading />}
        {!temperature.isLoading && !temperature.data && (
          <Center className="text-center">
            <BoltSlashIcon className="mx-auto h-10 w-10" />
            <Heading className="mt-2 text-sm font-semibold text-gray-900">
              {t("fermentation.noActiveFermentation")}
            </Heading>
            <Text className="mt-1 text-sm text-gray-500">
              {t("fermentation.noActiveFermentationHelp")}
            </Text>
            <Box className="mt-6">
              <Button
                size="md"
                variant="solid"
                action="primary"
                onPress={() => router.push("/fermentation")}
              >
                <ButtonText>{t("fermentation.startFermentation")}</ButtonText>
              </Button>
            </Box>
          </Center>
        )}
        {temperature.data && (
          <Center className="text-center">
            <Text className="text-md mt-2 flex items-center justify-center font-semibold text-gray-900">
              <Box className="mr-2 h-3 w-3 rounded-full bg-green-600"></Box>
              {t("fermentation.activeFermentation")}
            </Text>
            <Heading className="mt-2 text-3xl font-semibold text-gray-900">
              {t("common.temperature", {
                temp: deviceTemperature.data?.temperature ?? " - ",
              })}
            </Heading>
            <Text className="mt-1 text-sm text-gray-500">
              {t("fermentation.shouldTemperature")}{" "}
              {t("common.temperature", {
                temp: temperature.data?.temperature ?? " - ",
              })}
            </Text>
            <Button
              size="md"
              variant="solid"
              action="primary"
              className="mt-6"
              onPress={() => router.push("/fermentation")}
            >
              <ButtonText>{t("common.open")}</ButtonText>
            </Button>
          </Center>
        )}
      </>
    </Box>
  );
}
