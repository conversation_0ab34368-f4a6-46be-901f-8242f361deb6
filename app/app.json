{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "gaerschrank-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "g<PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.gaerschrank.app", "entitlements": {"com.apple.security.application-groups": ["group.gaerschrank.widget"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT", "android.permission.BIND_APPWIDGET"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", "react-native-ble-plx", "expo-localization"], "experiments": {"typedRoutes": true}}}