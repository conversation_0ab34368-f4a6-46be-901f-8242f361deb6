import * as AppleAuthentication from "expo-apple-authentication";
import { Platform } from "react-native";
import {
  startRegistration,
  startAuthentication,
} from "@simplewebauthn/browser";
import type {
  RegistrationResponseJSON,
  AuthenticationResponseJSON,
} from "@simplewebauthn/types";
import { post, get } from "@/helpers/http";
import { TokenStorage, UserStorage } from "@/helpers/secureStorage";

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
}

class AuthService {
  private tokens: AuthTokens | null = null;
  private user: User | null = null;

  // Token management
  async getTokens(): Promise<AuthTokens | null> {
    if (this.tokens) return this.tokens;

    try {
      this.tokens = await TokenStorage.getTokens();
      return this.tokens;
    } catch (error) {
      console.error("Error getting tokens:", error);
    }
    return null;
  }

  async setTokens(tokens: AuthTokens): Promise<void> {
    this.tokens = tokens;
    try {
      await TokenStorage.setTokens(tokens);
    } catch (error) {
      console.error("Error storing tokens:", error);
    }
  }

  async clearTokens(): Promise<void> {
    this.tokens = null;
    try {
      await TokenStorage.clearTokens();
      await UserStorage.clearUser();
    } catch (error) {
      console.error("Error clearing tokens:", error);
    }
  }

  // User management
  async getUser(): Promise<User | null> {
    if (this.user) return this.user;

    try {
      this.user = await UserStorage.getUser();
      return this.user;
    } catch (error) {
      console.error("Error getting user:", error);
    }
    return null;
  }

  async setUser(user: User): Promise<void> {
    this.user = user;
    try {
      await UserStorage.setUser(user);
    } catch (error) {
      console.error("Error storing user:", error);
    }
  }

  // Email/Password Authentication
  async loginWithEmail(
    credentials: LoginCredentials
  ): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      const response = await post("auth/login", credentials);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      await this.setTokens(data.tokens);
      await this.setUser(data.user);

      return data;
    } catch (error) {
      console.error("Email login error:", error);
      throw error;
    }
  }

  async registerWithEmail(
    credentials: RegisterCredentials
  ): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      const response = await post("auth/register", credentials);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Registration failed");
      }

      await this.setTokens(data.tokens);
      await this.setUser(data.user);

      return data;
    } catch (error) {
      console.error("Email registration error:", error);
      throw error;
    }
  }

  // Apple ID Authentication
  async loginWithApple(): Promise<{ user: User; tokens: AuthTokens }> {
    if (Platform.OS !== "ios") {
      throw new Error("Apple authentication is only available on iOS");
    }

    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      const response = await post("auth/apple", {
        identityToken: credential.identityToken,
        authorizationCode: credential.authorizationCode,
        user: credential.user,
        fullName: credential.fullName,
        email: credential.email,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Apple login failed");
      }

      await this.setTokens(data.tokens);
      await this.setUser(data.user);

      return data;
    } catch (error) {
      console.error("Apple login error:", error);
      throw error;
    }
  }

  // WebAuthn Authentication
  async registerWithWebAuthn(
    email: string
  ): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Get registration options from server
      const optionsResponse = await post("auth/webauthn/register/begin", {
        email,
      });
      const options = await optionsResponse.json();

      if (!optionsResponse.ok) {
        throw new Error(
          options.message || "Failed to get registration options"
        );
      }

      // Start registration with authenticator
      const attResp = await startRegistration(options);

      // Send registration response to server
      const verificationResponse = await post("auth/webauthn/register/finish", {
        email,
        registrationResponse: attResp,
      });

      const data = await verificationResponse.json();

      if (!verificationResponse.ok) {
        throw new Error(data.message || "WebAuthn registration failed");
      }

      await this.setTokens(data.tokens);
      await this.setUser(data.user);

      return data;
    } catch (error) {
      console.error("WebAuthn registration error:", error);
      throw error;
    }
  }

  async loginWithWebAuthn(
    email: string
  ): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Get authentication options from server
      const optionsResponse = await post("auth/webauthn/authenticate/begin", {
        email,
      });
      const options = await optionsResponse.json();

      if (!optionsResponse.ok) {
        throw new Error(
          options.message || "Failed to get authentication options"
        );
      }

      // Start authentication with authenticator
      const asseResp = await startAuthentication(options);

      // Send authentication response to server
      const verificationResponse = await post(
        "auth/webauthn/authenticate/finish",
        {
          email,
          authenticationResponse: asseResp,
        }
      );

      const data = await verificationResponse.json();

      if (!verificationResponse.ok) {
        throw new Error(data.message || "WebAuthn authentication failed");
      }

      await this.setTokens(data.tokens);
      await this.setUser(data.user);

      return data;
    } catch (error) {
      console.error("WebAuthn authentication error:", error);
      throw error;
    }
  }

  // Check if WebAuthn is available
  async isWebAuthnAvailable(): Promise<boolean> {
    try {
      return (
        typeof window !== "undefined" &&
        window.PublicKeyCredential &&
        typeof window.PublicKeyCredential === "function"
      );
    } catch {
      return false;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const tokens = await this.getTokens();
      if (tokens) {
        await post("auth/logout", { refreshToken: tokens.refreshToken });
      }
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      await this.clearTokens();
      this.user = null;
    }
  }

  // Check authentication status
  async isAuthenticated(): Promise<boolean> {
    const tokens = await this.getTokens();
    return tokens !== null;
  }

  // Refresh tokens
  async refreshTokens(): Promise<AuthTokens> {
    const tokens = await this.getTokens();
    if (!tokens) {
      throw new Error("No refresh token available");
    }

    try {
      const response = await post("auth/refresh", {
        refreshToken: tokens.refreshToken,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Token refresh failed");
      }

      await this.setTokens(data.tokens);
      return data.tokens;
    } catch (error) {
      console.error("Token refresh error:", error);
      await this.clearTokens();
      throw error;
    }
  }
}

export default new AuthService();
