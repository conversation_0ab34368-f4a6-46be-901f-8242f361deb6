import { Device, StatisticValues } from "@prisma/client";
import { useGet } from "@/helpers/http";

export type SpindelData = {
  id: number;
  createdAt: string | Date;
  gravity?: number;
  temperature?: number;
  battery?: number;
  angle?: number;
};

export const useGetSpindelStatistic = (deviceId: number) => {
  return useGet<SpindelData | null>(`device/${deviceId}/statistic`);
};

export const useGetSpindelStatistics = (deviceId: number) => {
  return useGet<SpindelData[]>(`device/${deviceId}/statistics`);
};

export const useGetSpindels = () => {
  return useGet<Device[]>("device?type=ISPINDEL");
};
