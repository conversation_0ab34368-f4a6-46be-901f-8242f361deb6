import WidgetService, { WidgetData } from '../WidgetService';
import { Brewtool } from '../BrewtoolService';

// Mock the HTTP helper
jest.mock('@/helpers/http', () => ({
  get: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock WidgetModule
jest.mock('@/modules/WidgetModule', () => ({
  updateWidgetData: jest.fn(),
  refreshWidget: jest.fn(),
  isWidgetSupported: jest.fn().mockResolvedValue(true),
}));

import { get } from '@/helpers/http';
const mockGet = get as jest.MockedFunction<typeof get>;

describe('WidgetService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createMockBrewtool = (overrides: Partial<Brewtool> = {}): Brewtool => ({
    id: 1,
    name: 'Test Brewtool',
    type: 'BREWTOOL',
    statistic: {
      id: 1,
      temperature: 19.2,
      createdAt: new Date(),
      updatedAt: new Date(),
      deviceId: 1,
    },
    config: {
      id: 1,
      temperature: 19.0,
      recipeName: 'Test Recipe',
      createdAt: new Date(),
      updatedAt: new Date(),
      deviceId: 1,
      sendPush: false,
      startDate: new Date(),
      endDate: null,
    },
    deviceConnections: [],
    plato: 12.5,
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: 1,
    operator: 'BREWTOOL',
    mqttClientId: null,
    mqttUsername: null,
    mqttPassword: null,
    tuyaDeviceId: null,
    online: true,
    ...overrides,
  });

  describe('getWidgetData', () => {
    it('should return formatted widget data for active fermentation', async () => {
      const mockBrewtool = createMockBrewtool();
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result).toEqual({
        fermentationName: 'Test Recipe',
        currentTemperature: 19.2,
        targetTemperature: 19.0,
        gravity: 12.5,
        lastUpdate: expect.any(String),
        status: 'fermentation',
        deviceId: 1,
      });
    });

    it('should return cold crash status for temperature 4°C', async () => {
      const mockBrewtool = createMockBrewtool({
        config: {
          id: 1,
          temperature: 4,
          recipeName: 'Cold Crash',
          createdAt: new Date(),
          updatedAt: new Date(),
          deviceId: 1,
          sendPush: false,
          startDate: new Date(),
          endDate: null,
        },
      });
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result.status).toBe('coldCrash');
    });

    it('should return standby status when no config', async () => {
      const mockBrewtool = createMockBrewtool({
        config: undefined,
      });
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result.status).toBe('standby');
    });

    it('should return offline status for old data', async () => {
      const oldDate = new Date();
      oldDate.setMinutes(oldDate.getMinutes() - 15); // 15 minutes ago

      const mockBrewtool = createMockBrewtool({
        statistic: {
          id: 1,
          temperature: 19.2,
          createdAt: oldDate,
          updatedAt: oldDate,
          deviceId: 1,
        },
      });
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result.status).toBe('offline');
    });

    it('should handle empty brewtools array', async () => {
      const mockResponse = {
        json: jest.fn().mockResolvedValue([]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result).toEqual({
        fermentationName: 'Keine Gärung aktiv',
        currentTemperature: null,
        targetTemperature: null,
        gravity: null,
        lastUpdate: expect.any(String),
        status: 'standby',
        deviceId: null,
      });
    });

    it('should handle API errors gracefully', async () => {
      mockGet.mockRejectedValue(new Error('Network error'));

      const result = await WidgetService.getWidgetData();

      expect(result).toEqual({
        fermentationName: 'Fehler beim Laden',
        currentTemperature: null,
        targetTemperature: null,
        gravity: null,
        lastUpdate: expect.any(String),
        status: 'offline',
        deviceId: null,
      });
    });
  });

  describe('findMostRelevantFermentation', () => {
    it('should prioritize active fermentation over standby', async () => {
      const standbyDevice = createMockBrewtool({
        id: 1,
        config: undefined,
      });
      const activeDevice = createMockBrewtool({
        id: 2,
        config: {
          id: 2,
          temperature: 19.0,
          recipeName: 'Active Recipe',
          createdAt: new Date(),
          updatedAt: new Date(),
          deviceId: 2,
          sendPush: false,
          startDate: new Date(),
          endDate: null,
        },
      });

      const mockResponse = {
        json: jest.fn().mockResolvedValue([standbyDevice, activeDevice]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      const result = await WidgetService.getWidgetData();

      expect(result.deviceId).toBe(2);
      expect(result.fermentationName).toBe('Active Recipe');
    });
  });

  describe('caching', () => {
    it('should return cached data within cache duration', async () => {
      const mockBrewtool = createMockBrewtool();
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      // First call
      await WidgetService.getWidgetData();
      expect(mockGet).toHaveBeenCalledTimes(1);

      // Second call within cache duration
      await WidgetService.getWidgetData();
      expect(mockGet).toHaveBeenCalledTimes(1); // Should not call API again
    });

    it('should refresh data after cache expiration', async () => {
      const mockBrewtool = createMockBrewtool();
      const mockResponse = {
        json: jest.fn().mockResolvedValue([mockBrewtool]),
      };
      mockGet.mockResolvedValue(mockResponse as any);

      // First call
      await WidgetService.getWidgetData();
      expect(mockGet).toHaveBeenCalledTimes(1);

      // Force refresh
      await WidgetService.refreshWidgetData();
      expect(mockGet).toHaveBeenCalledTimes(2);
    });
  });
});
