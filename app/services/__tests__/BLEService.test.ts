import BLEService from '../BLEService';

// Mock react-native-ble-plx
jest.mock('react-native-ble-plx', () => ({
  BleManager: jest.fn().mockImplementation(() => ({
    onStateChange: jest.fn(),
    state: jest.fn().mockResolvedValue('PoweredOn'),
    startDeviceScan: jest.fn(),
    stopDeviceScan: jest.fn(),
    connectToDevice: jest.fn(),
    cancelDeviceConnection: jest.fn(),
  })),
  State: {
    PoweredOn: 'PoweredOn',
    PoweredOff: 'PoweredOff',
  },
}));

// Mock react-native permissions
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
  PermissionsAndroid: {
    requestMultiple: jest.fn(),
    PERMISSIONS: {
      BLUETOOTH_SCAN: 'android.permission.BLUETOOTH_SCAN',
      BLUETOOTH_CONNECT: 'android.permission.BLUETOOTH_CONNECT',
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
    },
    RESULTS: {
      GRANTED: 'granted',
    },
  },
  Alert: {
    alert: jest.fn(),
  },
}));

describe('BLEService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = BLEService;
      const instance2 = BLEService;
      expect(instance1).toBe(instance2);
    });
  });

  describe('isBluetoothEnabled', () => {
    it('should return true when Bluetooth is enabled', async () => {
      const result = await BLEService.isBluetoothEnabled();
      expect(result).toBe(true);
    });
  });

  describe('device discovery', () => {
    it('should identify Brewtool devices correctly', () => {
      const mockBrewtoolDevice = {
        id: 'brewtool-123',
        name: 'Brewtool Device',
        localName: 'BT-001',
        rssi: -50,
        isConnectable: true,
        serviceUUIDs: ['6E400001-B5A3-F393-E0A9-E50E24DCCA9E'],
        manufacturerData: null,
      };

      const mockGenericDevice = {
        id: 'generic-456',
        name: 'Generic Device',
        localName: 'Generic',
        rssi: -60,
        isConnectable: true,
        serviceUUIDs: ['12345678-1234-1234-1234-123456789ABC'],
        manufacturerData: null,
      };

      // Test Brewtool identification
      const brewtoolDiscovered = BLEService['convertToDiscoveredDevice'](mockBrewtoolDevice as any);
      expect(brewtoolDiscovered.isBrewtool).toBe(true);

      // Test generic device identification
      const genericDiscovered = BLEService['convertToDiscoveredDevice'](mockGenericDevice as any);
      expect(genericDiscovered.isBrewtool).toBe(false);
    });
  });

  describe('scan management', () => {
    it('should track scanning status correctly', () => {
      expect(BLEService.getScanningStatus()).toBe(false);
    });

    it('should return discovered devices', () => {
      const devices = BLEService.getDiscoveredDevices();
      expect(Array.isArray(devices)).toBe(true);
    });

    it('should return Brewtool devices only', () => {
      const brewtools = BLEService.getBrewtoolDevices();
      expect(Array.isArray(brewtools)).toBe(true);
    });
  });

  describe('connection management', () => {
    it('should return connected devices', () => {
      const devices = BLEService.getConnectedDevices();
      expect(Array.isArray(devices)).toBe(true);
    });

    it('should check device connection status', () => {
      const isConnected = BLEService.isDeviceConnected('test-device-id');
      expect(typeof isConnected).toBe('boolean');
    });
  });

  describe('listeners', () => {
    it('should add and remove scan listeners', () => {
      const mockListener = jest.fn();
      
      BLEService.addScanListener(mockListener);
      BLEService.removeScanListener(mockListener);
      
      // Should not throw errors
      expect(true).toBe(true);
    });

    it('should add and remove connection listeners', () => {
      const mockListener = jest.fn();
      
      BLEService.addConnectionListener(mockListener);
      BLEService.removeConnectionListener(mockListener);
      
      // Should not throw errors
      expect(true).toBe(true);
    });
  });
});
