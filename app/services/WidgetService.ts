import AsyncStorage from "@react-native-async-storage/async-storage";
import { Brewtool } from "./BrewtoolService";
import { get } from "@/helpers/http";
import WidgetModule from "@/modules/WidgetModule";

export interface WidgetData {
  fermentationName: string;
  currentTemperature: number | null;
  targetTemperature: number | null;
  gravity: number | null;
  lastUpdate: string;
  status: "fermentation" | "coldCrash" | "standby" | "offline";
  deviceId: number | null;
}

export interface WidgetError {
  message: string;
  timestamp: string;
}

const WIDGET_DATA_KEY = "widget_fermentation_data";
const WIDGET_ERROR_KEY = "widget_error_data";
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class WidgetService {
  private static instance: WidgetService;
  private lastFetchTime: number = 0;
  private cachedData: WidgetData | null = null;

  static getInstance(): WidgetService {
    if (!WidgetService.instance) {
      WidgetService.instance = new WidgetService();
    }
    return WidgetService.instance;
  }

  // Get the most active/recent fermentation for widget display
  async getWidgetData(): Promise<WidgetData> {
    try {
      // Check if we have cached data that's still fresh
      const now = Date.now();
      if (this.cachedData && now - this.lastFetchTime < CACHE_DURATION) {
        return this.cachedData;
      }

      // Fetch fresh data from API
      const response = await get("brewtool");
      const brewtools: Brewtool[] = await response.json();

      if (!brewtools || brewtools.length === 0) {
        const noDataResult: WidgetData = {
          fermentationName: "Keine Gärung aktiv",
          currentTemperature: null,
          targetTemperature: null,
          gravity: null,
          lastUpdate: new Date().toISOString(),
          status: "standby",
          deviceId: null,
        };

        await this.saveWidgetData(noDataResult);
        return noDataResult;
      }

      // Find the most relevant brewtool for widget display
      const activeFermentation = this.findMostRelevantFermentation(brewtools);
      const widgetData = this.formatWidgetData(activeFermentation);

      // Cache the data
      this.cachedData = widgetData;
      this.lastFetchTime = now;

      // Save to persistent storage for widget access
      await this.saveWidgetData(widgetData);

      // Update native widgets
      try {
        await WidgetModule.updateWidgetData(widgetData);
      } catch (error) {
        console.error("Failed to update native widget:", error);
      }

      return widgetData;
    } catch (error) {
      console.error("Error fetching widget data:", error);

      // Try to return cached data from storage
      const cachedData = await this.getCachedWidgetData();
      if (cachedData) {
        return cachedData;
      }

      // Save error information
      await this.saveWidgetError({
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });

      // Return error state
      return {
        fermentationName: "Fehler beim Laden",
        currentTemperature: null,
        targetTemperature: null,
        gravity: null,
        lastUpdate: new Date().toISOString(),
        status: "offline",
        deviceId: null,
      };
    }
  }

  private findMostRelevantFermentation(brewtools: Brewtool[]): Brewtool {
    // Priority order:
    // 1. Active fermentation (has config and recent data)
    // 2. Cold crash
    // 3. Most recently updated device
    // 4. First device

    const activeFermentations = brewtools.filter(
      (bt) =>
        bt.config?.temperature &&
        bt.statistic?.updatedAt &&
        this.isRecentData(bt.statistic.updatedAt)
    );

    if (activeFermentations.length > 0) {
      // Sort by most recent update
      return activeFermentations.sort(
        (a, b) =>
          new Date(b.statistic!.updatedAt).getTime() -
          new Date(a.statistic!.updatedAt).getTime()
      )[0];
    }

    // Fallback to most recently updated device
    const devicesWithData = brewtools.filter((bt) => bt.statistic?.updatedAt);
    if (devicesWithData.length > 0) {
      return devicesWithData.sort(
        (a, b) =>
          new Date(b.statistic!.updatedAt).getTime() -
          new Date(a.statistic!.updatedAt).getTime()
      )[0];
    }

    // Last resort: return first device
    return brewtools[0];
  }

  private formatWidgetData(brewtool: Brewtool): WidgetData {
    const status = this.getDeviceStatus(brewtool);
    const fermentationName = this.getFermentationName(brewtool);

    return {
      fermentationName,
      currentTemperature: brewtool.statistic?.temperature || null,
      targetTemperature: brewtool.config?.temperature || null,
      gravity: brewtool.plato || null,
      lastUpdate: brewtool.statistic?.updatedAt || new Date().toISOString(),
      status,
      deviceId: brewtool.id,
    };
  }

  private getFermentationName(brewtool: Brewtool): string {
    if (brewtool.config?.recipeName) {
      return brewtool.config.recipeName;
    }
    if (brewtool.name) {
      return brewtool.name;
    }
    return `Gärung ${brewtool.id}`;
  }

  private getDeviceStatus(brewtool: Brewtool): WidgetData["status"] {
    if (!brewtool.statistic?.updatedAt) {
      return "offline";
    }

    if (!this.isRecentData(brewtool.statistic.updatedAt)) {
      return "offline";
    }

    if (!brewtool.config?.temperature) {
      return "standby";
    }

    if (brewtool.config.temperature === 4) {
      // Cold crash temperature
      return "coldCrash";
    }

    return "fermentation";
  }

  private isRecentData(updatedAt: string | Date): boolean {
    const lastUpdate = new Date(updatedAt);
    const now = new Date();
    const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
    return diffMinutes <= 10; // Consider data recent if within 10 minutes
  }

  // Storage methods for widget access
  private async saveWidgetData(data: WidgetData): Promise<void> {
    try {
      await AsyncStorage.setItem(WIDGET_DATA_KEY, JSON.stringify(data));
    } catch (error) {
      console.error("Error saving widget data:", error);
    }
  }

  private async getCachedWidgetData(): Promise<WidgetData | null> {
    try {
      const data = await AsyncStorage.getItem(WIDGET_DATA_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Error getting cached widget data:", error);
      return null;
    }
  }

  private async saveWidgetError(error: WidgetError): Promise<void> {
    try {
      await AsyncStorage.setItem(WIDGET_ERROR_KEY, JSON.stringify(error));
    } catch (error) {
      console.error("Error saving widget error:", error);
    }
  }

  // Method to force refresh widget data
  async refreshWidgetData(): Promise<WidgetData> {
    this.lastFetchTime = 0; // Force refresh
    this.cachedData = null;
    return await this.getWidgetData();
  }

  // Method to get data specifically for widget (with minimal processing)
  async getWidgetDataForExtension(): Promise<WidgetData | null> {
    try {
      return await this.getCachedWidgetData();
    } catch (error) {
      console.error("Error getting widget data for extension:", error);
      return null;
    }
  }

  // Clear all widget data
  async clearWidgetData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(WIDGET_DATA_KEY);
      await AsyncStorage.removeItem(WIDGET_ERROR_KEY);
      this.cachedData = null;
      this.lastFetchTime = 0;
    } catch (error) {
      console.error("Error clearing widget data:", error);
    }
  }
}

export default WidgetService.getInstance();
