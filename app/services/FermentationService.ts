import type { StatisticValues, Temperature } from "@prisma/client";
import { z } from "zod";

import { post, useGet } from "@/helpers/http";

// Temperature model for fermentation configuration
const TemperatureModel = z.object({
  temperature: z.number(),
  recipeName: z.string().optional(),
  sendPush: z.boolean().optional(),
  endDate: z.date().optional(),
});

export type FermentationConfig = z.infer<typeof TemperatureModel>;

export async function activateFermentation(data: FermentationConfig) {
  return await post("temperature", data);
}

export function useActualTemperature() {
  const temperature = useGet<Temperature | null>("temperature/actual");
  return temperature;
}

export function useActualDeviceTemperature() {
  return useGet<StatisticValues>("statistic/actual");
}

export function useTemperatureStatistic(deviceId: number) {
  const temperature = useGet<StatisticValues[]>(
    `brewtool/${deviceId}/temperature`
  );
  return temperature;
}
