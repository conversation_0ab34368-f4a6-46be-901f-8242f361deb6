import { Device, StatisticValues, Temperature } from "@prisma/client";
import { useMutation } from "@tanstack/react-query";
import { z } from "zod";

import { post, put, useGet } from "@/helpers/http";
import queryClient from "@/helpers/reactQuery";

// Temperature model for brewtool configuration
const TemperatureModel = z.object({
  temperature: z.number(),
  recipeName: z.string().optional(),
  sendPush: z.boolean().optional(),
  endDate: z.date().optional(),
});

export type Brewtool = Omit<Device, "type"> & {
  type: "BREWTOOL";
  statistic?: StatisticValues;
  config?: Temperature;
  deviceConnections: Device[];
  plato?: number;
};

export const useGetBrewtools = () => useGet<Brewtool[]>("brewtool");

export const useGetBrewtool = (deviceId: string) =>
  useGet<Brewtool>(`brewtool/${deviceId}`);

export const createBrewtoolFermentation = () => {
  return useMutation({
    mutationFn: async (data: {
      deviceId: number;
      data: Partial<z.infer<typeof TemperatureModel>>;
    }) => {
      return await post(`brewtool/${data.deviceId}/config`, data.data);
    },
    onSuccess: (_data, vars) => {
      queryClient.refetchQueries({ queryKey: ["brewtool"] });
      queryClient.refetchQueries({ queryKey: [`brewtool/${vars.deviceId}`] });
    },
  });
};

export const updateBrewtoolFermentation = () => {
  return useMutation({
    mutationFn: async (data: {
      deviceId: number;
      data: Partial<z.infer<typeof TemperatureModel>>;
    }) => {
      return await put(`brewtool/${data.deviceId}/config`, data.data);
    },
    onSuccess: (_data, vars) => {
      queryClient.refetchQueries({ queryKey: ["brewtool"] });
      queryClient.refetchQueries({ queryKey: [`brewtool/${vars.deviceId}`] });
    },
  });
};
