{"name": "app", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@tanstack/react-query": "^5.83.0", "expo": "~53.0.20", "expo-apple-authentication": "^7.2.4", "expo-constants": "^17.1.7", "expo-linking": "^7.1.7", "expo-localization": "^16.1.6", "expo-router": "^5.1.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "expo-widgets": "^0.0.0", "i18next": "^25.3.2", "nativewind": "^4.1.23", "react": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-ble-plx": "^3.5.0", "react-native-reanimated": "^4.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-svg": "^15.12.0", "react-native-worklets": "^0.4.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "typescript": "~5.8.3"}, "private": true}