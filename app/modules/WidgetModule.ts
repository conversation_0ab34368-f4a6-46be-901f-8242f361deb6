import { NativeModules, Platform } from 'react-native';
import { WidgetData } from '@/services/WidgetService';

interface WidgetModuleInterface {
  updateWidgetData(data: WidgetData): Promise<void>;
  refreshWidget(): Promise<void>;
  isWidgetSupported(): Promise<boolean>;
}

// Create a mock implementation for development
const createMockWidgetModule = (): WidgetModuleInterface => ({
  updateWidgetData: async (data: WidgetData) => {
    console.log('Mock: Updating widget data:', data);
    
    if (Platform.OS === 'ios') {
      // For iOS, we would use UserDefaults with App Groups
      // This is a placeholder for the native implementation
      console.log('iOS: Would update UserDefaults with app group');
    } else if (Platform.OS === 'android') {
      // For Android, we would use SharedPreferences
      // This is a placeholder for the native implementation
      console.log('Android: Would update SharedPreferences');
    }
  },
  
  refreshWidget: async () => {
    console.log('Mock: Refreshing widget');
  },
  
  isWidgetSupported: async () => {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  },
});

// Try to get the native module, fall back to mock if not available
const WidgetModule: WidgetModuleInterface = 
  NativeModules.WidgetModule || createMockWidgetModule();

export default WidgetModule;
