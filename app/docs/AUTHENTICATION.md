# Authentication System

This document describes the comprehensive authentication system implemented in the mobile app, supporting email/password, Apple ID, and WebAuthn authentication methods.

## Features Overview

### 🔐 **Authentication Methods**
1. **Email/Password**: Traditional username/password authentication
2. **Apple ID**: Native Apple Sign-In integration (iOS only)
3. **WebAuthn/Passkeys**: Modern passwordless authentication using biometrics

### 🛡️ **Security Features**
- Secure token storage using Expo SecureStore with encryption
- Automatic token refresh
- Route protection with authentication guards
- Encrypted local storage for sensitive data

## Architecture

### Services

#### AuthService (`app/services/AuthService.ts`)
Central authentication service handling all auth methods:
- Token management (access/refresh tokens)
- User session management
- Email/password authentication
- Apple ID authentication
- WebAuthn registration and authentication
- Logout functionality

#### SecureStorage (`app/helpers/secureStorage.ts`)
Encrypted storage wrapper for sensitive data:
- AES encryption for stored data
- Token-specific storage helpers
- User data storage helpers
- Secure cleanup functionality

### Context

#### AuthContext (`app/contexts/AuthContext.tsx`)
React context providing authentication state and methods:
- Global authentication state
- Loading states
- Authentication methods
- User information
- Auto-initialization on app start

### Components

#### Authentication Forms
- **LoginForm**: Email/password login with validation
- **RegisterForm**: User registration with password confirmation
- **SocialAuthButtons**: Apple ID authentication button
- **WebAuthnButton**: Passkey authentication interface

#### Route Guards
- **AuthGuard**: Protects authenticated routes
- **GuestGuard**: Redirects authenticated users from auth pages

### Screens

#### AuthScreen (`app/app/auth/index.tsx`)
Main authentication screen with:
- Toggle between login/register modes
- Social authentication options
- WebAuthn/Passkey support
- Responsive mobile design

## Usage

### Basic Authentication Flow

```typescript
import { useAuth } from '@/contexts/AuthContext';

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    login, 
    register, 
    logout 
  } = useAuth();

  const handleLogin = async () => {
    try {
      await login({ email: '<EMAIL>', password: 'password' });
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  if (isLoading) return <LoadingSpinner />;
  if (!isAuthenticated) return <LoginScreen />;
  
  return <AuthenticatedContent user={user} />;
}
```

### Route Protection

```typescript
import AuthGuard from '@/components/auth/AuthGuard';

function ProtectedScreen() {
  return (
    <AuthGuard>
      <YourProtectedContent />
    </AuthGuard>
  );
}
```

### Apple ID Authentication

```typescript
const { loginWithApple } = useAuth();

const handleAppleLogin = async () => {
  try {
    await loginWithApple();
  } catch (error) {
    if (!error.message.includes('canceled')) {
      Alert.alert('Apple Sign-In failed', error.message);
    }
  }
};
```

### WebAuthn/Passkey Authentication

```typescript
const { loginWithWebAuthn, registerWithWebAuthn } = useAuth();

// Register new passkey
const handlePasskeyRegister = async (email: string) => {
  try {
    await registerWithWebAuthn(email);
  } catch (error) {
    Alert.alert('Passkey registration failed', error.message);
  }
};

// Login with passkey
const handlePasskeyLogin = async (email: string) => {
  try {
    await loginWithWebAuthn(email);
  } catch (error) {
    Alert.alert('Passkey login failed', error.message);
  }
};
```

## API Integration

The authentication system expects the following backend endpoints:

### Email/Password Authentication
- `POST /api/auth/login` - Login with email/password
- `POST /api/auth/register` - Register new user
- `POST /api/auth/logout` - Logout user
- `POST /api/auth/refresh` - Refresh access token

### Apple ID Authentication
- `POST /api/auth/apple` - Authenticate with Apple ID token

### WebAuthn Authentication
- `POST /api/auth/webauthn/register/begin` - Start passkey registration
- `POST /api/auth/webauthn/register/finish` - Complete passkey registration
- `POST /api/auth/webauthn/authenticate/begin` - Start passkey authentication
- `POST /api/auth/webauthn/authenticate/finish` - Complete passkey authentication

## Security Considerations

### Token Storage
- Tokens are encrypted using AES encryption before storage
- SecureStore provides hardware-backed security on supported devices
- Automatic cleanup on logout or authentication errors

### WebAuthn Security
- Uses platform authenticators (Face ID, Touch ID, Windows Hello)
- Cryptographic key pairs stored in secure hardware
- Phishing-resistant authentication

### Apple ID Security
- Uses Apple's secure authentication flow
- Identity tokens are verified server-side
- No passwords stored locally

## Configuration

### iOS Configuration (for Apple ID)
Add to `app.json`:
```json
{
  "expo": {
    "ios": {
      "usesAppleSignIn": true
    }
  }
}
```

### WebAuthn Configuration
Ensure HTTPS is used in production for WebAuthn to work properly.

## Error Handling

The authentication system includes comprehensive error handling:
- Network errors
- Invalid credentials
- Token expiration
- Biometric authentication failures
- User cancellation

All errors are properly typed and localized for the German market.

## Localization

Full German localization is provided for:
- Authentication forms and labels
- Error messages
- Success notifications
- Button text and placeholders

## Testing

Unit tests are provided for:
- AuthService functionality
- Token management
- Storage operations
- Authentication state management

Run tests with:
```bash
npm test -- --testPathPattern=auth
```

## Future Enhancements

- Two-factor authentication (2FA)
- Social login with Google/Facebook
- Biometric authentication for app access
- Session management improvements
- Advanced security policies
