# Fermentation Features in Mobile App

This document describes the fermentation features that have been added to the mobile app, ported from the frontend web application.

## Features Overview

### 1. Fermentation Dashboard (`/fermentation`)
- **Location**: `app/app/fermentation/index.tsx`
- **Description**: Main dashboard showing all available brewtools
- **Features**:
  - List of all connected brewtools
  - Device status indicators
  - Quick navigation to device details

### 2. Device Detail View (`/fermentation/device/[deviceId]`)
- **Location**: `app/app/fermentation/device/[deviceId].tsx`
- **Description**: Detailed view of individual brewtool
- **Features**:
  - Real-time temperature monitoring
  - Target temperature display
  - Recipe name display
  - Spindle (gravity sensor) data
  - Control buttons for fermentation actions
  - Statistics and data history
  - Auto-refresh every 30 seconds

### 3. Components

#### DeviceCard
- **Location**: `app/components/fermentation/DeviceCard.tsx`
- **Purpose**: Card component showing device overview
- **Features**: Status badge, temperature info, plato values

#### StatusBadge
- **Location**: `app/components/fermentation/StatusBadge.tsx`
- **Purpose**: Visual status indicator
- **States**: Fermentation, Cold Crash, Standby, Offline

#### TemperatureDisplay
- **Location**: `app/components/fermentation/TemperatureDisplay.tsx`
- **Purpose**: Detailed temperature information
- **Features**: Current temp, target temp, trend indicators, recipe name

#### SpindelDisplay
- **Location**: `app/components/fermentation/SpindelDisplay.tsx`
- **Purpose**: Gravity sensor data display
- **Features**: Current gravity, temperature, battery level, trends

#### StatisticsSection
- **Location**: `app/components/fermentation/StatisticsSection.tsx`
- **Purpose**: Historical data visualization
- **Features**: Table view, graph placeholder, data filtering

#### Modals
- **FermentationModal**: Start new fermentation with recipe name and target temperature
- **PlatoAlertModal**: Set alerts for gravity thresholds

### 4. Services

#### BrewtoolService
- **Location**: `app/services/BrewtoolService.ts`
- **Purpose**: API communication for brewtool devices
- **Functions**:
  - `useGetBrewtools()`: Fetch all brewtools
  - `useGetBrewtool(id)`: Fetch single brewtool
  - `createBrewtoolFermentation()`: Start fermentation
  - `updateBrewtoolFermentation()`: Update fermentation settings

#### FermentationService
- **Location**: `app/services/FermentationService.ts`
- **Purpose**: Fermentation-specific API calls
- **Functions**:
  - `useActualTemperature()`: Current active temperature
  - `useActualDeviceTemperature()`: Device temperature statistics
  - `useTemperatureStatistic(deviceId)`: Historical temperature data

#### SpindelService
- **Location**: `app/services/SpindelService.ts`
- **Purpose**: Spindle/gravity sensor data
- **Functions**:
  - `useGetSpindelStatistic(deviceId)`: Current spindle data
  - `useGetSpindelStatistics(deviceId)`: Historical spindle data

### 5. Hooks

#### useDeviceStatus
- **Location**: `app/hooks/useDeviceStatus.ts`
- **Purpose**: Determine device operational status
- **Returns**: 'fermentation' | 'coldCrash' | 'standby' | 'offline'

#### useCalculateDataForLastHour
- **Location**: `app/hooks/useCalculateDataForLastHour.ts`
- **Purpose**: Calculate data trends over the last hour
- **Returns**: Change type and magnitude for temperature/gravity

### 6. Navigation

The fermentation features are integrated into the app's navigation:
- Main dashboard accessible via `/fermentation`
- Device details via `/fermentation/device/[deviceId]`
- Navigation configured in `app/app/_layout.tsx`

### 7. Localization

All fermentation features are fully localized with German translations in `app/locales/de.json`:
- Device status messages
- UI labels and buttons
- Error messages and placeholders

## Usage

1. **Starting Fermentation**:
   - Navigate to device detail view
   - Tap "Gärung starten" button
   - Enter recipe name and target temperature
   - Confirm to start fermentation

2. **Monitoring**:
   - View real-time temperature data
   - Check spindle gravity readings
   - Monitor trends and statistics
   - Receive visual status indicators

3. **Control Actions**:
   - Cold crash: Set temperature to 4°C
   - Turn off: End current fermentation
   - Set alerts: Configure plato thresholds

## Technical Notes

- All components use gluestack-ui for consistent styling
- React Query for efficient data fetching and caching
- TypeScript for type safety
- Responsive design for mobile devices
- Error handling and loading states
- Auto-refresh functionality for real-time updates

## Future Enhancements

- Chart visualization with react-native-chart-kit
- Push notifications for alerts
- Offline data caching
- Export functionality for statistics
- Advanced filtering and search
