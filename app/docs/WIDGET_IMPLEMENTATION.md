# Widget Implementation Guide

This document describes the comprehensive widget implementation for both iOS and Android platforms, allowing users to view fermentation status directly from their home screen.

## Overview

The widget system provides real-time fermentation information including:
- **Fermentation Name**: Current recipe or device name
- **Current Temperature**: Live temperature reading
- **Target Temperature**: Configured fermentation temperature
- **Gravity**: Current plato/gravity reading
- **Status**: Fermentation state (active, cold crash, standby, offline)
- **Last Update**: When data was last refreshed

## Architecture

### Core Components

#### WidgetService (`services/WidgetService.ts`)
Central service managing widget data:
- **Data Fetching**: Retrieves fermentation data from API
- **Data Processing**: Formats data for widget display
- **Caching**: Implements intelligent caching to reduce API calls
- **Error Handling**: Graceful degradation when data unavailable
- **Device Selection**: Automatically selects most relevant fermentation

#### WidgetProvider (`providers/WidgetProvider.tsx`)
React context providing widget state management:
- **Global State**: Manages widget data across the app
- **Auto-refresh**: Periodic data updates every 5 minutes
- **App State Handling**: Refreshes when app becomes active
- **Authentication Integration**: Clears data on logout

#### Native Modules
Platform-specific implementations for widget updates:
- **iOS**: `WidgetModule.swift` - Uses UserDefaults with App Groups
- **Android**: `WidgetModule.kt` - Uses SharedPreferences

### Widget Implementations

#### iOS Widget (`ios/FermentationWidget/FermentationWidget.swift`)
SwiftUI-based widget with multiple sizes:
- **Small Widget**: Compact view with essential info
- **Medium Widget**: Detailed view with temperature and gravity
- **Large Widget**: Full information display with status

Features:
- **WidgetKit Integration**: Native iOS widget framework
- **App Groups**: Secure data sharing between app and widget
- **Timeline Updates**: Automatic refresh every 5 minutes
- **Dynamic Content**: Real-time data display

#### Android Widget (`android/app/src/main/java/com/gaerschrank/widget/`)
Native Android App Widget implementation:
- **Widget Provider**: `FermentationWidgetProvider.kt`
- **Layout**: Custom XML layout with responsive design
- **Data Storage**: SharedPreferences for data persistence
- **Update Mechanism**: Broadcast-based updates

## Data Flow

### 1. Data Collection
```typescript
// WidgetService fetches data from API
const brewtools = await get('brewtool');
const relevantDevice = findMostRelevantFermentation(brewtools);
const widgetData = formatWidgetData(relevantDevice);
```

### 2. Data Processing
```typescript
// Format data for widget consumption
const widgetData: WidgetData = {
  fermentationName: device.config?.recipeName || device.name,
  currentTemperature: device.statistic?.temperature,
  targetTemperature: device.config?.temperature,
  gravity: device.plato,
  lastUpdate: device.statistic?.updatedAt,
  status: calculateDeviceStatus(device),
  deviceId: device.id
};
```

### 3. Native Update
```typescript
// Update platform-specific widget
await WidgetModule.updateWidgetData(widgetData);
```

### 4. Widget Display
- **iOS**: UserDefaults → WidgetKit → SwiftUI rendering
- **Android**: SharedPreferences → RemoteViews → Widget display

## Usage

### Automatic Updates
Widgets automatically update when:
- App becomes active
- Fermentation data changes
- Every 5 minutes (background refresh)
- User manually refreshes

### Manual Integration
```typescript
import { useWidgetDataSync } from '@/hooks/useWidgetUpdate';

// In fermentation screens
const MyFermentationScreen = () => {
  const { data } = useGetBrewtool(deviceId);
  
  // Automatically sync widget when data changes
  useWidgetDataSync([data?.statistic, data?.config]);
  
  return <YourComponent />;
};
```

## Configuration

### iOS Setup
1. **App Groups**: Configure in Apple Developer Console
2. **Entitlements**: Add to `app.json`:
```json
{
  "ios": {
    "entitlements": {
      "com.apple.security.application-groups": [
        "group.gaerschrank.widget"
      ]
    }
  }
}
```

### Android Setup
1. **Permissions**: Add to `app.json`:
```json
{
  "android": {
    "permissions": [
      "android.permission.BIND_APPWIDGET"
    ]
  }
}
```

2. **Widget Declaration**: Add to `AndroidManifest.xml`:
```xml
<receiver android:name=".widget.FermentationWidgetProvider">
  <intent-filter>
    <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
  </intent-filter>
  <meta-data
    android:name="android.appwidget.provider"
    android:resource="@xml/fermentation_widget_info" />
</receiver>
```

## Widget Sizes and Layouts

### iOS Widget Families
- **systemSmall**: 2x2 grid, essential info only
- **systemMedium**: 4x2 grid, detailed temperature and gravity
- **systemLarge**: 4x4 grid, full information display

### Android Widget Sizes
- **Minimum**: 250dp x 110dp (4x2 cells)
- **Resizable**: Horizontal and vertical
- **Responsive**: Adapts to different screen densities

## Localization

Full German localization provided:
- Widget titles and descriptions
- Status messages
- Temperature and gravity labels
- Error states and offline messages

### Localization Keys
```json
{
  "widget": {
    "name": "Gärschrank Widget",
    "description": "Zeigt den aktuellen Status Ihrer Gärung an",
    "fermentationActive": "Gärung aktiv",
    "coldCrash": "Cold Crash",
    "offline": "Offline"
  }
}
```

## Error Handling

### Network Errors
- Displays cached data when available
- Shows "Offline" status when no data
- Graceful degradation with error messages

### Authentication Errors
- Clears widget data on logout
- Shows "Please open app" message
- Handles token expiration

### Data Validation
- Validates all numeric values
- Handles missing or null data
- Provides fallback values

## Performance Optimization

### Caching Strategy
- **5-minute cache**: Reduces API calls
- **Persistent storage**: Survives app restarts
- **Smart refresh**: Only updates when necessary

### Battery Optimization
- **Minimal background activity**: Updates only when needed
- **Efficient data transfer**: Compressed JSON data
- **Platform-optimized**: Uses native update mechanisms

## Testing

### Manual Testing
1. **Widget Installation**: Add widget to home screen
2. **Data Updates**: Verify real-time updates
3. **Error States**: Test offline scenarios
4. **Authentication**: Test login/logout behavior

### Automated Testing
```typescript
// Test widget data formatting
describe('WidgetService', () => {
  it('should format widget data correctly', () => {
    const mockDevice = createMockBrewtool();
    const widgetData = WidgetService.formatWidgetData(mockDevice);
    expect(widgetData.fermentationName).toBeDefined();
  });
});
```

## Troubleshooting

### Common Issues
1. **Widget not updating**: Check app group configuration (iOS) or permissions (Android)
2. **No data displayed**: Verify authentication and API connectivity
3. **Incorrect information**: Check device selection logic
4. **Performance issues**: Review caching and update frequency

### Debug Information
- Enable console logging in WidgetService
- Check native module bridge connectivity
- Verify data persistence in storage

## Future Enhancements

### Planned Features
- **Multiple widget sizes**: Additional layout options
- **Interactive widgets**: Tap actions for quick controls
- **Customization**: User-selectable data fields
- **Charts**: Mini temperature/gravity graphs

### Technical Improvements
- **Background sync**: More efficient data updates
- **Push notifications**: Server-triggered updates
- **Offline support**: Enhanced caching strategies
- **Analytics**: Widget usage tracking

This widget implementation provides users with convenient access to their fermentation data directly from their device's home screen, enhancing the overall user experience and engagement with the application.
