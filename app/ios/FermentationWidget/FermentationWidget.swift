import WidgetKit
import SwiftUI

struct FermentationEntry: TimelineEntry {
    let date: Date
    let fermentationName: String
    let currentTemperature: Double?
    let targetTemperature: Double?
    let gravity: Double?
    let lastUpdate: String
    let status: String
    let isError: Bool
}

struct FermentationProvider: TimelineProvider {
    func placeholder(in context: Context) -> FermentationEntry {
        FermentationEntry(
            date: Date(),
            fermentationName: "Weissbier",
            currentTemperature: 19.2,
            targetTemperature: 19.0,
            gravity: 12.5,
            lastUpdate: "vor 2 Min",
            status: "fermentation",
            isError: false
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (FermentationEntry) -> ()) {
        let entry = FermentationEntry(
            date: Date(),
            fermentationName: "Weissbier",
            currentTemperature: 19.2,
            targetTemperature: 19.0,
            gravity: 12.5,
            lastUpdate: "vor 2 Min",
            status: "fermentation",
            isError: false
        )
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let currentDate = Date()
        let entry = loadFermentationData()
        
        // Update every 5 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func loadFermentationData() -> FermentationEntry {
        // Try to load data from UserDefaults (shared with main app)
        let userDefaults = UserDefaults(suiteName: "group.gaerschrank.widget")
        
        guard let data = userDefaults?.data(forKey: "widget_fermentation_data"),
              let widgetData = try? JSONDecoder().decode(WidgetData.self, from: data) else {
            return FermentationEntry(
                date: Date(),
                fermentationName: "Keine Daten",
                currentTemperature: nil,
                targetTemperature: nil,
                gravity: nil,
                lastUpdate: "Unbekannt",
                status: "offline",
                isError: true
            )
        }
        
        return FermentationEntry(
            date: Date(),
            fermentationName: widgetData.fermentationName,
            currentTemperature: widgetData.currentTemperature,
            targetTemperature: widgetData.targetTemperature,
            gravity: widgetData.gravity,
            lastUpdate: formatLastUpdate(widgetData.lastUpdate),
            status: widgetData.status,
            isError: false
        )
    }
    
    private func formatLastUpdate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        guard let date = formatter.date(from: dateString) else {
            return "Unbekannt"
        }
        
        let now = Date()
        let interval = now.timeIntervalSince(date)
        
        if interval < 60 {
            return "vor \(Int(interval))s"
        } else if interval < 3600 {
            return "vor \(Int(interval / 60)) Min"
        } else if interval < 86400 {
            return "vor \(Int(interval / 3600))h"
        } else {
            return "vor \(Int(interval / 86400))d"
        }
    }
}

struct WidgetData: Codable {
    let fermentationName: String
    let currentTemperature: Double?
    let targetTemperature: Double?
    let gravity: Double?
    let lastUpdate: String
    let status: String
    let deviceId: Int?
}

struct FermentationWidgetEntryView: View {
    var entry: FermentationProvider.Entry
    @Environment(\.widgetFamily) var family

    var body: some View {
        switch family {
        case .systemSmall:
            SmallWidgetView(entry: entry)
        case .systemMedium:
            MediumWidgetView(entry: entry)
        case .systemLarge:
            LargeWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

struct SmallWidgetView: View {
    let entry: FermentationEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Image(systemName: statusIcon)
                    .foregroundColor(statusColor)
                    .font(.caption)
                Spacer()
                Text(entry.lastUpdate)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Text(entry.fermentationName)
                .font(.headline)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            if let temp = entry.currentTemperature {
                HStack {
                    Image(systemName: "thermometer")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("\(temp, specifier: "%.1f")°C")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            if let gravity = entry.gravity {
                HStack {
                    Image(systemName: "drop.fill")
                        .foregroundColor(.blue)
                        .font(.caption)
                    Text("\(gravity, specifier: "%.1f")°P")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var statusIcon: String {
        switch entry.status {
        case "fermentation":
            return "flask.fill"
        case "coldCrash":
            return "snowflake"
        case "offline":
            return "wifi.slash"
        default:
            return "pause.circle"
        }
    }
    
    private var statusColor: Color {
        switch entry.status {
        case "fermentation":
            return .green
        case "coldCrash":
            return .blue
        case "offline":
            return .red
        default:
            return .gray
        }
    }
}

struct MediumWidgetView: View {
    let entry: FermentationEntry
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Image(systemName: statusIcon)
                        .foregroundColor(statusColor)
                        .font(.title2)
                    Text(entry.fermentationName)
                        .font(.headline)
                        .lineLimit(1)
                    Spacer()
                }
                
                if let temp = entry.currentTemperature {
                    HStack {
                        Image(systemName: "thermometer")
                            .foregroundColor(.orange)
                        Text("Aktuell: \(temp, specifier: "%.1f")°C")
                            .font(.subheadline)
                    }
                }
                
                if let target = entry.targetTemperature {
                    HStack {
                        Image(systemName: "target")
                            .foregroundColor(.orange)
                        Text("Ziel: \(target, specifier: "%.1f")°C")
                            .font(.subheadline)
                    }
                }
                
                Spacer()
                
                Text("Aktualisiert \(entry.lastUpdate)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            VStack(alignment: .trailing, spacing: 8) {
                if let gravity = entry.gravity {
                    VStack(alignment: .center) {
                        Image(systemName: "drop.fill")
                            .foregroundColor(.blue)
                            .font(.title2)
                        Text("\(gravity, specifier: "%.1f")°P")
                            .font(.title3)
                            .fontWeight(.semibold)
                        Text("Stammwürze")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var statusIcon: String {
        switch entry.status {
        case "fermentation":
            return "flask.fill"
        case "coldCrash":
            return "snowflake"
        case "offline":
            return "wifi.slash"
        default:
            return "pause.circle"
        }
    }
    
    private var statusColor: Color {
        switch entry.status {
        case "fermentation":
            return .green
        case "coldCrash":
            return .blue
        case "offline":
            return .red
        default:
            return .gray
        }
    }
}

struct LargeWidgetView: View {
    let entry: FermentationEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: statusIcon)
                    .foregroundColor(statusColor)
                    .font(.title)
                Text(entry.fermentationName)
                    .font(.title2)
                    .fontWeight(.semibold)
                Spacer()
                Text(entry.lastUpdate)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack(spacing: 20) {
                if let temp = entry.currentTemperature {
                    VStack(alignment: .leading) {
                        HStack {
                            Image(systemName: "thermometer")
                                .foregroundColor(.orange)
                            Text("Temperatur")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Text("\(temp, specifier: "%.1f")°C")
                            .font(.title)
                            .fontWeight(.semibold)
                        
                        if let target = entry.targetTemperature {
                            Text("Ziel: \(target, specifier: "%.1f")°C")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                if let gravity = entry.gravity {
                    VStack(alignment: .leading) {
                        HStack {
                            Image(systemName: "drop.fill")
                                .foregroundColor(.blue)
                            Text("Stammwürze")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Text("\(gravity, specifier: "%.1f")°P")
                            .font(.title)
                            .fontWeight(.semibold)
                    }
                }
            }
            
            Spacer()
            
            HStack {
                Text(statusText)
                    .font(.subheadline)
                    .foregroundColor(statusColor)
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var statusIcon: String {
        switch entry.status {
        case "fermentation":
            return "flask.fill"
        case "coldCrash":
            return "snowflake"
        case "offline":
            return "wifi.slash"
        default:
            return "pause.circle"
        }
    }
    
    private var statusColor: Color {
        switch entry.status {
        case "fermentation":
            return .green
        case "coldCrash":
            return .blue
        case "offline":
            return .red
        default:
            return .gray
        }
    }
    
    private var statusText: String {
        switch entry.status {
        case "fermentation":
            return "Gärung aktiv"
        case "coldCrash":
            return "Cold Crash"
        case "offline":
            return "Offline"
        default:
            return "Bereitschaft"
        }
    }
}

struct FermentationWidget: Widget {
    let kind: String = "FermentationWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: FermentationProvider()) { entry in
            FermentationWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Gärschrank")
        .description("Zeigt den aktuellen Status Ihrer Gärung an.")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

struct FermentationWidget_Previews: PreviewProvider {
    static var previews: some View {
        FermentationWidgetEntryView(entry: FermentationEntry(
            date: Date(),
            fermentationName: "Weissbier",
            currentTemperature: 19.2,
            targetTemperature: 19.0,
            gravity: 12.5,
            lastUpdate: "vor 2 Min",
            status: "fermentation",
            isError: false
        ))
        .previewContext(WidgetPreviewContext(family: .systemMedium))
    }
}
