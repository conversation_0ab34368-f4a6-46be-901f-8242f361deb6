import React, { createContext, useContext, useEffect, useState, ReactNode } from "react";
import AuthService, { User, LoginCredentials, RegisterCredentials } from "@/services/AuthService";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  loginWithApple: () => Promise<void>;
  loginWithWebAuthn: (email: string) => Promise<void>;
  registerWithWebAuthn: (email: string) => Promise<void>;
  logout: () => Promise<void>;
  isWebAuthnAvailable: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize authentication state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      const authenticated = await AuthService.isAuthenticated();
      
      if (authenticated) {
        const userData = await AuthService.getUser();
        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Auth initialization error:", error);
      // Clear any corrupted auth data
      await AuthService.clearTokens();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      const { user: userData } = await AuthService.loginWithEmail(credentials);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      setIsLoading(true);
      const { user: userData } = await AuthService.registerWithEmail(credentials);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithApple = async () => {
    try {
      setIsLoading(true);
      const { user: userData } = await AuthService.loginWithApple();
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("Apple login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithWebAuthn = async (email: string) => {
    try {
      setIsLoading(true);
      const { user: userData } = await AuthService.loginWithWebAuthn(email);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("WebAuthn login error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const registerWithWebAuthn = async (email: string) => {
    try {
      setIsLoading(true);
      const { user: userData } = await AuthService.registerWithWebAuthn(email);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("WebAuthn registration error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await AuthService.logout();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error("Logout error:", error);
      // Even if logout fails on server, clear local state
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const isWebAuthnAvailable = async (): Promise<boolean> => {
    return await AuthService.isWebAuthnAvailable();
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    loginWithApple,
    loginWithWebAuthn,
    registerWithWebAuthn,
    logout,
    isWebAuthnAvailable,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
