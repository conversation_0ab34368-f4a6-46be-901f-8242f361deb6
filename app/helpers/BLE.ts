import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "react-native-ble-plx";
import bleData from "@/assets/ble.json";
import { decode, encode } from "base-64";

const deviceName = bleData.name;
const serviceUUID = bleData.services[0].uuid;
const wifiSSIDUUID = bleData.services[0].characteristics[0].uuid;
const wifiPasswordDUUID = bleData.services[0].characteristics[0].uuid;

export const bleManager = new BleManager({
  // restoreStateIdentifier: "BleInTheBackground",
  // restoreStateFunction: (restoredState) => {
  //   if (restoredState == null) {
  //     // BleManager was constructed for the first time.
  //   } else {
  //     // BleManager was restored. Check `restoredState.connectedPeripherals` property.
  //   }
  // },
});

// let deviceId: string | null = null; // TODO: multiple devices TODO: store in zustand

let bleError: BleError | null = null;

export const getBleError = () => bleError;

export const scanForBrewtools = async () => {
  const devices: Device[] = [];

  bleError = null;

  await bleManager.startDeviceScan([serviceUUID], null, (error, device) => {
    if (error) {
      bleError = error;
      // console.error("BLE Error", error);
      return;
    }

    if (!device) {
      return;
    }

    devices.push(device);

    if (device?.name === deviceName) {
      console.log("Found Brewtools", device);
    }
  });

  devices.push({
    id: "1",
    name: "Brewtools",
    rssi: -50,
    serviceUUIDs: [serviceUUID],
  } as Device);

  return devices;
};

const setCharacteristic = async (
  deviceId: string,
  value: string,
  characteristicUUID: string
) => {
  return bleManager.writeCharacteristicWithResponseForDevice(
    deviceId,
    serviceUUID,
    characteristicUUID,
    encode(value)
    // transactionId: ?TransactionId
  );
};

export const setWifiName = async (deviceId: string, ssid: string) => {
  setCharacteristic(deviceId, ssid, wifiSSIDUUID);
};

export const setWifiPassword = async (deviceId: string, password: string) => {
  setCharacteristic(deviceId, password, wifiPasswordDUUID);
};

const getCharacteristic = async (
  deviceId: string,
  characteristicUUID: string
) => {
  return await bleManager.readCharacteristicForDevice(
    deviceId,
    serviceUUID,
    characteristicUUID
  );
};

export const getWifiName = async (deviceId: string) => {
  return getCharacteristic(deviceId, wifiSSIDUUID);
};

export const getWifiPassword = async (deviceId: string) => {
  return getCharacteristic(deviceId, wifiPasswordDUUID);
};
