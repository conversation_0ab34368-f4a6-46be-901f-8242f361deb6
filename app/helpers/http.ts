import { useQuery } from "@tanstack/react-query";
import { API_URL } from "./constants";

export function useGet<T>(route: string, key: string[] = []) {
  return useQuery<T>({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: [route].concat(key),
    queryFn: async () => {
      return (await get(route)) as T;
    },
  });
}

export async function send(method: "GET" | "DELETE", route: string) {
  const res = await fetch(API_URL + "/api/" + route, {
    method,
  });

  if (res.status === 404) {
    return null;
  }

  if (!res.ok) {
    console.error(`Error handling for GET ${route}`, res);
    throw new Error("Network response was not ok");
  }

  return res;
}

export async function get<T>(route: string) {
  const res = await send("GET", route);

  return ((await res?.json()) as T) ?? null;
}

export async function del(route: string) {
  return await send("DELETE", route);
}

export async function sendData(
  method: "POST" | "DELETE" | "PUT",
  route: string,
  data?: Record<string, unknown>
) {
  const res = await fetch(API_URL + "/api/" + route, {
    method,
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });

  if (res.status === 200) {
    return res;
  } else {
    console.error(`Error handling for POST ${route}`, res);
    throw new Error("Http Post Call failed");
  }
}

export async function post(route: string, data?: Record<string, unknown>) {
  return sendData("POST", route, data);
}

export async function put(route: string, data: Record<string, unknown>) {
  return sendData("PUT", route, data);
}
