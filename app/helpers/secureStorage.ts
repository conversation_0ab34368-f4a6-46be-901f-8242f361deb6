import * as SecureStore from "expo-secure-store";
import Crypt<PERSON><PERSON><PERSON> from "crypto-js";

// Encryption key for additional security (in production, this should be more secure)
const ENCRYPTION_KEY = "gaerschrank_secure_key_2024";

export class SecureStorage {
  private static encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
  }

  private static decrypt(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  static async setItem(key: string, value: string): Promise<void> {
    try {
      const encryptedValue = this.encrypt(value);
      await SecureStore.setItemAsync(key, encryptedValue);
    } catch (error) {
      console.error(`Error storing secure item ${key}:`, error);
      throw new Error(`Failed to store secure item: ${key}`);
    }
  }

  static async getItem(key: string): Promise<string | null> {
    try {
      const encryptedValue = await SecureStore.getItemAsync(key);
      if (!encryptedValue) return null;
      
      return this.decrypt(encryptedValue);
    } catch (error) {
      console.error(`Error retrieving secure item ${key}:`, error);
      return null;
    }
  }

  static async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error(`Error removing secure item ${key}:`, error);
      throw new Error(`Failed to remove secure item: ${key}`);
    }
  }

  static async setObject<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.setItem(key, jsonString);
    } catch (error) {
      console.error(`Error storing secure object ${key}:`, error);
      throw new Error(`Failed to store secure object: ${key}`);
    }
  }

  static async getObject<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.getItem(key);
      if (!jsonString) return null;
      
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Error retrieving secure object ${key}:`, error);
      return null;
    }
  }

  static async clear(): Promise<void> {
    try {
      // Get all keys and remove them
      const keys = ["auth_tokens", "auth_user", "auth_refresh_token"];
      await Promise.all(keys.map(key => this.removeItem(key)));
    } catch (error) {
      console.error("Error clearing secure storage:", error);
      throw new Error("Failed to clear secure storage");
    }
  }

  static async isAvailable(): Promise<boolean> {
    try {
      return await SecureStore.isAvailableAsync();
    } catch (error) {
      console.error("Error checking SecureStore availability:", error);
      return false;
    }
  }
}

// Token-specific storage helpers
export const TokenStorage = {
  async setTokens(tokens: { accessToken: string; refreshToken: string }): Promise<void> {
    await SecureStorage.setObject("auth_tokens", tokens);
  },

  async getTokens(): Promise<{ accessToken: string; refreshToken: string } | null> {
    return await SecureStorage.getObject("auth_tokens");
  },

  async clearTokens(): Promise<void> {
    await SecureStorage.removeItem("auth_tokens");
  },

  async setRefreshToken(refreshToken: string): Promise<void> {
    await SecureStorage.setItem("auth_refresh_token", refreshToken);
  },

  async getRefreshToken(): Promise<string | null> {
    return await SecureStorage.getItem("auth_refresh_token");
  },

  async clearRefreshToken(): Promise<void> {
    await SecureStorage.removeItem("auth_refresh_token");
  },
};

// User-specific storage helpers
export const UserStorage = {
  async setUser(user: any): Promise<void> {
    await SecureStorage.setObject("auth_user", user);
  },

  async getUser(): Promise<any | null> {
    return await SecureStorage.getObject("auth_user");
  },

  async clearUser(): Promise<void> {
    await SecureStorage.removeItem("auth_user");
  },
};
