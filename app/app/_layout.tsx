import { Stack } from "expo-router";
import { useTranslation } from "react-i18next";
import { QueryClientProvider } from "@tanstack/react-query";
import queryClient from "@/helpers/reactQuery";
import { AuthProvider } from "@/contexts/AuthContext";
import { WidgetProvider } from "@/providers/WidgetProvider";
import "@/helpers/i18n";

export default function RootLayout() {
  const { t } = useTranslation();

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <WidgetProvider>
          <Stack>
            <Stack.Screen
              name="index"
              options={{ headerShown: true, title: t("app.home") }}
            />
            <Stack.Screen
              name="auth/index"
              options={{
                headerShown: false,
                title: t("auth.authentication"),
              }}
            />
            <Stack.Screen
              name="fermentation/index"
              options={{ headerShown: true, title: t("fermentation.title") }}
            />
            <Stack.Screen
              name="fermentation/device/[deviceId]"
              options={{
                headerShown: false,
                title: t("fermentation.deviceDetail"),
              }}
            />
            <Stack.Screen
              name="device/index"
              options={{
                headerShown: true,
                title: t("device.management"),
              }}
            />
            <Stack.Screen name="+not-found" />
          </Stack>
        </WidgetProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}
