import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocalSearchParams, router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { ScrollView, RefreshControl } from "react-native";
import { Box } from "@/components/ui/box";
import { Button, ButtonText } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { HStack } from "@/components/ui/hstack";
import { VStack } from "@/components/ui/vstack";
import {
  useGetBrewtool,
  createBrewtoolFermentation,
  updateBrewtoolFermentation,
} from "@/services/BrewtoolService";
import useDeviceStatus, { COLD_CRUSH } from "@/hooks/useDeviceStatus";
import TemperatureDisplay from "@/components/fermentation/TemperatureDisplay";
import StatusBadge from "@/components/fermentation/StatusBadge";
import SpindelDisplay from "@/components/fermentation/SpindelDisplay";
import StatisticsSection from "@/components/fermentation/StatisticsSection";
import FermentationModal from "@/components/fermentation/FermentationModal";
import PlatoAlertModal from "@/components/fermentation/PlatoAlertModal";
import AuthGuard from "@/components/auth/AuthGuard";
import { useWidgetDataSync } from "@/hooks/useWidgetUpdate";
// import { ArrowLeftIcon } from "react-native-heroicons/solid";

const DeviceView = () => {
  const { t } = useTranslation();
  const { deviceId } = useLocalSearchParams<{ deviceId: string }>();
  const { data: device, refetch, isLoading } = useGetBrewtool(deviceId ?? "");
  const [refreshing, setRefreshing] = useState(false);
  const [showFermentationModal, setShowFermentationModal] = useState(false);
  const [showPlatoAlertModal, setShowPlatoAlertModal] = useState(false);

  const { mutate } = createBrewtoolFermentation();
  const { mutate: update } = updateBrewtoolFermentation();

  // Create a default device object for the hook
  const defaultDevice = {
    id: 0,
    name: "",
    type: "BREWTOOL" as const,
    statistic: undefined,
    config: undefined,
    deviceConnections: [],
  };

  const status = useDeviceStatus(device || defaultDevice);

  // Sync widget data when device data changes
  useWidgetDataSync([device?.statistic, device?.config]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const intervalId = setInterval(() => {
      refetch();
    }, 30000);
    return () => clearInterval(intervalId);
  }, [refetch]);

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const handleStartFermentation = () => {
    setShowFermentationModal(true);
  };

  const handleFermentationSubmit = (data: {
    title: string;
    targetTemp: string;
  }) => {
    if (!device?.id) return;

    mutate({
      deviceId: device.id,
      data: {
        recipeName: data.title || undefined,
        temperature: parseFloat(data.targetTemp),
        sendPush: false,
      },
    });
  };

  const handlePlatoAlert = () => {
    setShowPlatoAlertModal(true);
  };

  const handlePlatoAlertSubmit = (threshold: number) => {
    console.log(`Alert set for when Plato reaches ${threshold}`);
  };

  const handleColdCrash = () => {
    if (!device?.id) return;

    mutate({
      deviceId: device.id,
      data: {
        temperature: COLD_CRUSH,
        sendPush: false,
      },
    });
  };

  const handleTurnOff = () => {
    if (!device?.id) return;

    update({
      deviceId: device.id,
      data: {
        endDate: new Date(),
      },
    });
  };

  if (isLoading || !device) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <VStack className="flex-1 justify-center items-center">
          <Text className="text-xl text-gray-700">
            {t("fermentation.deviceNotFound")}
          </Text>
          <Button className="mt-4" onPress={() => router.back()}>
            <HStack space="sm" className="items-center">
              {/* <ArrowLeftIcon size={16} color="white" /> */}
              <ButtonText>{t("fermentation.backToDashboard")}</ButtonText>
            </HStack>
          </Button>
        </VStack>
      </SafeAreaView>
    );
  }

  return (
    <AuthGuard>
      <SafeAreaView className="flex-1 bg-gray-50">
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <VStack className="flex-1">
            {/* Header */}
            <Box className="px-6 py-4 bg-white border-b border-gray-200">
              <HStack className="items-center justify-between mb-2">
                <Button
                  variant="outline"
                  size="sm"
                  onPress={() => router.back()}
                >
                  <HStack space="xs" className="items-center">
                    {/* <ArrowLeftIcon size={16} color="#6b7280" /> */}
                    <ButtonText>{t("fermentation.back")}</ButtonText>
                  </HStack>
                </Button>
              </HStack>
              <HStack className="items-center justify-between">
                <Heading size="xl" className="text-gray-800 flex-1">
                  {device.name || t("fermentation.unnamedDevice")}
                </Heading>
                <StatusBadge status={status} large />
              </HStack>
            </Box>

            {/* Content */}
            <Box className="p-6">
              <VStack space="lg">
                {/* Temperature Display */}
                <TemperatureDisplay brewtool={device} />

                {/* Spindle Display */}
                <SpindelDisplay
                  spindle={device.deviceConnections.find(
                    (d) => d.type === "ISPINDEL"
                  )}
                  onConnectSpindle={() => console.log("Connect spindle")}
                />

                {/* Action Buttons */}
                <VStack space="md">
                  {!device.config?.temperature && (
                    <Button
                      className="bg-green-600"
                      onPress={handleStartFermentation}
                    >
                      <ButtonText>
                        {t("fermentation.startFermentation")}
                      </ButtonText>
                    </Button>
                  )}

                  {device.config?.temperature &&
                    device.config.temperature !== COLD_CRUSH && (
                      <Button className="bg-blue-600" onPress={handleColdCrash}>
                        <ButtonText>{t("fermentation.coldCrash")}</ButtonText>
                      </Button>
                    )}

                  {device.config?.temperature && (
                    <Button variant="outline" onPress={handleTurnOff}>
                      <ButtonText>{t("fermentation.turnOff")}</ButtonText>
                    </Button>
                  )}

                  <Button variant="outline" onPress={handlePlatoAlert}>
                    <ButtonText>{t("fermentation.setPlatoAlert")}</ButtonText>
                  </Button>
                </VStack>

                {/* Statistics Section */}
                <StatisticsSection device={device} />
              </VStack>
            </Box>
          </VStack>
        </ScrollView>

        {/* Modals */}
        <FermentationModal
          isOpen={showFermentationModal}
          onClose={() => setShowFermentationModal(false)}
          onSubmit={handleFermentationSubmit}
          initialTitle=""
        />

        <PlatoAlertModal
          isOpen={showPlatoAlertModal}
          onClose={() => setShowPlatoAlertModal(false)}
          onSubmit={handlePlatoAlertSubmit}
          currentPlato={device?.plato}
        />
      </SafeAreaView>
    </AuthGuard>
  );
};

export default DeviceView;
