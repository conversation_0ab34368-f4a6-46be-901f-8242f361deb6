import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Stack } from 'expo-router';
import { useQueryClient } from '@tanstack/react-query';
import AuthGuard from '@/components/auth/AuthGuard';
import DeviceListComponent from '@/components/ble/DeviceListComponent';
import DeviceSearchComponent from '@/components/ble/DeviceSearchComponent';
import BrewtoolScanner from '@/components/ble/BrewtoolScanner';
import BLEService, { DiscoveredDevice } from '@/services/BLEService';

type TabType = 'myDevices' | 'discover' | 'brewtools';

export default function DeviceScreen() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<TabType>('myDevices');
  const [isScanning, setIsScanning] = useState(false);

  // Handle device connection
  const handleDeviceConnect = useCallback(async (device: DiscoveredDevice) => {
    try {
      await BLEService.connectToDevice(device.id);
      Alert.alert(
        t('ble.connectionSuccess'),
        t('ble.deviceConnected', { name: device.name || t('ble.unknownDevice') })
      );
    } catch (error) {
      Alert.alert(
        t('ble.connectionError'),
        error instanceof Error ? error.message : t('ble.unknownError')
      );
    }
  }, [t]);

  // Handle device removal
  const handleDeviceRemove = useCallback(() => {
    // Refresh the device list after removal
    queryClient.invalidateQueries({ queryKey: ['userDevices'] });
  }, [queryClient]);

  // Handle Brewtool selection
  const handleBrewtoolSelect = useCallback(async (device: DiscoveredDevice) => {
    try {
      await BLEService.connectToDevice(device.id);
      Alert.alert(
        t('ble.brewtoolConnected'),
        t('ble.brewtoolConnectedMessage', { name: device.name || t('ble.unknownDevice') }),
        [
          {
            text: t('ble.addToMyDevices'),
            onPress: () => setActiveTab('myDevices'),
          },
          { text: t('common.ok') },
        ]
      );
    } catch (error) {
      Alert.alert(
        t('ble.connectionError'),
        error instanceof Error ? error.message : t('ble.unknownError')
      );
    }
  }, [t]);

  // Handle scan state change
  const handleScanStateChange = useCallback((scanning: boolean) => {
    setIsScanning(scanning);
  }, []);

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'myDevices':
        return (
          <DeviceListComponent
            showDiscovered={false}
            showUserDevices={true}
            onDeviceConnect={handleDeviceConnect}
            onDeviceRemove={handleDeviceRemove}
          />
        );
      case 'discover':
        return (
          <DeviceSearchComponent
            onDeviceSelect={handleDeviceConnect}
            brewToolsOnly={false}
          />
        );
      case 'brewtools':
        return (
          <BrewtoolScanner
            onBrewtoolSelect={handleBrewtoolSelect}
            onScanStateChange={handleScanStateChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <AuthGuard>
      <Stack.Screen
        options={{
          title: t('device.management'),
          headerBackTitle: t('navigation.back'),
        }}
      />
      <View style={styles.container}>
        {/* Tabs */}
        <View style={styles.tabContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tabScroll}
          >
            <TouchableOpacity
              style={[styles.tab, activeTab === 'myDevices' && styles.activeTab]}
              onPress={() => setActiveTab('myDevices')}
              disabled={isScanning}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'myDevices' && styles.activeTabText,
                ]}
              >
                {t('ble.myDevices')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'discover' && styles.activeTab]}
              onPress={() => setActiveTab('discover')}
              disabled={isScanning}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'discover' && styles.activeTabText,
                ]}
              >
                {t('ble.discoverDevices')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'brewtools' && styles.activeTab]}
              onPress={() => setActiveTab('brewtools')}
              disabled={isScanning}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === 'brewtools' && styles.activeTabText,
                ]}
              >
                {t('ble.findBrewtools')}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Tab Content */}
        <View style={styles.content}>
          {renderTabContent()}
        </View>
      </View>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  tabScroll: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingTop: 16,
  },
});
