package com.gaerschrank.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.widget.RemoteViews
import com.gaerschrank.R
import com.gaerschrank.MainActivity
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class FermentationWidgetProvider : AppWidgetProvider() {

    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
    }

    companion object {
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int
        ) {
            val widgetData = loadWidgetData(context)
            val views = RemoteViews(context.packageName, R.layout.fermentation_widget)

            // Set up click intent to open main app
            val intent = Intent(context, MainActivity::class.java)
            val pendingIntent = PendingIntent.getActivity(
                context, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)

            if (widgetData != null) {
                updateWidgetViews(views, widgetData)
            } else {
                showErrorState(views)
            }

            appWidgetManager.updateAppWidget(appWidgetId, views)
        }

        private fun loadWidgetData(context: Context): WidgetData? {
            return try {
                val sharedPref = context.getSharedPreferences("widget_data", Context.MODE_PRIVATE)
                val jsonString = sharedPref.getString("fermentation_data", null)
                
                if (jsonString != null) {
                    val json = JSONObject(jsonString)
                    WidgetData(
                        fermentationName = json.getString("fermentationName"),
                        currentTemperature = if (json.isNull("currentTemperature")) null else json.getDouble("currentTemperature"),
                        targetTemperature = if (json.isNull("targetTemperature")) null else json.getDouble("targetTemperature"),
                        gravity = if (json.isNull("gravity")) null else json.getDouble("gravity"),
                        lastUpdate = json.getString("lastUpdate"),
                        status = json.getString("status"),
                        deviceId = if (json.isNull("deviceId")) null else json.getInt("deviceId")
                    )
                } else {
                    null
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }

        private fun updateWidgetViews(views: RemoteViews, data: WidgetData) {
            // Set fermentation name
            views.setTextViewText(R.id.fermentation_name, data.fermentationName)

            // Set current temperature
            if (data.currentTemperature != null) {
                views.setTextViewText(
                    R.id.current_temperature,
                    String.format("%.1f°C", data.currentTemperature)
                )
                views.setViewVisibility(R.id.temperature_container, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.temperature_container, android.view.View.GONE)
            }

            // Set target temperature
            if (data.targetTemperature != null) {
                views.setTextViewText(
                    R.id.target_temperature,
                    String.format("Ziel: %.1f°C", data.targetTemperature)
                )
                views.setViewVisibility(R.id.target_temperature, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.target_temperature, android.view.View.GONE)
            }

            // Set gravity
            if (data.gravity != null) {
                views.setTextViewText(
                    R.id.gravity,
                    String.format("%.1f°P", data.gravity)
                )
                views.setViewVisibility(R.id.gravity_container, android.view.View.VISIBLE)
            } else {
                views.setViewVisibility(R.id.gravity_container, android.view.View.GONE)
            }

            // Set status
            val statusText = when (data.status) {
                "fermentation" -> "Gärung aktiv"
                "coldCrash" -> "Cold Crash"
                "offline" -> "Offline"
                else -> "Bereitschaft"
            }
            views.setTextViewText(R.id.status, statusText)

            // Set status color
            val statusColor = when (data.status) {
                "fermentation" -> android.graphics.Color.GREEN
                "coldCrash" -> android.graphics.Color.BLUE
                "offline" -> android.graphics.Color.RED
                else -> android.graphics.Color.GRAY
            }
            views.setTextColor(R.id.status, statusColor)

            // Set last update
            val lastUpdateText = formatLastUpdate(data.lastUpdate)
            views.setTextViewText(R.id.last_update, "Aktualisiert $lastUpdateText")
        }

        private fun showErrorState(views: RemoteViews) {
            views.setTextViewText(R.id.fermentation_name, "Keine Daten")
            views.setTextViewText(R.id.status, "Fehler beim Laden")
            views.setTextColor(R.id.status, android.graphics.Color.RED)
            views.setViewVisibility(R.id.temperature_container, android.view.View.GONE)
            views.setViewVisibility(R.id.target_temperature, android.view.View.GONE)
            views.setViewVisibility(R.id.gravity_container, android.view.View.GONE)
            views.setTextViewText(R.id.last_update, "Bitte App öffnen")
        }

        private fun formatLastUpdate(dateString: String): String {
            return try {
                val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                format.timeZone = TimeZone.getTimeZone("UTC")
                val date = format.parse(dateString)
                
                if (date != null) {
                    val now = Date()
                    val diff = now.time - date.time
                    val seconds = diff / 1000
                    val minutes = seconds / 60
                    val hours = minutes / 60
                    val days = hours / 24

                    when {
                        seconds < 60 -> "vor ${seconds}s"
                        minutes < 60 -> "vor ${minutes} Min"
                        hours < 24 -> "vor ${hours}h"
                        else -> "vor ${days}d"
                    }
                } else {
                    "Unbekannt"
                }
            } catch (e: Exception) {
                "Unbekannt"
            }
        }

        fun updateWidgetData(context: Context, data: WidgetData) {
            // Save data to SharedPreferences
            val sharedPref = context.getSharedPreferences("widget_data", Context.MODE_PRIVATE)
            with(sharedPref.edit()) {
                val json = JSONObject().apply {
                    put("fermentationName", data.fermentationName)
                    put("currentTemperature", data.currentTemperature)
                    put("targetTemperature", data.targetTemperature)
                    put("gravity", data.gravity)
                    put("lastUpdate", data.lastUpdate)
                    put("status", data.status)
                    put("deviceId", data.deviceId)
                }
                putString("fermentation_data", json.toString())
                apply()
            }

            // Update all widgets
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val widgetIds = appWidgetManager.getAppWidgetIds(
                android.content.ComponentName(context, FermentationWidgetProvider::class.java)
            )
            
            for (widgetId in widgetIds) {
                updateAppWidget(context, appWidgetManager, widgetId)
            }
        }
    }
}

data class WidgetData(
    val fermentationName: String,
    val currentTemperature: Double?,
    val targetTemperature: Double?,
    val gravity: Double?,
    val lastUpdate: String,
    val status: String,
    val deviceId: Int?
)
