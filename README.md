# <PERSON> Gaerschrank

## Deployment

```bash
sh ./scripts/build.sh
ssh -t <EMAIL> < ./scripts/deploy.sh
```

### Environment Variables

#### Backend

| Variable                     | Description        |
| ---------------------------- | ------------------ |
| `DATABASE_URL`               | MySQL Database URI |
| `WEB_PUSH_VAPID_PRIVATE_KEY` | VAPID Private Key  |
| `WEB_PUSH_VAPID_PUBLIC_KEY`  | VAPID Public Key   |

```
DATABASE_URL="mysql://root:password!@localhost:3306/gaerschrank"
WEB_PUSH_VAPID_PRIVATE_KEY="5cVDzEv6lcMJElkuGIniHHLQZfL-ahvj6r4jDTU79GY"
WEB_PUSH_VAPID_PUBLIC_KEY="BLcxbLI-aSJ3Z5n6Il6RE6YX7mGJBK_nhW7O2L8wICaEdPp9sBMhqZxxBriYRNNCuaR-68iPlHA8ztjSWYyWTqc"
```

#### Frontend

| Variable                   | Description      |
| -------------------------- | ---------------- |
| `VITE_WEB_PUSH_PUBLIC_KEY` | VAPID Public Key |

```
VITE_WEB_PUSH_PUBLIC_KEY="BLcxbLI-aSJ3Z5n6Il6RE6YX7mGJBK_nhW7O2L8wICaEdPp9sBMhqZxxBriYRNNCuaR-68iPlHA8ztjSWYyWTqc"
```
