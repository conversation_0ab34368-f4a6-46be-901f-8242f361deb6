{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": "src", "paths": {"@backend/*": ["../../backend/src/*"], "@backendZod/*": ["../../backend/prisma/zod/*"], "@root/*": ["../*"], "@tests/*": ["../tests/*"], "@prisma/*": ["../../node_modules/.prisma/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}