import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import tsconfigPaths from "vite-tsconfig-paths";
import dotenv from "dotenv";
import path from "path";

dotenv.config({
  path: path.resolve(__dirname, "../backend/.env"),
});

export default defineConfig({
  plugins: [
    tsconfigPaths({ ignoreConfigErrors: true }),
    react(),
    tailwindcss(),
  ],
  server: {
    port: 5200,
  },
  define: {
    API_KEY: JSON.stringify(process.env.DEV_API_KEY),
  },
});
