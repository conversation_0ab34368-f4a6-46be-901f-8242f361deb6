{"name": "@unrup1-gaerschrank/mockup", "version": "0.1.0", "author": {"name": "<PERSON>", "url": "https://philun.de"}, "license": "UNLICENSED", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "serve": "vite preview", "lint": "eslint ./src/**/*.{ts,tsx}", "test": "vitest"}, "dependencies": {"@headlessui/react": "^2.2.3", "@heroicons/react": "^2.1.1", "@tanstack/react-query": "^5.75.7", "axios": "^1.9.0", "dotenv": "^16.5.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.17.0", "tailwindcss": "^4.1.5", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/vite": "^4.1.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@vitejs/plugin-react": "^4.1.0", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}