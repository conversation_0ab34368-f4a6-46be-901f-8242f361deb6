import DeviceList from "components/DeviceList";
import {
  useGetBrewtools,
  useGetDevices,
  registerDevice,
  createApi<PERSON>ey,
  DeviceOperator,
  DeviceType,
} from "services/DeviceService";
import {
  useDeviceStore,
  generateRandomGravity,
  generateRandomTemperature,
} from "store/DeviceStore";

const Dashboard = () => {
  const {
    data: devices,
    isLoading,
    isError,
    error,
    refetch: refetchDevices,
  } = useGetDevices();
  const { data: brewtools, refetch } = useGetBrewtools();

  if (isLoading) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <p className="text-xl text-gray-700">Loading...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <p className="text-xl text-gray-700">Error: {error.message}</p>
      </div>
    );
  }

  const createNewBrewtool = async () => {
    const device = await registerDevice({
      name: `Philun Brewtool #${(brewtools?.length ?? 0) + 1}`,
      operator: DeviceOperator.BREWTOOL,
      operatorDeviceId: `mockup-brewtool-${(brewtools?.length ?? 0) + 1}`,
      type: DeviceType.BREWTOOL,
    });
    const apiKey = await createApiKey(device);
    useDeviceStore.getState().addDevice({
      id: device.id,
      type: "BREWTOOL",
      status: null,
      temperature: generateRandomTemperature(),
      apiKey: apiKey.key,
    });
    refetchDevices();
    refetch();
  };

  const createNewSpindle = async () => {
    const device = await registerDevice({
      name: `Mockup iSpindel #${(devices?.length ?? 0) + 1}`,
      operator: DeviceOperator.CUSTOM,
      operatorDeviceId: `mockup-ispindel-${(devices?.length ?? 0) + 1}`,
      type: DeviceType.ISPINDEL,
    });
    const apiKey = await createApiKey(device);

    useDeviceStore.getState().addDevice({
      id: device.id,
      type: DeviceType.ISPINDEL as "ISPINDEL",
      plato: generateRandomGravity(),
      temperature: generateRandomTemperature(),
      apiKey: apiKey.key,
    });

    refetchDevices();
  };

  return (
    <div className="container mx-auto p-4 flex gap-6 flex-col">
      <h1 className="text-2xl font-bold">Brewtools Devices</h1>
      <DeviceList
        devices={brewtools ?? []}
        onAddDevice={() => createNewBrewtool()}
      />
      <h1 className="text-2xl font-bold">Switch Devices</h1>
      <DeviceList
        devices={devices?.filter((d) => d.type === "SWITCH") ?? []}
        onAddDevice={() => {}}
      />
      <h1 className="text-2xl font-bold">iSpindel Devices</h1>
      <DeviceList
        devices={devices?.filter((d) => d.type === "ISPINDEL") ?? []}
        onAddDevice={() => createNewSpindle()}
      />
    </div>
  );
};

export default Dashboard;
