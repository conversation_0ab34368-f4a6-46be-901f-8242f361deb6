"use client";

import { useState } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import { useDeviceStore } from "store/DeviceStore";
import { ThermometerIcon } from "lucide-react";

interface TemperatureModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  deviceId: number;
  currentTemperature: number;
}

export default function TemperatureModal({
  open,
  setOpen,
  deviceId,
  currentTemperature,
}: TemperatureModalProps) {
  const saveTemperature = useDeviceStore((state) => state.setTemperature);
  const [temperature, setTemperature] = useState(
    Math.round(currentTemperature)
  );

  const handleSubmit = () => {
    saveTemperature(deviceId, temperature);
    setOpen(false);
  };

  return (
    <Dialog open={open} onClose={setOpen} className="relative z-10">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500/75 transition-opacity data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all data-closed:translate-y-4 data-closed:opacity-0 data-enter:duration-300 data-enter:ease-out data-leave:duration-200 data-leave:ease-in sm:my-8 sm:w-full sm:max-w-lg sm:p-6 data-closed:sm:translate-y-0 data-closed:sm:scale-95"
          >
            <div>
              <div className="mx-auto flex size-12 items-center justify-center rounded-full bg-blue-100">
                <ThermometerIcon
                  aria-hidden="true"
                  size={24}
                  className="size-6 text-blue-600"
                />
              </div>
              <div className="mt-3 text-center sm:mt-5">
                <DialogTitle
                  as="h3"
                  className="text-base font-semibold text-gray-900"
                >
                  Set Target Temperature
                </DialogTitle>
                <div className="mt-4">
                  <div className="flex items-center justify-center">
                    <input
                      type="range"
                      min="5"
                      max="30"
                      step="0.5"
                      value={temperature}
                      onChange={(e) =>
                        setTemperature(parseFloat(e.target.value))
                      }
                      className="w-full"
                    />
                  </div>
                  <div className="mt-2 text-2xl font-semibold text-gray-900">
                    {temperature}°C
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Current temperature: {currentTemperature.toFixed(1)}°C
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
              <button
                type="button"
                onClick={handleSubmit}
                className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-xs hover:bg-blue-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 sm:col-start-2"
              >
                Set Temperature
              </button>
              <button
                type="button"
                data-autofocus
                onClick={() => setOpen(false)}
                className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset hover:bg-gray-50 sm:col-start-1 sm:mt-0"
              >
                Cancel
              </button>
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
}
