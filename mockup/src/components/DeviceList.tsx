import DeviceCard from "components/DeviceCard";
import { Device } from "services/DeviceService";

const DeviceList = ({
  devices,
  onAddDevice,
}: {
  devices: Device[];
  onAddDevice: () => void;
}) => {
  if (!devices || devices.length === 0) {
    return (
      <div className="rounded-lg bg-white py-10 text-center shadow">
        <p className="text-gray-600">Keine Geräte im Moment vorhanden</p>
        <button
          onClick={onAddDevice}
          className="mt-4 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Neues Gerät hinzufügen
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {devices.map((device) => (
          <DeviceCard key={device.id} device={device} />
        ))}
        <div className="rounded-lg bg-white p-4 shadow flex items-center justify-center">
          <button
            onClick={onAddDevice}
            className="rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Neues Gerät hinzufügen +
          </button>
        </div>
      </div>
    </div>
  );
};
export default DeviceList;
