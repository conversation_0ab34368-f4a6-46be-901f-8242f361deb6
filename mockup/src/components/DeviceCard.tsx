import { useMemo, useState } from "react";
import { sendTemperature } from "services/BrewtoolApiService";
import { Device } from "services/DeviceService";
import { useDeviceStore } from "store/DeviceStore";
import TemperatureModal from "./TemperatureModal";

interface DeviceCardProps {
  device: Device;
}

const DeviceCard = ({ device }: DeviceCardProps) => {
  const { devices, addDevice } = useDeviceStore();
  const [modalOpen, setModalOpen] = useState(false);

  // Check if device is already in the store
  const deviceExtraData = useMemo(
    () => devices.find((d) => d.id === device.id),
    [devices]
  );

  const handleAddDevice = () => {
    addDevice(device);
  };

  const sendFakeData = () => {
    if (!deviceExtraData) {
      console.error("Device not found in store");
      return;
    }

    console.log(`Sending fake data to device ${device.id}`);
    sendTemperature(deviceExtraData.id, deviceExtraData.temperature);
  };

  const openTemperatureModal = () => {
    setModalOpen(true);
  };

  return (
    <>
      <div className="rounded-lg bg-white p-4 shadow">
        <h3 className="text-lg font-medium text-gray-900">{device.name}</h3>
        <div className="mt-2 flex items-center">
          <span
            className={`h-3 w-3 rounded-full ${device.online || deviceExtraData ? "bg-green-500" : "bg-red-500"}`}
          ></span>
          <span className="ml-2 text-sm text-gray-600">
            {device.online || deviceExtraData ? "online" : "offline"}
          </span>
        </div>
        <div className="mt-4 grid grid-cols-2 gap-2 text-sm text-gray-600">
          <div>ID: {device.id}</div>
          {device.type === "BREWTOOL" && deviceExtraData && (
            <>
              <div>
                Device Temperature: {deviceExtraData.temperature.toFixed(1)} °C
              </div>
              {device.targetTemperature && (
                <div>Target Temperature: {device.targetTemperature} °C</div>
              )}
              <div>Status: {deviceExtraData.status || "IDLE"}</div>
            </>
          )}
          {device.type === "ISPINDEL" && deviceExtraData && (
            <>
              <div>Plato: {deviceExtraData.plato.toFixed(2)} °P</div>
              <div>Interval: {device.interval} seconds</div>
              <div>Status: {deviceExtraData.status || "IDLE"}</div>
            </>
          )}
        </div>

        {!deviceExtraData && (
          <button
            onClick={handleAddDevice}
            className="mt-4 w-full rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Add as Fake Device
          </button>
        )}

        {deviceExtraData && (
          <div className="mt-4 grid grid-cols-2 gap-2">
            <button
              onClick={sendFakeData}
              className="rounded-md bg-blue-500 px-4 py-2 text-sm font-medium text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Send fake data
            </button>
            <button
              onClick={openTemperatureModal}
              className="rounded-md bg-green-500 px-4 py-2 text-sm font-medium text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Set Temperature
            </button>
          </div>
        )}
      </div>

      {deviceExtraData && (
        <TemperatureModal
          open={modalOpen}
          setOpen={setModalOpen}
          deviceId={device.id}
          currentTemperature={deviceExtraData.temperature}
        />
      )}
    </>
  );
};

export default DeviceCard;
