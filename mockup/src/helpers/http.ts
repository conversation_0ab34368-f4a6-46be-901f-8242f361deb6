import { useQuery } from "@tanstack/react-query";
import axios from "axios";

export const API_URL = "http://localhost:3000/api/";

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
    Authentication: `Bearer ${API_KEY}`,
  },
});

export function useGet<T>(route: string) {
  return useQuery<T>({
    queryKey: [route],
    queryFn: async () => {
      const response = await axios.get<T>(API_URL + route, {
        headers: {
          "Content-Type": "application/json",
          Authentication: `Bearer ${API_KEY}`,
        },
      });
      return response.data;
    },
  });
}
