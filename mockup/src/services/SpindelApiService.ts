import { api } from "helpers/http";

export type iSpindelData = {
  name: string;
  id: string;
  temperature: number;
  angle: number;
  battery: number;
  temp_units: string;
  gravity: number;
  interval: number;
  token?: string;
  RSSI: number;
};

export const sendSpindelData = async (
  deviceId: number,
  data: Partial<iSpindelData>
) => {
  const response = await api.post(`/device/${deviceId}/spindel`, {
    name: "iSpindel101",
    angle: 60.09726,
    temp_units: "C",
    battery: 3.727841,
    RSSI: -87,
    ...data,
  });
  return response.data;
};
