import { useQuery } from "@tanstack/react-query";
import { api } from "helpers/http";
import axios from "axios";

export const getTemperatureConfig = async (deviceId: number) => {
  try {
    const res = await api.get(`v1/brewtool/${deviceId}/config`);
    const data = res.data.split(";");
    return {
      temperature: parseFloat(data[0]),
      endDate: parseInt(data[1]),
    };
  } catch (error) {
    if (axios.isAxiosError(error) && error?.response?.status !== 404) {
      console.error("Error getting temperature config:", error);
    }
    return null;
  }
};

export const useBrewtoolConfig = async (deviceId: number) => {
  return useQuery({
    queryKey: ["brewtool", deviceId, "config"],
    queryFn: async () => getTemperatureConfig(deviceId),
  });
};

export const sendElectricSignal = async (
  type: "HEATING" | "COOLING",
  status: boolean
) => {
  api.post("electricity", [type, status ? "START" : "STOP"].join(";"), {
    headers: {
      "Content-Type": "text/plain",
    },
  });
};

export const getCurrentTemperature = async (deviceId: number) => {
  try {
    const res = await api.get(`v1/brewtool/${deviceId}/temperature`);
    return parseFloat(res.data);
  } catch (error) {
    console.error("Error getting current temperature:", error);
    return null;
  }
};

export const useCurrentTemperature = (deviceId: number) => {
  return useQuery({
    queryKey: ["brewtool", deviceId, "temperature"],
    queryFn: async () => getCurrentTemperature(deviceId),
    refetchInterval: 10000, // Refetch every 10 seconds
  });
};

export const sendTemperature = async (
  deviceId: number,
  temperature: number
) => {
  try {
    await api.post(`v1/brewtool/${deviceId}/temperature`, `${temperature}`, {
      headers: {
        "Content-Type": "text/plain",
      },
    });
  } catch (error) {
    console.error("Error sending temperature:", error);
  }
};

export const setTargetTemperature = async (
  deviceId: number,
  targetTemperature: number
) => {
  try {
    await api.post(`v1/brewtool/${deviceId}/target`, `${targetTemperature}`, {
      headers: {
        "Content-Type": "text/plain",
      },
    });
    return true;
  } catch (error) {
    console.error("Error setting target temperature:", error);
    return false;
  }
};
