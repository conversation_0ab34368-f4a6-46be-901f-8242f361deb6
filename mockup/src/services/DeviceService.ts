import { useGet, api } from "helpers/http";

import { <PERSON>ceModel } from "@backendZod/device";
import type { <PERSON><PERSON><PERSON><PERSON>, Device } from "@prisma/client";
import { z } from "zod";

export const useGetDevices = () => useGet<Device[]>("device");

export const useGetBrewtoolDevices = () =>
  useGet<Device[]>("device?type=brewtool");

export const useGetBrewtools = () => useGet<Device[]>("brewtool");

export const useGetDevice = (deviceId: string) =>
  useGet<Device[]>(`device/${deviceId}`);

export const DeviceOperator = {
  TUYA: "TUYA",
  TASMOTA: "TASMOTA",
  SMARTLIFE: "SMARTLIFE",
  BREWTOOL: "BREWTOOL",
  CUSTOM: "CUSTOM",
};

export const DeviceType = {
  TEMPERATURE: "TEMPERATURE",
  SWITCH: "SWITCH",
  ISPINDEL: "ISPINDEL",
  BREWTOOL: "BREWTOOL",
  OTHER: "OTHER",
};

const Model = DeviceModel.omit({
  id: true,
  operator: true,
  mqttClientId: true,
  mqttPassword: true,
  mqttUsername: true,
  createdAt: true,
  updatedAt: true,
  type: true,
}).merge(
  z.object({
    type: z.enum(Object.values(DeviceType) as [string, ...string[]]),
    operator: z.enum(Object.values(DeviceOperator) as [string, ...string[]]),
  })
);

type CompleteDevice = z.infer<typeof Model>;

export const registerDevice = async (device: CompleteDevice) => {
  const response = await api.post<Device>("device", device);

  return response.data;
};

export const createApiKey = async (device: Device) => {
  const responseApiKey = await api.post<ApiKey>(
    `device/${device.id}/api-key`,
    {}
  );

  return responseApiKey.data;
};
