import {
  getTemperatureConfig,
  sendTemperature,
} from "services/BrewtoolApiService";
import { sendSpindelData } from "services/SpindelApiService";
import { create } from "zustand";
import { persist } from "zustand/middleware";

type ExtraDeviceConfig =
  | {
      id: number;
      type: "BREWTOOL";
      temperature: number;
      status: "HEATING" | "COOLING" | null;
      apiKey?: string;
    }
  | {
      id: number;
      type: "ISPINDEL";
      plato: number;
      temperature: number;
      apiKey?: string;
      interval: number;
    };

interface DeviceStore {
  devices: ExtraDeviceConfig[];
  addDevice: (device: ExtraDeviceConfig) => void;
  removeDevice: (id: number) => void;
  updateDevice: (id: number, updates: Partial<ExtraDeviceConfig>) => void;
  setTemperature: (id: number, temperature: number) => void;
  sendDataInterval: number | null;
  temperatureAdjustInterval: number | null;
  startSendingData: () => void;
  stopSendingData: () => void;
}

export const generateRandomGravity = () => {
  return Math.random() * 28 + 2; // 2-30°P
};

export const generateRandomTemperature = () => {
  return Math.random() * 25 + 5; // 5-30°C
};

const sendFakeData = async (devices: ExtraDeviceConfig[]) => {
  try {
    for (const device of devices) {
      if (device.type === "BREWTOOL") {
        const config = await getTemperatureConfig(device.id);

        const temperature = adjustTemperatureTowardsTarget(
          device,
          config?.temperature ?? null
        );

        console.log(
          `Sending fake temperature ${temperature} for device ${device.id} on ${new Date().toLocaleTimeString()} (Target: ${config?.temperature ?? "N/A"})`
        );

        sendTemperature(device.id, temperature);
      } else if (device.type === "ISPINDEL") {
        console.log(
          `Sending fake iSpindel data for device ${device.id} on ${new Date().toLocaleTimeString()}`
        );
        let gravity = adjustValueTowardsTarget(
          device.plato,
          device.plato,
          0.02
        );

        useDeviceStore.getState().updateDevice(device.id, { plato: gravity });

        sendSpindelData(device.id, {
          id: String(device.id),
          token: device.apiKey,
          gravity,
          interval: device.interval ?? 900,
          temperature: device.temperature,
        });
      } else {
        console.error("Unknown device type:", device.type);
      }
    }
  } catch (error) {
    console.error("Error sending fake data:", error);
  }
};

const adjustValueTowardsTarget = (
  actual: number,
  target: number | null,
  adjustment: number = 0.2
): number => {
  if (!target) {
    return actual;
  }

  let newValue = actual;
  const diff = target - actual;

  if (Math.abs(diff) < adjustment) {
    // Close enough to target
    newValue = target;
  } else if (diff > 0) {
    // Need to heat up
    newValue += Math.min(adjustment, diff);
  } else {
    // Need to cool down
    newValue -= Math.min(adjustment, Math.abs(diff));
  }

  return newValue;
};

const adjustTemperatureTowardsTarget = (
  device: ExtraDeviceConfig,
  targetTemperature: number | null
): number => {
  if (!targetTemperature) {
    return device.temperature;
  }

  let newTemp = adjustValueTowardsTarget(device.temperature, targetTemperature);

  useDeviceStore.getState().setTemperature(device.id, newTemp);
  return newTemp;
};

export const useDeviceStore = create<DeviceStore>()(
  persist(
    (set, get) => ({
      devices: [],
      extraDeviceConfig: [],
      sendDataInterval: null,
      temperatureAdjustInterval: null,

      addDevice: (device) =>
        set((state) => ({
          devices: [...state.devices, device as ExtraDeviceConfig],
        })),

      removeDevice: (id) =>
        set((state) => ({
          devices: state.devices.filter((device) => device.id !== id),
        })),

      updateDevice: (id, updates) =>
        set((state) => ({
          devices: state.devices.map((device) =>
            device.id === id ? { ...device, ...updates } : device
          ),
        })),

      setTemperature: (id, temperature) =>
        set((state) => ({
          devices: state.devices.map((device) =>
            device.id === id ? { ...device, temperature } : device
          ),
        })),

      startSendingData: () => {
        // First clear any existing interval to avoid duplicates
        const oldIntervalId = get().sendDataInterval;
        if (oldIntervalId) {
          clearInterval(oldIntervalId);
        }

        // Send data immediately on start
        sendFakeData(get().devices);

        // Set up the interval (every 30 seconds)
        const intervalId = window.setInterval(() => {
          sendFakeData(get().devices);
        }, 30000);

        set({ sendDataInterval: intervalId });
      },

      stopSendingData: () => {
        const { sendDataInterval } = get();
        if (sendDataInterval) {
          clearInterval(sendDataInterval);
          set({ sendDataInterval: null });
        }
      },
    }),
    {
      name: "device-storage", // name of the item in local storage
      partialize: (state) => ({ devices: state.devices }), // only persist devices, not the interval
    }
  )
);
