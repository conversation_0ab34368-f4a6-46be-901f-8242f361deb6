import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Dashboard from "components/Dashboard";
import { useEffect } from "react";
import { useDeviceStore } from "./store/DeviceStore";

const queryClient = new QueryClient();

const App = () => {
  const startSendingData = useDeviceStore((state) => state.startSendingData);
  const stopSendingData = useDeviceStore((state) => state.stopSendingData);

  useEffect(() => {
    // Start sending data when component mounts
    startSendingData();

    // Clean up by stopping interval when component unmounts
    return () => {
      stopSendingData();
    };
  }, [startSendingData, stopSendingData]);

  return (
    <QueryClientProvider client={queryClient}>
      <Dashboard />
    </QueryClientProvider>
  );
};

export default App;
