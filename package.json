{"repository": "**************:unrup1/gaerschrank.git", "name": "unrup1-g<PERSON><PERSON><PERSON>", "version": "0.0.1", "author": {"name": "<PERSON>", "url": "https://philun.de"}, "license": "UNLICENSED", "main": "index.ts", "engines": {"node": "20.x", "yarn": "1.22.x"}, "private": true, "workspaces": ["frontend", "backend", "mockup", "app"], "scripts": {"dev": "concurrently --names \"BE_DEV,BE_API,FE_APP\" -c \"dim,blue,magenta\" \"yarn dev:backend:sql\" \"yarn dev:backend:server\" \"yarn dev:frontend\" \"yarn dev:mockup\"", "dev:backend:sql": "cd backend && docker compose up --detach && prisma generate && prisma migrate deploy", "dev:backend:server": "cd backend && yarn dev", "dev:frontend": "cd frontend && yarn dev", "dev:mockup": "cd mockup && yarn dev", "deploy": "ssh -t -t <EMAIL> < ./deploy.sh", "telebit": "~/telebit http 3000", "dev:mqtt": "cd backend && yarn run dev:nanomq && yarn run dev:nanomq:restart", "build": "yarn build:backend && yarn build:frontend && mkdir ./backend/dist/src/public && cp -r ./frontend/dist/* ./backend/public", "build:backend": "cd backend && yarn build", "build:frontend": "cd frontend && yarn build"}, "devDependencies": {"concurrently": "^8.2.1"}, "dependencies": {"yalc": "^1.0.0-pre.53"}}