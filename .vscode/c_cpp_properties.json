{"version": 4, "configurations": [{"name": "<PERSON>", "compilerPath": "/usr/bin/clang", "compilerArgs": [], "intelliSenseMode": "macos-clang-arm64", "includePath": ["${workspaceFolder}/**"], "forcedInclude": [], "cStandard": "c17", "cppStandard": "c++17", "defines": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "compilerPath": "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/bin/xtensa-esp32-elf-g++", "compilerArgs": ["-mlongcalls", "-Wno-frame-address", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-Wno-error=unused-but-set-variable", "-fno-jump-tables", "-fno-tree-switch-conversion", "-std=gnu++11", "-fexceptions", "-fno-rtti"], "intelliSenseMode": "gcc-x64", "includePath": ["/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/newlib/platform_include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/freertos/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/freertos/include/esp_additions/freertos", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/freertos/port/xtensa/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/freertos/include/esp_additions", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hw_support/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hw_support/include/soc", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hw_support/include/soc/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hw_support/port/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hw_support/port/esp32/private_include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/heap/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/log/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/lwip/include/apps", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/lwip/include/apps/sntp", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/lwip/lwip/src/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/lwip/port/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/lwip/port/esp32/include/arch", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/soc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/soc/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/soc/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/hal/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/hal/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/hal/platform_port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_rom/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_rom/include/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_rom/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_system/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_system/port/soc", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_system/port/public_compat", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/xtensa/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/xtensa/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/driver/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/driver/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_pm/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_ringbuf/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/efuse/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/efuse/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/vfs/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_wifi/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_event/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_netif/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_eth/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/tcpip_adapter/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_phy/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_phy/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_ipc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/app_trace/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_timer/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/mbedtls/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/mbedtls/mbedtls/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/mbedtls/esp_crt_bundle/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/app_update/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/spi_flash/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bootloader_support/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/nvs_flash/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/pthread/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_gdbstub/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_gdbstub/xtensa", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_gdbstub/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espcoredump/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espcoredump/include/port/xtensa", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/wpa_supplicant/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/wpa_supplicant/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/wpa_supplicant/esp_supplicant/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/ieee802154/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/console", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/asio/asio/asio/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/asio/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/common/osi/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/include/esp32/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/common/api/include/api", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/common/btc/profile/esp/blufi/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/common/btc/profile/esp/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/host/bluedroid/api/include/api", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_core", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_core/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_core/storage", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/btc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_models/common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_models/client/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/mesh_models/server/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/api/core/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/api/models/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/bt/esp_ble_mesh/api", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/cbor/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/unity/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/unity/unity/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/cmock/CMock/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/coap/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/coap/libcoap/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/nghttp/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/nghttp/nghttp2/lib/includes", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-tls", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-tls/esp-tls-crypto", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_adc_cal/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_hid/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/tcp_transport/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_http_client/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_http_server/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_https_ota/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_https_server/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_lcd/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_lcd/interface", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/protobuf-c/protobuf-c", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/protocomm/include/common", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/protocomm/include/security", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/protocomm/include/transports", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/mdns/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_local_ctrl/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/sdmmc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_serial_slave_link/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_websocket_client/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/expat/expat/expat/lib", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/expat/port/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/wear_levelling/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/fatfs/diskio", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/fatfs/vfs", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/fatfs/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/freemodbus/freemodbus/common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/idf_test/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/idf_test/include/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/jsmn/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/json/cJSON", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/libsodium/libsodium/src/libsodium/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/libsodium/port_include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/mqtt/esp-mqtt/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/openssl/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/perfmon/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/spiffs/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/ulp/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/wifi_provisioning/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/rmaker_common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_diagnostics/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/rtc_store/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_insights/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/json_parser/upstream/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/json_parser/upstream", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/json_generator/upstream", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_schedule/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp_secure_cert_mgr/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_rainmaker/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/gpio_button/button/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/qrcode/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/ws2812_led", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp_littlefs/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/tool", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/typedef", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/image", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/math", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/nn", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/layer", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/detect", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp-dl/include/model_zoo", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp32-camera/driver/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/esp32-camera/conversions/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/dotprod/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/support/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/support/mem/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/hann/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/blackman/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/blackman_harris/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/nuttall/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/windows/flat_top/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/iir/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/fir/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/add/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/sub/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/mul/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/addc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/mulc/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/math/sqrt/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/matrix/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/fft/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/dct/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/conv/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/common/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/kalman/ekf/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/include/fb_gfx/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/tools/sdk/esp32/qio_qspi/include", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/cores/esp32", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/variants/esp32", "/Users/<USER>/Documents/Arduino/libraries/OneWire", "/Users/<USER>/Documents/Arduino/libraries/DallasTemperature", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/libraries/WiFi/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/libraries/HTTPClient/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/libraries/WiFiClientSecure/src", "/Users/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/libraries/Preferences/src", "/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/libraries/BLE/src", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/xtensa-esp32-elf/include/c++/8.4.0", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/xtensa-esp32-elf/include/c++/8.4.0/xtensa-esp32-elf", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/xtensa-esp32-elf/include/c++/8.4.0/backward", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/lib/gcc/xtensa-esp32-elf/8.4.0/include", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/lib/gcc/xtensa-esp32-elf/8.4.0/include-fixed", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/xtensa-esp32-elf/sys-include", "/Users/<USER>/Library/Arduino15/packages/esp32/tools/xtensa-esp32-elf-gcc/esp-2021r2-patch5-8.4.0/xtensa-esp32-elf/include"], "forcedInclude": ["/Users/<USER>/Library/Arduino15/packages/esp32/hardware/esp32/2.0.14/cores/esp32/Arduino.h"], "cStandard": "c11", "cppStandard": "c++11", "defines": ["HAVE_CONFIG_H", "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"", "UNITY_INCLUDE_CONFIG_H", "WITH_POSIX", "_GNU_SOURCE", "IDF_VER=\"v4.4.6-dirty\"", "ESP_PLATFORM", "_POSIX_READER_WRITER_LOCKS", "F_CPU=240000000L", "ARDUINO=10607", "ARDUINO_ESP32_DEV", "ARDUINO_ARCH_ESP32", "ARDUINO_BOARD=\"ESP32_DEV\"", "ARDUINO_VARIANT=\"esp32\"", "ARDUINO_PARTITION_huge_app", "ESP32", "CORE_DEBUG_LEVEL=0", "ARDUINO_RUNNING_CORE=1", "ARDUINO_EVENT_RUNNING_CORE=1", "ARDUINO_USB_CDC_ON_BOOT=0", "__DBL_MIN_EXP__=(-1021)", "__FLT32X_MAX_EXP__=1024", "__cpp_attributes=200809", "__UINT_LEAST16_MAX__=0xffff", "__ATOMIC_ACQUIRE=2", "__FLT_MIN__=1.1754943508222875e-38F", "__GCC_IEC_559_COMPLEX=0", "__cpp_aggregate_nsdmi=201304", "__UINT_LEAST8_TYPE__=unsigned char", "__INTMAX_C(c)=c ## LL", "__CHAR_BIT__=8", "__UINT8_MAX__=0xff", "__WINT_MAX__=0xffffffffU", "__FLT32_MIN_EXP__=(-125)", "__cpp_static_assert=200410", "__ORDER_LITTLE_ENDIAN__=1234", "__SIZE_MAX__=0xffffffffU", "__WCHAR_MAX__=0xffff", "__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1=1", "__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2=1", "__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4=1", "__DBL_DENORM_MIN__=double(4.9406564584124654e-324L)", "__GCC_ATOMIC_CHAR_LOCK_FREE=2", "__GCC_IEC_559=0", "__FLT32X_DECIMAL_DIG__=17", "__FLT_EVAL_METHOD__=0", "__cpp_binary_literals=201304", "__FLT64_DECIMAL_DIG__=17", "__GCC_ATOMIC_CHAR32_T_LOCK_FREE=2", "__cpp_variadic_templates=200704", "__UINT_FAST64_MAX__=0xffffffffffffffffULL", "__SIG_ATOMIC_TYPE__=int", "__DBL_MIN_10_EXP__=(-307)", "__FINITE_MATH_ONLY__=0", "__cpp_variable_templates=201304", "__GNUC_PATCHLEVEL__=0", "__FLT32_HAS_DENORM__=1", "__UINT_FAST8_MAX__=0xffffffffU", "__has_include(STR)=__has_include__(STR)", "__DEC64_MAX_EXP__=385", "__INT8_C(c)=c", "__INT_LEAST8_WIDTH__=8", "__UINT_LEAST64_MAX__=0xffffffffffffffffULL", "__SHRT_MAX__=0x7fff", "__LDBL_MAX__=1.7976931348623157e+308L", "__UINT_LEAST8_MAX__=0xff", "__GCC_ATOMIC_BOOL_LOCK_FREE=2", "__UINTMAX_TYPE__=long long unsigned int", "__DEC32_EPSILON__=1E-6DF", "__FLT_EVAL_METHOD_TS_18661_3__=0", "__CHAR_UNSIGNED__=1", "__UINT32_MAX__=0xffffffffU", "__GXX_EXPERIMENTAL_CXX0X__=1", "__LDBL_MAX_EXP__=1024", "__WINT_MIN__=0U", "__INT_LEAST16_WIDTH__=16", "__SCHAR_MAX__=0x7f", "__WCHAR_MIN__=0", "__INT64_C(c)=c ## LL", "__DBL_DIG__=15", "__GCC_ATOMIC_POINTER_LOCK_FREE=2", "__SIZEOF_INT__=4", "__SIZEOF_POINTER__=4", "__GCC_ATOMIC_CHAR16_T_LOCK_FREE=2", "__USER_LABEL_PREFIX__", "__STDC_HOSTED__=1", "__LDBL_HAS_INFINITY__=1", "__XTENSA_EL__=1", "__FLT32_DIG__=6", "__FLT_EPSILON__=1.1920928955078125e-7F", "__GXX_WEAK__=1", "__SHRT_WIDTH__=16", "__LDBL_MIN__=2.2250738585072014e-308L", "__DEC32_MAX__=9.999999E96DF", "__cpp_threadsafe_static_init=200806", "__FLT32X_HAS_INFINITY__=1", "__INT32_MAX__=0x7fffffff", "__INT_WIDTH__=32", "__SIZEOF_LONG__=4", "__UINT16_C(c)=c", "__PTRDIFF_WIDTH__=32", "__DECIMAL_DIG__=17", "__FLT64_EPSILON__=2.2204460492503131e-16F64", "__INTMAX_WIDTH__=64", "__FLT64_MIN_EXP__=(-1021)", "__has_include_next(STR)=__has_include_next__(STR)", "__LDBL_HAS_QUIET_NAN__=1", "__FLT64_MANT_DIG__=53", "__GNUC__=8", "__GXX_RTTI=1", "__cpp_delegating_constructors=200604", "__FLT_HAS_DENORM__=1", "__SIZEOF_LONG_DOUBLE__=8", "__BIGGEST_ALIGNMENT__=16", "__STDC_UTF_16__=1", "__FLT64_MAX_10_EXP__=308", "__FLT32_HAS_INFINITY__=1", "__DBL_MAX__=double(1.7976931348623157e+308L)", "__cpp_raw_strings=200710", "__INT_FAST32_MAX__=0x7fffffff", "__DBL_HAS_INFINITY__=1", "__DEC32_MIN_EXP__=(-94)", "__INTPTR_WIDTH__=32", "__FLT32X_HAS_DENORM__=1", "__INT_FAST16_TYPE__=int", "__LDBL_HAS_DENORM__=1", "__cplusplus=201402L", "__cpp_ref_qualifiers=200710", "__DEC128_MAX__=9.999999999999999999999999999999999E6144DL", "__INT_LEAST32_MAX__=0x7fffffff", "__DEC32_MIN__=1E-95DF", "__DEPRECATED=1", "__cpp_rvalue_references=200610", "__DBL_MAX_EXP__=1024", "__WCHAR_WIDTH__=16", "__FLT32_MAX__=3.4028234663852886e+38F32", "__DEC128_EPSILON__=1E-33DL", "__PTRDIFF_MAX__=0x7fffffff", "__FLT32_HAS_QUIET_NAN__=1", "__GNUG__=8", "__LONG_LONG_MAX__=0x7fffffffffffffffLL", "__SIZEOF_SIZE_T__=4", "__cpp_rvalue_reference=200610", "__cpp_nsdmi=200809", "__SIZEOF_WINT_T__=4", "__LONG_LONG_WIDTH__=64", "__cpp_initializer_lists=200806", "__FLT32_MAX_EXP__=128", "__cpp_hex_float=201603", "__GXX_ABI_VERSION=1013", "__FLT_MIN_EXP__=(-125)", "__cpp_lambdas=200907", "__INT_FAST64_TYPE__=long long int", "__FP_FAST_FMAF=1", "__FLT64_DENORM_MIN__=4.9406564584124654e-324F64", "__DBL_MIN__=double(2.2250738585072014e-308L)", "__FLT32X_EPSILON__=2.2204460492503131e-16F32x", "__FLT64_MIN_10_EXP__=(-307)", "__DEC128_MIN__=1E-6143DL", "__REGISTER_PREFIX__", "__UINT16_MAX__=0xffff", "__DBL_HAS_DENORM__=1", "__FLT32_MIN__=1.1754943508222875e-38F32", "__UINT8_TYPE__=unsigned char", "__NO_INLINE__=1", "__FLT_MANT_DIG__=24", "__LDBL_DECIMAL_DIG__=17", "__VERSION__=\"8.4.0\"", "__UINT64_C(c)=c ## ULL", "__cpp_unicode_characters=200704", "__cpp_decltype_auto=201304", "__GCC_ATOMIC_INT_LOCK_FREE=2", "__FLT32_MANT_DIG__=24", "__FLOAT_WORD_ORDER__=__ORDER_LITTLE_ENDIAN__", "__SCHAR_WIDTH__=8", "__INT32_C(c)=c", "__DEC64_EPSILON__=1E-15DD", "__ORDER_PDP_ENDIAN__=3412", "__DEC128_MIN_EXP__=(-6142)", "__FLT32_MAX_10_EXP__=38", "__INT_FAST32_TYPE__=int", "__UINT_LEAST16_TYPE__=short unsigned int", "__INT16_MAX__=0x7fff", "__cpp_rtti=199711", "__SIZE_TYPE__=unsigned int", "__UINT64_MAX__=0xffffffffffffffffULL", "__INT8_TYPE__=signed char", "__cpp_digit_separators=201309", "__ELF__=1", "__xtensa__=1", "__FLT_RADIX__=2", "__INT_LEAST16_TYPE__=short int", "__LDBL_EPSILON__=2.2204460492503131e-16L", "__UINTMAX_C(c)=c ## ULL", "__SIG_ATOMIC_MAX__=0x7fffffff", "__GCC_ATOMIC_WCHAR_T_LOCK_FREE=2", "__SIZEOF_PTRDIFF_T__=4", "__FLT32X_MANT_DIG__=53", "__FLT32X_MIN_EXP__=(-1021)", "__DEC32_SUBNORMAL_MIN__=0.000001E-95DF", "__INT_FAST16_MAX__=0x7fffffff", "__FLT64_DIG__=15", "__UINT_FAST32_MAX__=0xffffffffU", "__UINT_LEAST64_TYPE__=long long unsigned int", "__FLT_HAS_QUIET_NAN__=1", "__FLT_MAX_10_EXP__=38", "__LONG_MAX__=0x7fffffffL", "__DEC128_SUBNORMAL_MIN__=0.000000000000000000000000000000001E-6143DL", "__FLT_HAS_INFINITY__=1", "__cpp_unicode_literals=200710", "__UINT_FAST16_TYPE__=unsigned int", "__DEC64_MAX__=9.999999999999999E384DD", "__INT_FAST32_WIDTH__=32", "__CHAR16_TYPE__=short unsigned int", "__PRAGMA_REDEFINE_EXTNAME=1", "__SIZE_WIDTH__=32", "__INT_LEAST16_MAX__=0x7fff", "__DEC64_MANT_DIG__=16", "__INT64_MAX__=0x7fffffffffffffffLL", "__UINT_LEAST32_MAX__=0xffffffffU", "__FLT32_DENORM_MIN__=1.4012984643248171e-45F32", "__GCC_ATOMIC_LONG_LOCK_FREE=2", "__SIG_ATOMIC_WIDTH__=32", "__INT_LEAST64_TYPE__=long long int", "__INT16_TYPE__=short int", "__INT_LEAST8_TYPE__=signed char", "__DEC32_MAX_EXP__=97", "__INT_FAST8_MAX__=0x7fffffff", "__INTPTR_MAX__=0x7fffffff", "__cpp_sized_deallocation=201309", "__cpp_range_based_for=200907", "__FLT64_HAS_QUIET_NAN__=1", "__FLT32_MIN_10_EXP__=(-37)", "__EXCEPTIONS=1", "__LDBL_MANT_DIG__=53", "__DBL_HAS_QUIET_NAN__=1", "__FLT64_HAS_INFINITY__=1", "__SIG_ATOMIC_MIN__=(-__SIG_ATOMIC_MAX__ - 1)", "__cpp_return_type_deduction=201304", "__INTPTR_TYPE__=int", "__UINT16_TYPE__=short unsigned int", "__WCHAR_TYPE__=short unsigned int", "__SIZEOF_FLOAT__=4", "__UINTPTR_MAX__=0xffffffffU", "__INT_FAST64_WIDTH__=64", "__DEC64_MIN_EXP__=(-382)", "__cpp_decltype=200707", "__FLT32_DECIMAL_DIG__=9", "__INT_FAST64_MAX__=0x7fffffffffffffffLL", "__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1", "__FLT_DIG__=6", "__UINT_FAST64_TYPE__=long long unsigned int", "__INT_MAX__=0x7fffffff", "__INT64_TYPE__=long long int", "__FLT_MAX_EXP__=128", "__DBL_MANT_DIG__=53", "__cpp_inheriting_constructors=201511", "__INT_LEAST64_MAX__=0x7fffffffffffffffLL", "__FP_FAST_FMAF32=1", "__DEC64_MIN__=1E-383DD", "__WINT_TYPE__=unsigned int", "__UINT_LEAST32_TYPE__=unsigned int", "__SIZEOF_SHORT__=2", "__LDBL_MIN_EXP__=(-1021)", "__FLT64_MAX__=1.7976931348623157e+308F64", "__WINT_WIDTH__=32", "__INT_LEAST8_MAX__=0x7f", "__FLT32X_MAX_10_EXP__=308", "__WCHAR_UNSIGNED__=1", "__LDBL_MAX_10_EXP__=308", "__ATOMIC_RELAXED=0", "__DBL_EPSILON__=double(2.2204460492503131e-16L)", "__XTENSA_WINDOWED_ABI__=1", "__UINT8_C(c)=c", "__FLT64_MAX_EXP__=1024", "__INT_LEAST32_TYPE__=int", "__SIZEOF_WCHAR_T__=2", "__INT_FAST8_TYPE__=int", "__GNUC_STDC_INLINE__=1", "__FLT64_HAS_DENORM__=1", "__FLT32_EPSILON__=1.1920928955078125e-7F32", "__DBL_DECIMAL_DIG__=17", "__STDC_UTF_32__=1", "__INT_FAST8_WIDTH__=32", "__DEC_EVAL_METHOD__=2", "__FLT32X_MAX__=1.7976931348623157e+308F32x", "__XTENSA__=1", "__ORDER_BIG_ENDIAN__=4321", "__cpp_runtime_arrays=198712", "__UINT64_TYPE__=long long unsigned int", "__UINT32_C(c)=c ## U", "__INTMAX_MAX__=0x7fffffffffffffffLL", "__cpp_alias_templates=200704", "__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__", "__FLT_DENORM_MIN__=1.4012984643248171e-45F", "__INT8_MAX__=0x7f", "__LONG_WIDTH__=32", "__UINT_FAST32_TYPE__=unsigned int", "__CHAR32_TYPE__=unsigned int", "__FLT_MAX__=3.4028234663852886e+38F", "__cpp_constexpr=201304", "__INT32_TYPE__=int", "__SIZEOF_DOUBLE__=8", "__cpp_exceptions=199711", "__FLT_MIN_10_EXP__=(-37)", "__FLT64_MIN__=2.2250738585072014e-308F64", "__INT_LEAST32_WIDTH__=32", "__INTMAX_TYPE__=long long int", "__DEC128_MAX_EXP__=6145", "__FLT32X_HAS_QUIET_NAN__=1", "__ATOMIC_CONSUME=1", "__GNUC_MINOR__=4", "__INT_FAST16_WIDTH__=32", "__UINTMAX_MAX__=0xffffffffffffffffULL", "__DEC32_MANT_DIG__=7", "__FLT32X_DENORM_MIN__=4.9406564584124654e-324F32x", "__DBL_MAX_10_EXP__=308", "__LDBL_DENORM_MIN__=4.9406564584124654e-324L", "__INT16_C(c)=c", "__cpp_generic_lambdas=201304", "__STDC__=1", "__FLT32X_DIG__=15", "__PTRDIFF_TYPE__=int", "__ATOMIC_SEQ_CST=5", "__UINT32_TYPE__=unsigned int", "__FLT32X_MIN_10_EXP__=(-307)", "__UINTPTR_TYPE__=unsigned int", "__DEC64_SUBNORMAL_MIN__=0.000000000000001E-383DD", "__DEC128_MANT_DIG__=34", "__LDBL_MIN_10_EXP__=(-307)", "__SIZEOF_LONG_LONG__=8", "__cpp_user_defined_literals=200809", "__GCC_ATOMIC_LLONG_LOCK_FREE=1", "__FLT32X_MIN__=2.2250738585072014e-308F32x", "__LDBL_DIG__=15", "__FLT_DECIMAL_DIG__=9", "__UINT_FAST16_MAX__=0xffffffffU", "__GCC_ATOMIC_SHORT_LOCK_FREE=2", "__INT_LEAST64_WIDTH__=64", "__UINT_FAST8_TYPE__=unsigned int", "__cpp_init_captures=201304", "__ATOMIC_ACQ_REL=4", "__ATOMIC_RELEASE=3", "USBCON"]}]}