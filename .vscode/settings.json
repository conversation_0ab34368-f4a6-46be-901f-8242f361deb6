{"debug.internalConsoleOptions": "neverOpen", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "eslint.validate": ["typescript", "typescriptreact"], "i18n-ally.defaultNamespace": "translation", "i18n-ally.dirStructure": "file", "i18n-ally.displayLanguage": "de", "i18n-ally.enabledFrameworks": ["react", "i18next"], "i18n-ally.keystyle": "nested", "i18n-ally.localesPaths": ["frontend/src/locales"], "i18n-ally.namespace": true, "i18n-ally.sortKeys": true, "i18n-ally.sourceLanguage": "de", "jest.jestCommandLine": "yarn test", "prettier.prettierPath": "", "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}}