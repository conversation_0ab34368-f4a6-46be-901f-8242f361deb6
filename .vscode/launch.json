{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Edge against localhost",
      "request": "launch",
      "type": "pwa-msedge",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Launch Chrome against localhost",
      "linux": {},
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Launch Linux Chromium against localhost",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}",
      "runtimeExecutable": "/usr/bin/chromium",
      "runtimeArgs": [
        "--new-window",
        "-user-data-dir=\"/${workspaceFolder}/DevProfile\"",
        "--remote-debugging-port=9222",
        "--disable-background-networking"
      ]
    },
    {
      "type": "node",
      "name": "vscode-jest-tests",
      "request": "launch",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "disableOptimisticBPs": true,
      "runtimeExecutable": "yarn",
      "protocol": "inspector",
      "cwd": "${workspaceFolder}",
      "args": ["test", "test", "--runInBand", "--watchAll=false"]
    }
  ]
}
