import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import gitRevSync from 'git-rev-sync'
import { defineConfig } from 'vite'
// import { VitePWA } from 'vite-plugin-pwa'
import svgrPlugin from 'vite-plugin-svgr'
import tsconfigPaths from 'vite-tsconfig-paths'

// https://vitejs.dev/config/
const config = {
  plugins: [
    tsconfigPaths(),
    svgrPlugin(),
    react(),
    tailwindcss(),
    // VitePWA({
    //   srcDir: 'src',
    //   filename: 'service-worker.ts',
    //   strategies: 'injectManifest',
    //   injectRegister: false,
    //   manifest: false,
    //   injectManifest: {
    //     injectionPoint: undefined,
    //     // plugins: [tsconfigPaths()],
    //   },
    //   devOptions: {
    //     enabled: true,
    //     type: 'module', // dont working in firefox
    //   },
    // }),
  ],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        ws: true,
      },
    },
  },
  resolve: {
    alias: {
      // bug because of import of enums from prisma client
      '.prisma/client/index-browser': '../node_modules/.prisma/client/index-browser.js',
    },
  },
  define: {
    BUILD_TIMESTAMP: JSON.stringify(new Date().toISOString()),
  },
  optimizeDeps: {
    esbuildOptions: {
      target: 'esnext',
    },
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './tests/setupTests.ts',
    coverage: {
      reporter: ['text', 'html'],
      exclude: ['node_modules/', 'tests/'],
    },
  },
}

export default defineConfig(config)
