# WebAuthn Implementation in Frontend

This document describes the WebAuthn (Web Authentication) implementation added to the frontend web application, enabling passwordless authentication using biometric data, security keys, or platform authenticators.

## Overview

WebAuthn has been integrated into the existing authentication system, providing users with a modern, secure, and convenient way to authenticate using:

- **Platform Authenticators**: Face ID, Touch ID, Windows Hello, Android biometrics
- **Cross-platform Authenticators**: USB security keys, Bluetooth authenticators
- **Hybrid Transport**: QR code-based authentication with mobile devices

## Architecture

### Core Components

#### WebAuthn Service (`components/Authentication/WebAuthn.ts`)
The main service handling WebAuthn operations:

- **Registration Flow**: Creates new credentials for users
- **Authentication Flow**: Authenticates users with existing credentials
- **Browser Support Detection**: Checks WebAuthn availability
- **Platform Authenticator Detection**: Identifies biometric capabilities
- **Base64URL Encoding/Decoding**: Handles credential data conversion

#### WebAuthn Button Component (`components/Authentication/WebAuthnButton.tsx`)
React component providing the user interface:

- **Mode Support**: Works for both login and registration
- **Email Input**: Collects user email when needed
- **Loading States**: Shows authentication progress
- **Error Handling**: Displays user-friendly error messages
- **Responsive Design**: Mobile-friendly interface

### Integration Points

#### Login Page (`page/LoginPage.tsx`)
- WebAuthn button added below traditional login form
- Shares email input with WebAuthn component
- Unified error handling

#### Register Page (`page/RegisterPage.tsx`)
- WebAuthn registration option available
- Email input integration
- Consistent user experience

## API Integration

The WebAuthn implementation expects the following backend endpoints:

### Registration Flow
```
POST /auth/webauthn/register/begin
Body: { email: string }
Response: WebAuthnRegistrationOptions

POST /auth/webauthn/register/finish
Body: { email: string, registrationResponse: RegistrationResponseJSON }
Response: { user: User, token: string }
```

### Authentication Flow
```
POST /auth/webauthn/authenticate/begin
Body: { email: string }
Response: WebAuthnAuthenticationOptions

POST /auth/webauthn/authenticate/finish
Body: { email: string, authenticationResponse: AuthenticationResponseJSON }
Response: { user: User, token: string }
```

## Security Features

### Credential Creation
- **Challenge-based**: Server-generated random challenges prevent replay attacks
- **Origin Verification**: Credentials are bound to the specific domain
- **User Verification**: Requires biometric or PIN verification
- **Resident Keys**: Credentials can be stored on the authenticator

### Authentication Process
- **Cryptographic Signatures**: Each authentication creates a unique signature
- **Counter Verification**: Prevents cloned authenticators
- **User Presence**: Confirms user interaction with authenticator
- **Timeout Protection**: Prevents indefinite authentication attempts

## User Experience

### Registration Process
1. User enters email address
2. Clicks "Mit Passkey registrieren" button
3. Browser prompts for biometric authentication
4. Credential is created and stored on device
5. User is automatically logged in

### Login Process
1. User enters email address (or uses remembered email)
2. Clicks "Mit Passkey anmelden" button
3. Browser prompts for biometric authentication
4. User is authenticated and logged in

### Error Handling
- **Browser Compatibility**: Graceful degradation for unsupported browsers
- **User Cancellation**: Handles user canceling authentication
- **Network Errors**: Proper error messages for connection issues
- **Invalid Credentials**: Clear feedback for authentication failures

## Browser Support

### Supported Browsers
- **Chrome/Edge**: Full WebAuthn support including platform authenticators
- **Firefox**: WebAuthn support with security keys and platform authenticators
- **Safari**: WebAuthn support on macOS and iOS with Touch ID/Face ID

### Feature Detection
The implementation includes comprehensive feature detection:
```typescript
// Check basic WebAuthn support
isWebAuthnSupported(): boolean

// Check platform authenticator availability
isPlatformAuthenticatorAvailable(): Promise<boolean>
```

## Localization

Full German localization is provided:
- `orUsePasskey`: "oder Passkey verwenden"
- `signInWithPasskey`: "Mit Passkey anmelden"
- `signUpWithPasskey`: "Mit Passkey registrieren"
- `authenticating`: "Authentifizierung..."
- `passkeyDescription`: "Verwenden Sie Ihren Fingerabdruck, Face ID oder andere biometrische Daten"

## Implementation Details

### Base64URL Encoding
WebAuthn uses base64url encoding for binary data:
```typescript
function base64urlToBuffer(base64url: string): ArrayBuffer
function bufferToBase64url(buffer: ArrayBuffer): string
```

### Credential Options
Registration options include:
- **Challenge**: Random server-generated challenge
- **Relying Party**: Application identification
- **User Information**: User ID, name, and display name
- **Algorithm Preferences**: Supported cryptographic algorithms
- **Authenticator Selection**: Platform vs cross-platform preferences

### Error Handling
Comprehensive error handling for:
- Browser compatibility issues
- User cancellation
- Network connectivity problems
- Invalid server responses
- Authenticator errors

## Testing

### Manual Testing
1. **Registration**: Test with different authenticator types
2. **Authentication**: Verify login with created credentials
3. **Error Cases**: Test network failures and user cancellation
4. **Browser Compatibility**: Test across different browsers

### Automated Testing
Consider implementing:
- Unit tests for WebAuthn service functions
- Integration tests for authentication flows
- Browser compatibility tests
- Error handling tests

## Future Enhancements

### Planned Features
- **Multiple Credentials**: Support for multiple authenticators per user
- **Credential Management**: UI for managing registered authenticators
- **Backup Codes**: Fallback authentication method
- **Advanced Options**: Authenticator attachment preferences

### Security Improvements
- **Attestation Verification**: Verify authenticator attestation statements
- **Risk Assessment**: Implement risk-based authentication
- **Session Management**: Enhanced session security with WebAuthn

## Troubleshooting

### Common Issues
1. **"WebAuthn not supported"**: Browser doesn't support WebAuthn API
2. **"No authenticator available"**: No biometric or security key available
3. **"User cancelled"**: User cancelled authentication prompt
4. **"Network error"**: Server communication failed

### Debug Information
Enable browser developer tools to see:
- WebAuthn API calls
- Credential creation/get requests
- Server communication
- Error details

## Configuration

### Development Setup
Ensure HTTPS is used for WebAuthn to work properly:
```javascript
// Development server should use HTTPS
// WebAuthn requires secure context
```

### Production Deployment
- Configure proper HTTPS certificates
- Set correct Relying Party ID
- Implement proper CORS policies
- Monitor WebAuthn usage and errors

This WebAuthn implementation provides a modern, secure, and user-friendly authentication option that enhances the overall security posture of the application while improving user experience.
