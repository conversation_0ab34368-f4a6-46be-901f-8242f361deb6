{
  "compilerOptions": {
    "target": "es2021",
    "useDefineForClassFields": true,
    "lib": ["ES2021", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "types": [
      "vite/client",
      "vite-plugin-svgr/client",
      "vitest/globals",
      "vite-plugin-pwa/client",
      "@testing-library/react",
      "@testing-library/jest-dom"
    ],
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    // "moduleResolution": "node",
    "baseUrl": "src",
    "paths": {
      "@backend/*": ["../../backend/src/*"],
      "@backendZod/*": ["../../backend/prisma/zod/*"],
      "@root/*": ["../*"],
      "@tests/*": ["../tests/*"],
      "@prisma/*": ["../../node_modules/.prisma/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
