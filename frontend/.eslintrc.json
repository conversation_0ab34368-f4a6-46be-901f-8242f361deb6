{"env": {"browser": true, "node": true, "es2021": true, "jest/globals": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:prettier/recommended", "plugin:import/recommended", "plugin:import/typescript", "prettier", "plugin:@tanstack/eslint-plugin-query/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "jest", "react", "prettier", "import-helpers", "import", "no-relative-import-paths", "@tanstack/query"], "settings": {"import/extensions": [".js", ".jsx", ".ts", ".tsx"], "import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"node": {"extensions": [".js", ".jsx", ".ts", ".tsx"]}, "typescript": {"project": "./frontend/tsconfig.json"}}, "react": {"version": "detect"}}, "rules": {"import-helpers/order-imports": ["warn", {"alphabetize": {"ignoreCase": true, "order": "asc", "natural": true}, "groups": ["module", ["/^assets/", "/^icons/", "/^images/", "/^locales/", "/^styles/"], ["/^services/", "/^helpers/", "/^types/", "/^hooks/", "/^middleware/"], "/^routes/", "/^components/", ["/^@root/", "/^@tests/"], ["parent", "sibling", "index"]], "newlinesBetween": "always"}], "import/no-restricted-paths": ["error", {"zones": [{"except": ["./index.ts"], "from": "./lib/client/i18n", "target": "./**/!(*.test).{ts,tsx}"}]}], "no-relative-import-paths/no-relative-import-paths": ["warn", {"allowSameFolder": false, "rootDir": "src"}], "no-duplicate-imports": "error", "no-restricted-imports": ["warn", {"patterns": ["*../*"]}], "quotes": ["error", "single", {"avoidEscape": true}], "react/jsx-pascal-case": ["warn"], "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "sort-imports": ["warn", {"ignoreCase": true, "ignoreDeclarationSort": true, "ignoreMemberSort": false, "memberSyntaxSortOrder": ["none", "all", "single", "multiple"], "allowSeparatedGroups": true}], "spaced-comment": ["error", "always", {"markers": ["/"]}], "react/display-name": "warn", "react/no-children-prop": "warn"}}