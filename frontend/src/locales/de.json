{"translation": {"appname": "<PERSON><PERSON><PERSON><PERSON>", "common": {"cancel": "Abbrechen", "close": "Schließen", "deactivate": "Deaktivieren", "delete": "Löschen", "discard": "Verwerfen", "discardChanges": "<PERSON>s gibt nicht gespeicherte Änderungen", "discardChangesDescription": "Sind <PERSON> sicher, dass Sie Ihre Änderungen verwerfen und diese Seite verlassen möchten?", "discardDeleteDescription": "Diese Aktion kann nicht rückgängig gemacht werden.", "edit": "<PERSON><PERSON><PERSON>", "errorOccured": "Es ist ein unerwarteter Fehler aufgetreten.", "loading": "Laden...", "menu": "<PERSON><PERSON>", "no": "<PERSON><PERSON>", "requiredField": "{{label}} (<PERSON><PERSON><PERSON><PERSON>)", "save": "Speichern", "yes": "<PERSON>a", "continue": "<PERSON><PERSON>", "activate": "Aktivieren", "dismiss": "Schließen", "on": "Einschahlten", "off": "Ausschalten", "upload": "Hochladen", "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "noItems": "<PERSON><PERSON> Ein<PERSON>ä<PERSON> vorhanden", "createNewText": "Füge einen neuen Eintrag hinzu", "openContextMenu": "Ö<PERSON>ne <PERSON>", "deleteDescription": "Möchten Sie diesen Eintrag wirklich löschen?", "amount": "<PERSON><PERSON>", "name": "Name", "temperature": "Temperatur", "open": "<PERSON><PERSON><PERSON>", "password": "Passwort", "username": "<PERSON><PERSON><PERSON><PERSON>", "login": "Anmelden", "register": "Registrieren", "forgotPassword": "Passwort vergessen?", "email": "E-Mail", "heating": "<PERSON><PERSON><PERSON><PERSON>", "cooling": "Kühlung", "createdAt": "Erstellt am {{date}}", "recreate": "<PERSON><PERSON><PERSON><PERSON>", "offline": "Offline", "online": "Online", "standBy": "Standby", "start": "Starten", "table": "<PERSON><PERSON><PERSON>", "graph": "<PERSON><PERSON>", "connect": "Verbinden"}, "navigation": {"dashboard": "Dashboard", "brewing": "<PERSON><PERSON><PERSON>", "fermentation": "Gärung", "inventory": "Inventar", "setting": "Einstellungen", "recipe": "Rezepte", "notifications": "Benachrichtigungen", "device": "<PERSON><PERSON><PERSON><PERSON>", "userSettings": "Einstellungen", "logout": "Abmelden", "brewlog": "<PERSON><PERSON><PERSON><PERSON>"}, "fermentation": {"temperature": "Temperatur", "hasPushOnReached": "Push Meldung bei Erreichen der Temperatur", "name": "<PERSON><PERSON><PERSON><PERSON>", "formDescription": "Setzte die gewünschte Temperatur für den Gärschrank", "coldCrush": "Cold Crush", "shouldTemperature": "<PERSON><PERSON><PERSON>", "isTemperature": "Ist", "workingUnlimited": "l<PERSON><PERSON>t unbefristet", "noActiveFermentation": "Keine aktive Gärung", "noActiveFermentationHelp": "Starte eine Gärung vom Rezept aus", "startFermentation": "Gärung starten", "activeFermentation": "Gärung läuft", "selectDevices": "Gerät fürs Gären", "monitor": "Gärungsmonitor", "trackAndControl": "Überwache und kontrolliere deine Fermentationsgeräte", "currentTemperature": "Aktuelle Temperatur", "targetTemperature": "Zieltemperatur", "spindleInformation": "Spindel Information", "plato": "<PERSON>", "spindleName": "Spindel Name", "notAvailable": "Nicht verfügbar", "coldCrash": "Cold Crash", "turnOff": "Ausschalten", "setPlatoAlert": "Plato-Al<PERSON> e<PERSON>tellen", "statistics": "Statistiken", "time": "Zeit", "simplifiedStats": "Dies ist eine vereinfachte Statistikansicht. In einer echten Anwendung würde hier ein richtiges Diagramm angezeigt werden.", "unnamedDevice": "Unbenanntes Gerät", "deviceNotFound": "Gerät nicht gefunden", "backToDashboard": "Zurück zum Dashboard", "back": "Zurück", "title": "Titel", "platoThreshold": "<PERSON> (°P)", "currentPlato": "Aktueller Plato", "setPlatoAlertTitle": "Plato-Al<PERSON> e<PERSON>tellen", "platoAlertDescription": "Sie erhalten eine Benachrichtigung, wenn der Plato-<PERSON>rt den von Ihnen festgelegten Schwellenwert erreicht.", "noDevicesFound": "<PERSON><PERSON> G<PERSON> gefunden", "recipeName": "Rezeptname", "noSpindleConnected": "<PERSON>ine <PERSON>del verbunden. Verbinde eine Spindel, um den Plato-Wert zu überwachen.", "connectSpindle": "Spindel verbinden", "temperatureChangeLastHour": "Temperaturänderung in der letzten Stunde: {{value}} °C", "notificationPowerOff": "Gärung ausgeschaltet", "notificationTemperatureSet": "Gärung auf {{temperature}} °C eingestellt", "selectSpindle": "Spindel au<PERSON>wählen", "selectableSpindle": "<PERSON><PERSON>", "spindelAlreadyConnected": "Spindel ist bereits mit {{deviceName}} verbunden", "spindleConnected": "Spindel verbunden", "noSpindleData": "<PERSON><PERSON> verfü<PERSON>", "gravityChangeLastHour": "Gravitätsänderung in der letzten Stunde: {{value}} °P"}, "time": {"seconds": "Sekunden", "minutes": "Minuten", "hours": "Stunden", "days": "Tage"}, "hop": {"title": "Hopfen", "defaultUnit": "g", "alpha": "Alpha (in %)"}, "yeast": {"title": "<PERSON><PERSON>", "defaultUnit": "Packung"}, "malt": {"title": "Malz", "defaultUnit": "kg"}, "inventory": {"name": "Inventar", "everything": "Alles", "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "createNewText": "Füge einen neuen Eintrag zum Inventar hinzu", "type": "<PERSON><PERSON>", "nameLabel": "Name", "amount": "<PERSON><PERSON>", "unit": "Einheit", "malt": {"title": "Malz", "defaultUnit": "kg"}, "other": {"title": "Sonstiges", "defaultUnit": "Stück"}}, "brew": {"name": "<PERSON><PERSON><PERSON>", "start": "Sud kochen", "boilWort": "<PERSON><PERSON><PERSON><PERSON> kochen", "boilMash": "Maischeplan", "boilHop": "Hopfengaben", "fermentation": "Gärführung", "ingredients": "<PERSON><PERSON><PERSON><PERSON>", "boil": "<PERSON><PERSON>", "single": "Sud", "timer_zero": "{{m}}:{{s}}", "timer_other": "{{m}}:{{s}} von {{count}} min", "timerTitle": "{{name}} bei {{temperature}} °C ({{type}})", "mashboil": "Maischekochen", "mashing": "Maischen", "actualProcess": "Laufende Prozesse", "noProcessAtTheMoment": "<PERSON><PERSON> laufenden Prozesse im Moment", "selectDevices": "Gerät fürs Brauen", "createdByRecipe": "Erstellt aus Rezept: ", "heating": "<PERSON><PERSON><PERSON><PERSON> auf {{temperature}} °C", "startMashing": "Maischevorgang starten", "fermentables": "<PERSON><PERSON><PERSON>", "notifications": {"hopsTitle": "Hopfen zum Kochen hinzufügen", "hopsText": "<PERSON>üge den Hopfen {{hop}} zum Kochen hinzu"}, "startBoil": "Kochvorgang starten", "boilTimer": "Kochvorgang l<PERSON>"}, "recipe": {"ingredients": "<PERSON><PERSON><PERSON><PERSON>", "name": "Rezepte", "single": "Rezept", "beerXmlUpload": "BeerXML hochladen", "beerXmlDescription": "Lade ein BeerXML hoch, um ein Rezept zu erstellen", "file": "<PERSON><PERSON>", "createdAt": "Erstellt am {{date}}", "cooked": "Gekocht", "inProgress": "In Arbeit", "notCooked": "<PERSON><PERSON> gek<PERSON>t", "createdByBeerXml": "Erstellt aus BeerXML", "viewRecipe": "<PERSON><PERSON><PERSON>", "cook": "Sud kochen", "fermentate": "G<PERSON><PERSON>rank starten", "inInventory": "<PERSON><PERSON>", "optimalTemperature": "Optimale Temperatur", "downloadOrPrint": "Rezept download oder drucken", "messageFermentationStarted": "Gärschrank mit der Temperatur {{temperature}} °C gestartet", "dryHop": "Hopfenstopfen", "firstWort": "Vorderwürzhopfen", "hopOnBoil": "{{time}} <PERSON><PERSON><PERSON> vor <PERSON>", "hopOnWhirlpool": "Whirlpool", "showAmountHops": "Zeige Gesamtmenge an Hopfen", "showHopsRecipe": "Zeige Hopfen nach Rezept", "time": "<PERSON><PERSON><PERSON>", "ibu": "IBU", "alcohol": "Alkohol", "yeastNotFoundSelectAnother": "<PERSON><PERSON> nicht gefunden, bitte wähle die Hefe aus der Liste aus", "startBrew": "Sud starten", "start": "Starten", "allTimeForToday": "Gesamtzeit: {{time}} min", "steps": "<PERSON><PERSON><PERSON>", "boil": "Maischeplan", "hops": "Hopfengaben", "dryHopping": "Hopfenstopfen", "fermentation": "Gärführung", "importedFromBraureka": "Importiert aus Braureka", "openBraureka": "Öffne Rezept in Braureka", "mashStep": " bei {{temp}} °C für {{time}} min ({{type}})", "uploadPdf": "PDF hochladen", "uploadPdfDescription": "Mit Hilfe künstlicher Inteligenz wird das Rezept aus der PDF importiert, aber auch eine KI kann Fehler machen!", "openRecipe": "<PERSON><PERSON><PERSON>", "recipeCreated": "Rezept erfolgreich aus der PDF erstellt", "noFermentationTemperatureExist": "<PERSON>ine Gärtemperatur gefunden"}, "notification": {"push": {"alert": {"title": "Push Notification aktivieren", "body": "Möchten Sie Push Notifications aktivieren?"}, "messages": {"temperatureReached": "Die Temperatur von {{temperature}} °C ist im Gärschrank erreicht."}}, "error": {"general": "<PERSON>s ist ein Fehler aufgetreten.", "saved": "Es ist ein Fehler beim Speichern aufgetreten."}, "success": {"saved": "Erfolgreich gespeichert."}}, "device": {"addDevice": "Ger<PERSON> hinzufügen", "deviceType": "Gerätetyp", "type": {"temperature": "Thermometer", "switch": "<PERSON><PERSON><PERSON>", "ispindel": "iSpindel", "other": "Anderes", "brewtool": "Brewtool"}, "brewing": "<PERSON><PERSON><PERSON>", "fermentation": "Gärung", "mqttClientId": "MQTT Client ID", "mqtt": "MQTT", "createMqtt": "<PERSON><PERSON><PERSON>", "deviceTypeDescription": "<PERSON><PERSON><PERSON><PERSON> Sie aus für was das Geräte genutzt werden soll", "addTuyaDevice": "Tuya Gerät hinzufügen", "tuyaDeviceId": "Tuya Gerät ID", "tuyaDeviceIdNotFound": "Tuya Gerät ID nicht gefunden", "tuyaDeviceIdAlreadyExist": "Ger<PERSON> wurde schon hinzugefügt", "addSmartLifeDevice": "Smart Life hinzufügen", "searchDevices": "<PERSON><PERSON><PERSON>", "searchForDevices": "<PERSON><PERSON> starten", "deviceNotFound": "Gerät nicht gefunden", "password": "Passwort", "ssid": "WLAN Name / SSID", "registerDevice": "Gerät registrieren", "hasInternetConnection": "Internetverbindung vorhanden", "hasNoInternetConnection": "<PERSON>ine Internetverbindung vorhanden", "connectedWithDevice": "Verbunden mit Gerät: {{name}}", "bluetoothNotWork": "Bluetooth funktioniert nicht in diesen Browser. Bitte verwenden Sie einen anderen Browser oder ein anderes Gerät."}, "user": {"buttonDeleteAccount": "<PERSON><PERSON>, entferne mein <PERSON>", "deleteAccount": "Konto löschen", "deleteAccountHelp": "Sie möchten unseren Service nicht mehr nutzen? Hier können Sie Ihr Konto löschen. Diese Aktion ist nicht reversibel. Alle mit diesem Konto verbundenen Informationen werden dauerhaft gelöscht.", "fullName": "Name", "email": "E-Mail", "profile": "Profil", "profileHelp": "<PERSON><PERSON> können Sie Ihr Profil bearbeiten", "changePasswordHelp": "<PERSON>er können Sie Ihr Passwort ändern", "changePassword": "Passwort ändern", "confirmPassword": "Passwort wiederholen", "integrationActive": "Integration eingerichtet", "integrationDeactivated": "Integration nicht aktiviert", "account": "Ko<PERSON>", "braureka": {"sectionTitle": "Braureka Integration", "help": "Braureka ist eine Webanwendung, die <PERSON><PERSON> hilft, <PERSON>hre Brauerei zu verwalten. Sie können Rezepte erstellen. Sie können Braureka verwenden, um Rezepte zu erstellen und sie hier automatisch zu importieren."}, "tuya": {"sectionTitle": "Tuya Integration", "help": "<PERSON><PERSON> ist ein Hersteller für smarte Produkte. Mit dieser Integration können Sie Ihre smarten Geräte steuern. Sie können die Temperatur des Gärschranks steuern oder Geräte ein- und ausschalten.", "accessKey": "Access Key", "secretKey": "Secret Key"}, "pushSubscriptions": "<PERSON>ush Benachrichtigungen", "pushSubscriptionsHelp": "<PERSON>er können Sie Push Benachrichtigungenfür verschiedene Geräte verwalten", "browser": "Browser", "os": "System", "product": "Produkt", "createdAt": "Erstellt am", "subscriptionSelf": "<PERSON>ser Browser wurde für Benachrichtigungen registriert", "subscriptionSelfHelp": "Sie können Benachrichtigungen für diesen Browser aktivieren oder deaktivieren"}, "authentication": {"email": "E-Mail", "password": "Passwort", "login": "Anmelden", "register": "Registrieren", "forgotPassword": "Passwort vergessen?", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "resetPassword": "Passwort zurücksetzen", "resetPasswordDescription": "Geben Sie Ihre E-Mail-Adresse ein, um Ihr Passwort zurückzusetzen", "resetPasswordButton": "Passwort zurücksetzen", "resetPasswordSuccess": "Eine E-Mail wurde an Ihre E-Mail-Adresse gesendet", "emailOrPasswordWrong": "E-Mail oder Passwort falsch", "signInToYourAccount": "Melden Sie sich bei Ihrem <PERSON> an", "orContinueWith": "oder Anmelden mit", "statusOk": "Alle Systeme arbeiten", "statusProblem": "<PERSON><PERSON> gibt ein Problem mit dem System", "notAMember": "Noch kein Mitglied?", "backToLogin": "<PERSON><PERSON><PERSON> zum Login", "passwordRepeat": "Passwort wiederholen", "registerDontWork": "Registrierung moment nicht möglich", "orUsePasskey": "oder Passkey verwenden", "signInWithPasskey": "<PERSON><PERSON> <PERSON> anmelden", "signUpWithPasskey": "Mit Passkey registrieren", "authenticating": "Authentifizierung...", "webauthnError": "Ein Fehler ist bei der Passkey-Authentifizierung aufgetreten", "passkeyDescription": "<PERSON>erwen<PERSON> Si<PERSON>, Face ID oder andere biometrische Daten", "emailPlaceholder": "<EMAIL>"}, "cold": {"crush": "Cold Crush"}, "success": {"saved": "Gespe<PERSON>rt"}, "error": {"occurred": "<PERSON><PERSON>"}, "gaerschrank": {"turnedOff": "Gärschrank ausgeschaltet", "turnedOffFailed": "Gärschrank ausschalten fehlgeschlagen"}, "ble": {"connectToBrewtool": "Mit Brewtool verbinden", "unknownBrewtool": "Unbekanntes Brewtool", "unknownError": "Unbekannter Fehler", "selectBrewtool": "Brewtool auswählen", "selectBrewtoolDescription": "Wählen Sie ein Brewtool-Gerät aus der Liste aus", "selectDevice": "Gerät auswählen", "connecting": "Verbin<PERSON>...", "deviceInformation": "Geräteinformationen", "connectionStatus": "Verbindungss<PERSON>us", "testConnection": "Verbindung testen", "internetConnection": "Internetverbindung", "online": "Online", "offline": "Offline", "firmwareVersion": "Firmware-Version", "basicConfiguration": "Grundkonfiguration", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "enterDeviceName": "Ger<PERSON><PERSON><PERSON> e<PERSON>ben", "wifiConfiguration": "WLAN-Konfiguration", "enableWifi": "WLAN aktivieren", "wifiNetwork": "WLAN-Netzwerk", "enterWifiName": "WLAN-Name eingeben", "wifiPassword": "WLAN-Passwort", "enterWifiPassword": "WLAN-Passwort eingeben", "scanNetworks": "Netzwerke scannen", "noInternetConnection": "<PERSON>ine <PERSON>bindung", "connectionTestFailed": "Verbindungstest fehlgeschlagen", "wifiScanFailed": "WLAN-<PERSON>an fehlgeschlagen", "nameRequired": "Gerätename ist erforderlich", "wifiSSIDRequired": "WLAN-Name ist erford<PERSON>lich", "wifiConfigSaved": "WLAN-Konfiguration gespeichert", "saveError": "Fe<PERSON> beim <PERSON>rn", "connectionError": "Verbindungsfehler", "connectToWifi": "Mit WLAN verbinden", "deviceId": "Device ID"}}}