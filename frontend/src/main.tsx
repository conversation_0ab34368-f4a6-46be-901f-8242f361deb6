import { QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import 'helpers/i18n/react'
import 'styles/index.css'
import { RouterProvider } from 'react-router-dom'

import queryClient from 'helpers/reactQuery'
import { logVersion } from 'helpers/version'
import { DialogContainer } from 'hooks/Dialog/useEditDialog'
import { NotificationContainer } from 'hooks/useSimpleNotification'

import router from 'routes/router'

logVersion()

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
      <DialogContainer />
    </QueryClientProvider>
    <NotificationContainer />
  </StrictMode>
)
