import type {
  Brewing,
  BrewingFermentable,
  BrewingHop,
  BrewingMashStep,
  BrewingYeast,
  Recipe,
} from '@prisma/client'

import { useGet } from 'helpers/http'

export type BrewingResponse = Brewing & {
  recipe: Recipe
  mashSteps: BrewingMashStep[]
  fermentables: BrewingFermentable[]
  hops: BrewingHop[]
  yeasts: BrewingYeast[]
}

export const useGetBrewings = () => useGet<BrewingResponse[]>('brewing')

export const useGetBrewing = (brewingId: string) => useGet<BrewingResponse>(`brewing/${brewingId}`)
