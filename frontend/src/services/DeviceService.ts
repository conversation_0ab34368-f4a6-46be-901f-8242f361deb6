import { DeviceModel } from '@backendZod/device'
import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@prisma/client'
import { useMutation } from '@tanstack/react-query'
import { z } from 'zod'

import { post, useGet } from 'helpers/http'
import queryClient from 'helpers/reactQuery'

export const useGetDevices = () => useGet<Device[]>('device')

export const useGetDevicesByType = (type: (typeof DeviceType)[keyof typeof DeviceType]) =>
  useGet<Device[]>(`device?type=${type}`)

export const useGetDevice = (deviceId: string) =>
  useGet<(Device & { deviceConnected?: Device })[]>(`device/${deviceId}`)

export const DeviceOperator = {
  TUYA: 'TUYA',
  TASMOTA: 'TASMOTA',
  SMARTLIFE: 'SMARTLIFE',
  BREWTOOL: 'BREWTOOL',
  CUSTOM: 'CUSTOM',
}

export const DeviceType = <const>{
  TEMPERATURE: 'TEMPERATURE',
  SWITCH: 'SWITCH',
  ISPINDEL: 'ISPINDEL',
  BREWTOOL: 'BREWTOOL',
  OTHER: 'OTHER',
}

const Model = DeviceModel.omit({
  id: true,
  operator: true,
  mqttClientId: true,
  mqttPassword: true,
  mqttUsername: true,
  createdAt: true,
  updatedAt: true,
  type: true,
}).merge(
  z.object({
    type: z.enum(Object.values(DeviceType) as [string, ...string[]]),
    operator: z.enum(Object.values(DeviceOperator) as [string, ...string[]]),
  })
)

type CompleteDevice = z.infer<typeof Model>

export const registerDevice = async (device: CompleteDevice) => {
  const response = await post<Device>('device', device)

  return (await response.json()) as Device
}

export const createApiKey = async (device: Device) => {
  const responseApiKey = await post<ApiKey>(`device/${device.id}/api-key`, {})

  return (await responseApiKey.json()) as ApiKey
}

export const connectDevice = async (deviceId: number, data: { deviceConnectedId: number }) => {
  await post(`device/${deviceId}/connect`, data)
}

export const useConnectDevice = () => {
  return useMutation({
    mutationFn: async (data: { deviceId: number; deviceConnectedId: number }) => {
      return await connectDevice(data.deviceId, { deviceConnectedId: data.deviceConnectedId })
    },
    onSuccess: (_data, vars) => {
      queryClient.invalidateQueries({ queryKey: ['device'] })
      queryClient.invalidateQueries({ queryKey: [`device/${vars.deviceId}`] })
    },
  })
}
