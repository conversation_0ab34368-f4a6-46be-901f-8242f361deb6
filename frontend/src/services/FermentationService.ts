import { TemperatureModel } from '@backendZod/temperature'
import type { StatisticValues, Temperature } from '@prisma/client'
import { z } from 'zod'

import { post, useGet } from 'helpers/http'

const model = TemperatureModel.omit({ id: true, createdAt: true, startDate: true, endDate: true })

export async function activateFermentation(data: z.infer<typeof model>) {
  return await post('temperature', data)
}

export function useActualTemperature() {
  const temperature = useGet<Temperature | null>('temperature/actual')
  return temperature
}

export function useActualDeviceTemperature() {
  return useGet<StatisticValues>('statistic/actual')
}

export function useTemperatureStatistic(deviceId: number) {
  const temperature = useGet<StatisticValues[]>(`brewtool/${deviceId}/temperature`)
  return temperature
}
