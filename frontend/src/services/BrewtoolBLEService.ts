import type {
  BluetoothRemoteGATTService,
  BluetoothRemoteGATTCharacteristic,
} from '@types/web-bluetooth'

export interface BrewtoolDevice {
  id: string
  name: string | null
  device: BluetoothDevice
}

export interface BrewtoolConfig {
  name: string
  wifiSSID: string
  wifiPassword: string
  enableWifi: boolean
  autoConnect: boolean
  temperatureUnit: 'celsius' | 'fahrenheit'
  updateInterval: number
}

export interface ConnectionStatus {
  isConnected: boolean
  hasInternet: boolean
  signalStrength: number
  ipAddress: string | null
  firmwareVersion: string | null
}

class BrewtoolBLEService {
  private static instance: BrewtoolBLEService
  private connectedDevice: BluetoothDevice | null = null
  private service: BluetoothRemoteGATTService | null = null

  // Brewtool specific UUIDs
  private readonly BREWTOOL_SERVICE_UUID = '91bad492-b950-4226-aa2b-4ede9fa42f59'
  private readonly BREWTOOL_WIFI_CHAR_UUID = 'cba1d466-344c-4be3-ab3f-189f80dd7518'
  private readonly BREWTOOL_WIFI_SCAN_CHAR_UUID = '594228c8-9eb8-497c-8886-960d1c9cdb67'
  private readonly BREWTOOL_DEVICE_ID_CHAR_UUID = '678fb259-4af8-4e47-a249-08fbbdbe5845'
  private readonly BREWTOOL_API_KEY_CHAR_UUID = 'c68a2e95-b47a-4880-8b90-ed5f92f8aee0'

  private constructor() {}

  static getInstance(): BrewtoolBLEService {
    if (!BrewtoolBLEService.instance) {
      BrewtoolBLEService.instance = new BrewtoolBLEService()
    }
    return BrewtoolBLEService.instance
  }

  // Check if Web Bluetooth is supported
  isSupported(): boolean {
    return typeof navigator !== 'undefined' && 'bluetooth' in navigator
  }

  // Check if Bluetooth is available
  async isAvailable(): Promise<boolean> {
    if (!this.isSupported()) return false
    try {
      return await navigator.bluetooth.getAvailability()
    } catch {
      return false
    }
  }

  // Request device selection
  async requestDevice(): Promise<BrewtoolDevice> {
    if (!this.isSupported()) {
      throw new Error('Web Bluetooth is not supported in this browser')
    }

    const available = await this.isAvailable()
    if (!available) {
      throw new Error('Bluetooth is not available')
    }

    try {
      const device = await navigator.bluetooth.requestDevice({
        filters: [
          {
            services: [this.BREWTOOL_SERVICE_UUID],
          },
        ],
      })

      return {
        id: device.id,
        name: device.name,
        device,
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        throw new Error('No Brewtool device selected')
      }
      throw new Error('Failed to request Brewtool device')
    }
  }

  // Connect to device
  async connectToDevice(brewtoolDevice: BrewtoolDevice): Promise<void> {
    try {
      const server = await brewtoolDevice.device.gatt?.connect()
      if (!server?.connected) {
        throw new Error('Failed to connect to device')
      }

      this.connectedDevice = brewtoolDevice.device
      this.service = await server.getPrimaryService(this.BREWTOOL_SERVICE_UUID)
    } catch (error) {
      throw new Error('Failed to connect to Brewtool device')
    }
  }

  // Disconnect from device
  async disconnect(): Promise<void> {
    if (this.connectedDevice?.gatt?.connected) {
      this.connectedDevice.gatt.disconnect()
    }
    this.connectedDevice = null
    this.service = null
  }

  // Check if connected
  isConnected(): boolean {
    return this.connectedDevice?.gatt?.connected ?? false
  }

  // Helper methods for reading/writing data
  private decoder = new TextDecoder('utf-8')
  private encoder = new TextEncoder()

  private async readCharacteristic(characteristicUUID: string): Promise<string> {
    if (!this.service) {
      throw new Error('Not connected to device')
    }

    try {
      const characteristic = await this.service.getCharacteristic(characteristicUUID)
      const dataView = await characteristic.readValue()
      const value = this.decoder.decode(dataView)
      return value.replaceAll('\0', '')
    } catch (error) {
      throw new Error(`Failed to read characteristic: ${error}`)
    }
  }

  private async writeCharacteristic(characteristicUUID: string, data: string): Promise<void> {
    if (!this.service) {
      throw new Error('Not connected to device')
    }

    try {
      const characteristic = await this.service.getCharacteristic(characteristicUUID)
      await characteristic.writeValue(this.encoder.encode(data))
    } catch (error) {
      throw new Error(`Failed to write characteristic: ${error}`)
    }
  }

  private async readJsonData<T>(characteristicUUID: string): Promise<T> {
    const value = await this.readCharacteristic(characteristicUUID)
    return JSON.parse(value) as T
  }

  // Brewtool-specific methods
  async readWifiStatus(): Promise<{ ssid: string; status: boolean }> {
    return await this.readJsonData<{ ssid: string; status: boolean }>(this.BREWTOOL_WIFI_CHAR_UUID)
  }

  async writeWifiConfig(ssid: string, password: string): Promise<void> {
    const config = JSON.stringify({ ssid, password })
    await this.writeCharacteristic(this.BREWTOOL_WIFI_CHAR_UUID, config)
  }

  async scanWifiNetworks(): Promise<string[]> {
    return await this.readJsonData<string[]>(this.BREWTOOL_WIFI_SCAN_CHAR_UUID)
  }

  async readDeviceId(): Promise<string> {
    return await this.readCharacteristic(this.BREWTOOL_DEVICE_ID_CHAR_UUID)
  }

  async writeApiKey(apiKey: string): Promise<void> {
    await this.writeCharacteristic(this.BREWTOOL_API_KEY_CHAR_UUID, apiKey)
  }

  // Test internet connection
  async testInternetConnection(): Promise<ConnectionStatus> {
    const wifiStatus = await this.readWifiStatus()

    let deviceId = 'Unknown'
    try {
      deviceId = await this.readDeviceId()
    } catch (error) {
      console.log('Could not read device ID:', error)
    }

    return {
      isConnected: true,
      hasInternet: wifiStatus.status,
      signalStrength: -50, // Web Bluetooth doesn't provide RSSI
      ipAddress: wifiStatus.status ? 'Connected' : null,
      firmwareVersion: deviceId,
    }
  }
}

export default BrewtoolBLEService.getInstance()
