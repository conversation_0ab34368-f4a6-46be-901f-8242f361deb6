import { Notification } from '@prisma/client'
import { useMutation } from '@tanstack/react-query'
import useAuthenticationStore from 'stores/AuthenticationStore'

import { put, useGet } from 'helpers/http'
import queryClient from 'helpers/reactQuery'

export const useGetNotifications = () =>
  useGet<Notification[]>(`user/${useAuthenticationStore.getState().user?.id}/notification`)

export const markNotificationReaded = () => {
  return useMutation({
    mutationFn: async (data: { notificationId: number }) => {
      return await put(
        `user/${useAuthenticationStore.getState().user?.id}/notification/${data.notificationId}`,
        { read: true }
      )
    },
    onSuccess: () => {
      queryClient.refetchQueries({
        queryKey: [`user/${useAuthenticationStore.getState().user?.id}/notification`],
      })
    },
  })
}
