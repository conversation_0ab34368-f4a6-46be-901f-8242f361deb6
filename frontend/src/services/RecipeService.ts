import type {
  Recipe,
  RecipeFermentable,
  RecipeHop,
  RecipeMash,
  RecipeMashStep,
  RecipeYeast,
} from '@prisma/client'

import { useGet } from 'helpers/http'

export type RecipeRequestResponse = Recipe & {
  hops: RecipeHop[]
  yeasts: RecipeYeast[]
  mashs: (RecipeMash & { steps: RecipeMashStep[] })[]
  fermentables: RecipeFermentable[]
}

export function useGetRecipe(recipeId: string) {
  const recipeRequest = useGet<RecipeRequestResponse>(`recipe/${recipeId}`)
  return recipeRequest
}

export function useGetRecipes(query: string) {
  const recipesRequest = useGet<Recipe[]>(`recipe?${query}`)
  return recipesRequest
}
