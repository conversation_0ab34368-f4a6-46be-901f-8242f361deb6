type Ranges = '5days' | '2days' | '24hours' | '12hours'
const [range, setRange] = useState<Ranges>('5days')
const [temperatureData, setTemperatureData] = useState<StatisticValues[]>([])
const [timeRange, setTimeRange] = useState<string[]>([])

const tabs: {
  name: string
  range: Ranges
}[] = [
  { name: t('5 days'), range: '5days' },
  { name: t('2 days'), range: '2days' },
  { name: t('24 hours'), range: '24hours' },
  { name: t('12 hours'), range: '12hours' },
]

const statisticData = useGet<StatisticValues[]>('statistic')

const formatDate = (date: Date) => `${date.getDay()}.${date.getMonth()}`

const createRangeForDays = (days: number) => {
  const ranges = []
  const now = new Date()
  for (let i = 0; i < days; i++) {
    const date = new Date()
    date.setDate(now.getDate() - i)
    ranges.push(formatDate(date))
  }
  return ranges
}

useEffect(() => {
  switch (range) {
    case '5days':
      setTimeRange(createRangeForDays(5))
      break
    case '2days':
      setTimeRange(createRangeForDays(2))
      break
    case '24hours':
    case '12hours':
    default:
      setTimeRange([])
      break
  }
}, [range])

const groupDataPerDay = () => {
  const groupedData: Record<string, StatisticValues[]> = {}
  timeRange.forEach(time => {
    groupedData[time] = []
  })

  statisticData.data?.forEach(stat => {
    const date = new Date(stat.createdAt)
    const time = formatDate(date)
    if (groupedData[time]) {
      groupedData[time].push(stat)
    }
  })

  const data = Object.keys(groupedData).map(key => {
    const value = groupedData[key]
    const summary = value.reduce((acc, curr) => acc + curr.temperature, 0)
    const temperature = summary ? (summary / value.length).toFixed(2) : 0
    return { temperature, createdAt: key }
  })

  return data
}

useEffect(() => {
  if (statisticData.data && timeRange) {
    switch (range) {
      case '5days':
      case '2days':
        setTemperatureData(groupDataPerDay())
        break
      case '24hours':
      case '12hours':
      default:
        setTemperatureData(statisticData.data)
        break
    }
  }
}, [statisticData.data, timeRange])
