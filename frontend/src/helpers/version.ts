import { name as packageName, version } from '@root/package.json'

export function getVersion() {
  return `${version} (${BUILD_TIMESTAMP})`
}

export function logVersion() {
  const version = getVersion()

  console.log(`${packageName}: ${version}`)

  if (version.includes('-LOCAL')) {
    console.warn('CAUTION! This is the *local* bundle. Do NOT use it in production!')
  } else if (version.includes('[DEV]')) {
    console.warn('CAUTION! This is the *dev* bundle. Do NOT use it in production!')
  } else if (version.includes('[STAGE]')) {
    console.warn('CAUTION! This is the *test* bundle. Do NOT use it in production!')
  }
}
