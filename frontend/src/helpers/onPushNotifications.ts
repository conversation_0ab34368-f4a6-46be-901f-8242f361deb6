import { post } from 'helpers/http'

export function hasPushNotificationSupport(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window
}

export async function createPushNotifications() {
  if (!hasPushNotificationSupport()) {
    // Service Worker isn't supported on this browser, disable or hide UI.
    // Push isn't supported on this browser, disable or hide UI.
    return
  }

  try {
    await askPermission()
    subscribeUserToPush()
  } catch (err) {
    console.error('Push Notifications failed', err)
  }
}

export function hasPushNotificationPermission(): boolean {
  return Notification.permission === 'granted'
}

async function askPermission() {
  return new Promise(function (resolve, reject) {
    const permissionResult = Notification.requestPermission(function (result) {
      resolve(result)
    })

    if (permissionResult) {
      permissionResult.then(resolve, reject)
    }
  }).then(function (permissionResult) {
    if (permissionResult !== 'granted') {
      throw new Error("We weren't granted permission.")
    }
  })
}

async function subscribeUserToPush() {
  try {
    const registration = await navigator.serviceWorker.register(
      import.meta.env.MODE === 'production' ? '/service-worker.js' : '/dev-sw.js?dev-sw',
      { type: import.meta.env.MODE === 'production' ? 'classic' : 'module' }
    )

    const subscribeOptions = {
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(
        import.meta.env.VITE_WEB_PUSH_PUBLIC_KEY as string
      ),
    }

    const pushSubscription = await registration.pushManager.subscribe(subscribeOptions)

    sendSubscriptionToBackEnd(pushSubscription)
    return pushSubscription
  } catch (err) {
    console.error('Unable to subscribe to push.', err)
  }
}

function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4)
  const base64 = (base64String + padding).replace(/\-/g, '+').replace(/_/g, '/')

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }
  return outputArray
}

async function sendSubscriptionToBackEnd(subscription: PushSubscription) {
  try {
    const response = await post('subscription', {
      endpoint: subscription.endpoint,
      keys: subscription.toJSON().keys,
      userAgent: navigator.userAgent,
    })

    const responseData = await response.json()
    if (!responseData || responseData.endpoint !== subscription.endpoint) {
      throw new Error('Bad response from server.')
    }
  } catch (e) {
    throw new Error('Bad status code from server.')
  }
}
