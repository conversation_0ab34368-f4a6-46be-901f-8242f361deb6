import { parseBeerXml } from 'helpers/xml'

describe('xml', () => {
  it('should parse xml', () => {
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
        <RECIPES>
          <RECIPE>
            <VERSION>1</VERSION>
            <NAME>Oat White IPA  Kopie</NAME>
            <TYPE>All Grain</TYPE>
            <BREWER>kleiner-brauhelfer-2</BREWER>
            <BATCH_SIZE>23.0</BATCH_SIZE>
            <BOIL_SIZE>26.9</BOIL_SIZE>
            <BOIL_TIME>70</BOIL_TIME>
            <EFFICIENCY>70.0</EFFICIENCY>
            <STYLE>
              <VERSION>1</VERSION>
              <NAME>Unknown</NAME>
              <CATEGORY>Unknown</CATEGORY>
              <CATEGORY_NUMBER>0</CATEGORY_NUMBER>
              <STYLE_LETTER></STYLE_LETTER>
              <STYLE_GUIDE></STYLE_GUIDE>
              <TYPE></TYPE>
              <OG_MIN>1.0590</OG_MIN>
              <OG_MAX>1.0590</OG_MAX>
              <FG_MIN>0.8472</FG_MIN>
              <FG_MAX>0.8472</FG_MAX>
              <IBU_MIN>45</IBU_MIN>
              <IBU_MAX>45</IBU_MAX>
              <COLOR_MIN>3</COLOR_MIN>
              <COLOR_MAX>3</COLOR_MAX>
              <CARB_MIN>5.5</CARB_MIN>
              <CARB_MAX>5.5</CARB_MAX>
            </STYLE>
            <HOPS>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Simcoe</NAME>
                <ALPHA>13.0</ALPHA>
                <AMOUNT>0.0086</AMOUNT>
                <USE>First Wort</USE>
                <TIME>70</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Citra</NAME>
                <ALPHA>14.5</ALPHA>
                <AMOUNT>0.0078</AMOUNT>
                <USE>First Wort</USE>
                <TIME>70</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Simcoe</NAME>
                <ALPHA>13.0</ALPHA>
                <AMOUNT>0.0109</AMOUNT>
                <USE>Boil</USE>
                <TIME>10</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Hallertauer Cascade</NAME>
                <ALPHA>6.0</ALPHA>
                <AMOUNT>0.0127</AMOUNT>
                <USE>Boil</USE>
                <TIME>10</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Citra</NAME>
                <ALPHA>14.5</ALPHA>
                <AMOUNT>0.0107</AMOUNT>
                <USE>Boil</USE>
                <TIME>5</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Hallertauer Cascade</NAME>
                <ALPHA>6.0</ALPHA>
                <AMOUNT>0.0136</AMOUNT>
                <USE>Boil</USE>
                <TIME>5</TIME>
                <TYPE>Both</TYPE>
                <FORM>Pellet</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Simcoe</NAME>
                <ALPHA>13.0</ALPHA>
                <AMOUNT>0.0206</AMOUNT>
                <USE>Boil</USE>
                <TIME>0</TIME>
                <TYPE>Both</TYPE>
                <FORM>Leaf</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Hallertauer Cascade</NAME>
                <ALPHA>6.0</ALPHA>
                <AMOUNT>0.0306</AMOUNT>
                <USE>Boil</USE>
                <TIME>0</TIME>
                <TYPE>Both</TYPE>
                <FORM>Leaf</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Simcoe</NAME>
                <AMOUNT>0.033</AMOUNT>
                <USE>Dry Hop</USE>
                <TIME>0</TIME>
                <ALPHA>0.0</ALPHA>
                <TYPE>Both</TYPE>
                <FORM>Leaf</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Hallertauer Cascade</NAME>
                <AMOUNT>0.033</AMOUNT>
                <USE>Dry Hop</USE>
                <TIME>0</TIME>
                <ALPHA>0.0</ALPHA>
                <TYPE>Both</TYPE>
                <FORM>Leaf</FORM>
              </HOP>
              <HOP>
                <VERSION>1</VERSION>
                <NAME>Citra</NAME>
                <AMOUNT>0.033</AMOUNT>
                <USE>Dry Hop</USE>
                <TIME>0</TIME>
                <ALPHA>0.0</ALPHA>
                <TYPE>Both</TYPE>
                <FORM>Leaf</FORM>
              </HOP>
            </HOPS>
            <FERMENTABLES>
              <FERMENTABLE>
                <VERSION>1</VERSION>
                <NAME>Weizenmalz Hell</NAME>
                <TYPE>Grain</TYPE>
                <AMOUNT>2.07</AMOUNT>
                <YIELD>77</YIELD>
                <COLOR>1.34859</COLOR>
              </FERMENTABLE>
              <FERMENTABLE>
                <VERSION>1</VERSION>
                <NAME>Pilsener Malz </NAME>
                <TYPE>Grain</TYPE>
                <AMOUNT>2.07</AMOUNT>
                <YIELD>77</YIELD>
                <COLOR>1.87362</COLOR>
              </FERMENTABLE>
              <FERMENTABLE>
                <VERSION>1</VERSION>
                <NAME>Hafermmalz</NAME>
                <TYPE>Grain</TYPE>
                <AMOUNT>0.91</AMOUNT>
                <YIELD>77</YIELD>
                <COLOR>2.06113</COLOR>
              </FERMENTABLE>
            </FERMENTABLES>
            <YEASTS>
              <YEAST>
                <VERSION>1</VERSION>
                <NAME>
        Danstar BRY-97 American West Coast -</NAME>
                <TYPE>Lager</TYPE>
                <FORM>Dry</FORM>
                <AMOUNT>0</AMOUNT>
              </YEAST>
            </YEASTS>
            <MISCS/>
            <WATERS>
              <VERSION>1</VERSION>
              <NAME>Profil 1</NAME>
              <AMOUNT>31.8</AMOUNT>
              <CALCIUM>0.0</CALCIUM>
              <BICARBONATE>0.0</BICARBONATE>
              <SULFATE>0.0</SULFATE>
              <CHLORIDE>0.0</CHLORIDE>
              <SODIUM>0.0</SODIUM>
              <MAGNESIUM>0.0</MAGNESIUM>
            </WATERS>
            <MASH>
              <VERSION>1</VERSION>
              <NAME>Temperatur</NAME>
              <GRAIN_TEMP>18</GRAIN_TEMP>
              <MASH_STEPS/>
            </MASH>
          </RECIPE>
        </RECIPES>
    `
    const result = parseBeerXml(xml)
    expect(result).toEqual({
      RECIPES: [
        {
          VERSION: '1',
          NAME: 'Oat White IPA  Kopie',
          TYPE: 'All Grain',
          BREWER: 'kleiner-brauhelfer-2',
          BATCH_SIZE: '23.0',
          BOIL_SIZE: '26.9',
          BOIL_TIME: '70',
          EFFICIENCY: '70.0',
          STYLE: {
            VERSION: '1',
            NAME: 'Unknown',
            CATEGORY: 'Unknown',
            CATEGORY_NUMBER: '0',
            STYLE_LETTER: null,
            STYLE_GUIDE: null,
            TYPE: null,
            OG_MIN: '1.0590',
            OG_MAX: '1.0590',
            FG_MIN: '0.8472',
            FG_MAX: '0.8472',
            IBU_MIN: '45',
            IBU_MAX: '45',
            COLOR_MIN: '3',
            COLOR_MAX: '3',
            CARB_MIN: '5.5',
            CARB_MAX: '5.5',
          },
          HOPS: [
            {
              VERSION: '1',
              NAME: 'Simcoe',
              ALPHA: '13.0',
              AMOUNT: '0.0086',
              USE: 'First Wort',
              TIME: '70',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Citra',
              ALPHA: '14.5',
              AMOUNT: '0.0078',
              USE: 'First Wort',
              TIME: '70',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Simcoe',
              ALPHA: '13.0',
              AMOUNT: '0.0109',
              USE: 'Boil',
              TIME: '10',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Hallertauer Cascade',
              ALPHA: '6.0',
              AMOUNT: '0.0127',
              USE: 'Boil',
              TIME: '10',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Citra',
              ALPHA: '14.5',
              AMOUNT: '0.0107',
              USE: 'Boil',
              TIME: '5',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Hallertauer Cascade',
              ALPHA: '6.0',
              AMOUNT: '0.0136',
              USE: 'Boil',
              TIME: '5',
              TYPE: 'Both',
              FORM: 'Pellet',
            },
            {
              VERSION: '1',
              NAME: 'Simcoe',
              ALPHA: '13.0',
              AMOUNT: '0.0206',
              USE: 'Boil',
              TIME: '0',
              TYPE: 'Both',
              FORM: 'Leaf',
            },
            {
              VERSION: '1',
              NAME: 'Hallertauer Cascade',
              ALPHA: '6.0',
              AMOUNT: '0.0306',
              USE: 'Boil',
              TIME: '0',
              TYPE: 'Both',
              FORM: 'Leaf',
            },
            {
              VERSION: '1',
              NAME: 'Simcoe',
              AMOUNT: '0.033',
              USE: 'Dry Hop',
              TIME: '0',
              ALPHA: '0.0',
              TYPE: 'Both',
              FORM: 'Leaf',
            },
            {
              VERSION: '1',
              NAME: 'Hallertauer Cascade',
              AMOUNT: '0.033',
              USE: 'Dry Hop',
              TIME: '0',
              ALPHA: '0.0',
              TYPE: 'Both',
              FORM: 'Leaf',
            },
            {
              VERSION: '1',
              NAME: 'Citra',
              AMOUNT: '0.033',
              USE: 'Dry Hop',
              TIME: '0',
              ALPHA: '0.0',
              TYPE: 'Both',
              FORM: 'Leaf',
            },
          ],
          FERMENTABLES: [
            {
              VERSION: '1',
              NAME: 'Weizenmalz Hell',
              TYPE: 'Grain',
              AMOUNT: '2.07',
              YIELD: '77',
              COLOR: '1.34859',
            },
            {
              VERSION: '1',
              NAME: 'Pilsener Malz ',
              TYPE: 'Grain',
              AMOUNT: '2.07',
              YIELD: '77',
              COLOR: '1.87362',
            },
            {
              VERSION: '1',
              NAME: 'Hafermmalz',
              TYPE: 'Grain',
              AMOUNT: '0.91',
              YIELD: '77',
              COLOR: '2.06113',
            },
          ],
          YEASTS: [
            {
              VERSION: '1',
              NAME: 'Danstar BRY-97 American West Coast -',
              TYPE: 'Lager',
              FORM: 'Dry',
              AMOUNT: '0',
            },
          ],
          MISCS: [],
          WATERS: {
            VERSION: '1',
            NAME: 'Profil 1',
            AMOUNT: '31.8',
            CALCIUM: '0.0',
            BICARBONATE: '0.0',
            SULFATE: '0.0',
            CHLORIDE: '0.0',
            SODIUM: '0.0',
            MAGNESIUM: '0.0',
          },
          MASH: [
            {
              VERSION: '1',
              NAME: 'Temperatur',
              GRAIN_TEMP: '18',
              MASH_STEPS: [],
            },
          ],
        },
      ],
    })
  })
})
