import type { RecipeHop, RecipeMashStep, RecipeYeast } from '@prisma/client'

import { parseBeerXml } from 'helpers/xml'
import { BeerXml } from 'types/beerXml'

export const generateRecipeFromBeerXml = async (
  beerXml: string | BeerXml,
  isForPrisma: boolean,
  xml?: string
) => {
  if (typeof beerXml === 'string') {
    beerXml = parseBeerXml(beerXml)
  }

  if (beerXml.RECIPES.length === 0) {
    throw new Error('No recipes found')
  } else if (beerXml.RECIPES.length > 1) {
    throw new Error('Multiple recipes found')
  }

  const recipeData = beerXml.RECIPES[0]

  const recipe = {
    name: recipeData.NAME,
    brewer: recipeData.BREWER,
    batchSize: Number(recipeData.BATCH_SIZE),
    boilSize: Number(recipeData.BOIL_SIZE),
    boilTime: Number(recipeData.BOIL_TIME),
    efficiency: Number(recipeData.EFFICIENCY) ?? null,
    ibu: recipeData.IBU,
    age: Number(recipeData.AGE) ?? null,
    ageTemperature: Number(recipeData.AGE_TEMP) ?? null,
    preGravity: Number(recipeData.OG) ?? null,
    finishedGravity: Number(recipeData.FG) ?? null,
    file: isForPrisma && xml ? xml : null,
    mashs: mapItems(
      [
        {
          name: recipeData.MASH?.NAME ?? '',
          steps: mapItems(
            recipeData.MASH?.MASH_STEPS?.map(s => ({
              name: s.NAME,
              temperature: Number(s.STEP_TEMP),
              time: Number(s.STEP_TIME),
              infuseAmount: Number(s.INFUSE_AMOUNT ?? 0),
              type: s.TYPE,
            })) as RecipeMashStep[],
            isForPrisma
          ),
        },
      ],
      isForPrisma
    ),
    hops: mapItems(
      recipeData.HOPS.map(h => ({
        name: h.NAME,
        amount: Number(h.AMOUNT),
        alpha: Number(h.ALPHA),
        form: h.FORM,
        time: Number(h.TIME),
        type: h.TYPE,
        use: h.USE,
      })) as RecipeHop[],
      isForPrisma
    ),
    yeasts: mapItems(
      recipeData.YEASTS.map(y => ({
        name: y.NAME,
        amount: Number(y.AMOUNT),
      })) as RecipeYeast[],
      isForPrisma
    ),
    fermentables: mapItems(
      recipeData.FERMENTABLES.map(f => ({
        name: f.NAME,
        amount: Number(f.AMOUNT),
        yield: f.YIELD ? Number(f.YIELD) : null,
        color: Number(f.COLOR),
        type: f.TYPE,
        addAfterBoil: f.ADD_AFTER_BOIL?.toLowerCase() == 'true',
        notes: f.NOTES,
      })),
      isForPrisma
    ),
  }

  return recipe
}

const mapItems = (data: Array<unknown>, isForPrisma: boolean) => {
  return isForPrisma
    ? {
        create: data,
      }
    : data
}
