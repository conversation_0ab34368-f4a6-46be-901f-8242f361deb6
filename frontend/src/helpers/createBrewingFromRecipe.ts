import type {
  Brewing,
  BrewingFermentable,
  BrewingHop,
  BrewingMashStep,
  BrewingYeast,
} from '@prisma/client'

import { omit } from 'helpers/object'
import { RecipeRequestResponse } from 'services/RecipeService'

type BrewingMashStepModel = Omit<BrewingMashStep, 'id' | 'createdAt' | 'brewingId'>
type BrewingHopModel = Omit<BrewingHop, 'id' | 'createdAt' | 'brewingId'>
type BrewingYeastModel = Omit<BrewingYeast, 'id' | 'createdAt' | 'brewingId'>
type BrewingFermentableModel = Omit<BrewingFermentable, 'id' | 'createdAt' | 'brewingId'>

type BrewingModel = Omit<Brewing, 'id' | 'createdAt' | 'status'> & {
  mashSteps: BrewingMashStepModel[]
  hops: BrewingHopModel[]
  yeasts: BrewingYeastModel[]
  fermentables: BrewingFermentableModel[]
}

export const createBrewingFromRecipe = (
  recipe: RecipeRequestResponse,
  fermentationTemperature: number
): BrewingModel => {
  return {
    startTime: null,
    endTime: null,
    recipeId: recipe.id,
    mashType: recipe.mashs[0].name,
    fermentationTemperature: fermentationTemperature, // TODO: get from recipe
    lauteringPauseTime: null,
    boilTime: recipe.boilTime,
    boilStartTime: null,
    mashSteps: recipe.mashs[0].steps.map(
      ({ name, infuseAmount, temperature, time, type }, index) => ({
        name,
        infuseAmount,
        temperature,
        time,
        type,
        order: index,
        startTime: null,
        heatingStart: null,
        status: 'WAITING',
      })
    ),
    hops: recipe.hops.map(
      hop => ({ ...omit(hop, 'id'), type: hop.type ?? 'PELLET', added: false }) as BrewingHopModel
    ),
    yeasts: recipe.yeasts.map(yeast => ({
      ...(omit(yeast, 'recipeId', 'id') as BrewingYeastModel),
      fermentationTemperature,
    })),
    fermentables: recipe.fermentables.map(fermentable => ({
      ...omit(fermentable, 'recipeId', 'id'),
      added: false,
    })),
  }
}
