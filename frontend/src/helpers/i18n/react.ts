import i18n, { use } from 'i18next'
import { initReactI18next } from 'react-i18next'
import { z } from 'zod'
import { zodI18nMap } from 'zod-i18n-map'

import { availableLanguages, availableResources, fallBackLanguage } from 'helpers/i18n/constants'

const getLanguage = () => {
  // check device language
  for (const locale of window.navigator.languages) {
    const language = locale.slice(0, 2)
    if (availableLanguages.includes(language)) {
      return language
    }
  }

  return fallBackLanguage
}

const language = getLanguage()

use(initReactI18next).init({
  lng: language,
  fallbackLng: fallBackLanguage,
  resources: availableResources,
  supportedLngs: availableLanguages,
  interpolation: {
    escapeValue: false,
  },
  returnNull: false,
})

if (navigator.serviceWorker.controller) {
  navigator.serviceWorker.controller.postMessage({
    type: 'MESSAGE_LANGUAGE',
    language,
  })
}

z.setErrorMap(zodI18nMap)

export default i18n
