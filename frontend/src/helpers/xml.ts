import { BeerXml } from 'types/beerXml'

const beerXmlArray = ['RECIPES', 'HOPS', 'FERMENTABLES', 'MISCS', 'YEASTS', 'MASH_STEPS', 'MASHS']

export function parseBeerXml(xml: string) {
  const dom = new DOMParser().parseFromString(xml.replaceAll(/[\n\r]+ */g, ''), 'application/xml')

  const response: Record<string, unknown> = {}
  for (const node of dom.childNodes) {
    if (beerXmlArray.includes(node.nodeName)) {
      response[node.nodeName] = []

      for (const childNode of node.childNodes) {
        response[node.nodeName].push(parseXmlNode(childNode))
      }
      continue
    }

    throw new Error('BeerXML: Unknown node: ' + node.nodeName)
  }

  return response as BeerXml
}

function parseXmlNode(node: ChildNode) {
  const response: Record<string, unknown> = {}

  if (beerXmlArray.includes(node.nodeName)) {
    response[node.nodeName] = []
  }

  for (const childNode of node.childNodes) {
    // nur ein text child, heißt => <NAME>Pilsner</NAME>
    if (childNode.childNodes.length === 1 && childNode.childNodes[0].nodeName === '#text') {
      response[childNode.nodeName] = childNode.childNodes[0].nodeValue
      continue
    }

    // array von objekten, heißt => <HOPS><HOP><NAME>Magnum</NAME></HOP>....</HOPS>
    if (beerXmlArray.includes(childNode.nodeName)) {
      response[childNode.nodeName] = []

      for (const childNode2 of childNode.childNodes) {
        response[childNode.nodeName].push(parseXmlNode(childNode2))
      }
      continue
    }

    // objekt leer, heißt => <STYLE></STYLE>
    if (childNode.childNodes.length === 0) {
      response[childNode.nodeName] = childNode.nodeValue
      continue
    }

    response[childNode.nodeName] = parseXmlNode(childNode)
  }

  return response
}
