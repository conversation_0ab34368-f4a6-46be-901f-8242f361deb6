import { useQuery } from '@tanstack/react-query'
import { API_URL } from 'constants'
import { useTranslation } from 'react-i18next'
import useAuthenticationStore from 'stores/AuthenticationStore'

import useSimpleNotification from 'hooks/useSimpleNotification'

export class RequestError extends Error {
  constructor(
    public response: Response,
    public request: {
      method: string
      url: string
      data?: unknown
    }
  ) {
    super('Network response was not ok')

    console.error(`RequestError with status ${this.response.status}`)
  }
}

export function useGet<T>(route: string, key: string[] = []) {
  return useQuery<T>({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: [route].concat(key),
    queryFn: async () => {
      return (await get(route)) as T
    },
  })
}

export async function send(method: 'GET' | 'DELETE', route: string) {
  const res = await fetch(API_URL + '/api/' + route, {
    method,
    headers: useAuthenticationStore.getState().isAuthenticated
      ? { Authentication: `Bearer ${useAuthenticationStore.getState().token}` }
      : undefined,
  })

  if (res.status === 404) {
    return null
  }

  if (res.status === 401) {
    useAuthenticationStore.getState().logout()
    return null
  }

  if (!res.ok) {
    console.error(`Error handling for GET ${route}`, res)
    throw new RequestError(res, {
      method,
      url: route,
    })
  }

  return res
}

export async function get<T>(route: string) {
  const res = await send('GET', route)

  return ((await res?.json()) as T) ?? null
}

export async function del(route: string) {
  return await send('DELETE', route)
}

export async function sendData<T>(
  method: 'POST' | 'DELETE' | 'PUT',
  route: string,
  data?: Record<string, unknown> | object | FormData
) {
  const header = new Headers()

  const request: RequestInit = {
    method,
  }

  if (useAuthenticationStore.getState().isAuthenticated) {
    header.set('Authentication', `Bearer ${useAuthenticationStore.getState().token}`)
  }

  if (data instanceof FormData) {
    request.body = data
  } else {
    header.set('Content-Type', 'application/json')
    request.body = JSON.stringify(data)
  }

  const res = await fetch(API_URL + '/api/' + route, { ...request, headers: header })

  if (res.status === 200) {
    return res as Response & { json: () => Promise<T> }
  } else {
    console.error(`Error handling for POST ${route}`, res)
    throw new RequestError(res, {
      method,
      url: route,
      data,
    })
  }
}

export async function post<T>(route: string, data?: Record<string, unknown> | object) {
  return sendData<T>('POST', route, data)
}

export async function put(route: string, data: Record<string, unknown> | object) {
  return sendData('PUT', route, data)
}

export function useHttpNotifications() {
  const { createError, createSuccess } = useSimpleNotification()
  const { t } = useTranslation()

  const resolveResponse = async (promise: Promise<unknown> | (() => Promise<unknown>)) => {
    try {
      const res = typeof promise === 'function' ? await promise() : await promise
      createSuccess(t('notification.success.saved'))
      return res
    } catch (error) {
      createError(t('notification.error.general'))
    }
  }

  return resolveResponse
}
