import { ArchiveBoxIcon } from '@heroicons/react/20/solid'

import YeastIcon from 'assets/icons8-chemie-50.png'
import HopIcon from 'assets/icons8-hopfen-50.png'
import MaltIcon from 'assets/icons8-weizen-50.png'

import i18n from 'helpers/i18n/react'

export type InventoryType = 'malt' | 'hop' | 'yeast' | 'other'

export type InventoryTypeDefinition = {
  id: string
  name: string
  icon: JSX.Element
  defaultUnit: string
  extraFields?: Field[]
}

export type Field = { name: string; label: string; type: 'number' | 'text' }

const types: InventoryTypeDefinition[] = [
  {
    id: 'malt',
    name: i18n.t('inventory.malt.title'),
    icon: <img src={MaltIcon} alt={i18n.t('inventory.malt.title')} />,
    defaultUnit: i18n.t('inventory.malt.defaultUnit'),
  },
  {
    id: 'hop',
    name: i18n.t('hop.title'),
    icon: <img src={HopIcon} alt={i18n.t('hop.title')} />,
    extraFields: [
      {
        name: 'alpha',
        label: i18n.t('hop.alpha'),
        type: 'number',
      },
    ],
    defaultUnit: i18n.t('hop.defaultUnit'),
  },
  {
    id: 'yeast',
    name: i18n.t('yeast.title'),
    icon: <img src={YeastIcon} alt={i18n.t('yeast.title')} />,
    defaultUnit: i18n.t('yeast.defaultUnit'),
  },
  {
    id: 'other',
    name: i18n.t('inventory.other.title'),
    icon: <ArchiveBoxIcon className="h-6 w-6" />,
    defaultUnit: i18n.t('inventory.other.defaultUnit'),
  },
]

export default types

export function getTypeById(id: InventoryType): InventoryTypeDefinition {
  return types.find(t => t.id === id) as InventoryTypeDefinition
}
