import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import { useGet } from 'helpers/http'

export default function useStatus() {
  const { t } = useTranslation()

  const [isSuccess, setIsSuccess] = useState(true)

  const status = useGet<{ db: boolean; status: boolean }>('status')

  useEffect(() => {
    if (status.isFetching) {
      return
    }

    if (status.isError || !status.data) {
      setIsSuccess(false)
      return
    }

    setIsSuccess(!Object.entries(status.data).includes(false))
  }, [status])

  const StatusLabel = () => (
    <p className="mt-10 flex items-center justify-center text-sm text-gray-500">
      <div
        className={twMerge('mr-3 h-3 w-3 rounded-full ', isSuccess ? 'bg-green-700' : 'bg-red-600')}
      ></div>
      {isSuccess ? t('authentication.statusOk') : t('authentication.statusProblem')}
    </p>
  )

  return StatusLabel
}
