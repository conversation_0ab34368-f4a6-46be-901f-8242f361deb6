import { FunctionComponent, useState } from 'react'

import FormDialog from 'hooks/Dialog/FormDialog'

type FormDialogProps<T> = {
  id?: string | number
  title: string
  item?: T
  onSubmit: (data: unknown, intiItem?: T) => void
  FormElement: FunctionComponent<FormComponentProps<T>>
}

export type FormComponentProps<T> = Readonly<{
  data?: T
  onCancel: () => void
  onSubmit: (data: unknown) => void
}>

let _createDialog: null | (<T>(props: FormDialogProps<T>) => void) = null

export const DialogContainer = () => {
  const [dialogs, setDialogs] = useState<(FormDialogProps<unknown> & { id: number | string })[]>([])

  const closeModal = (id: string | number) => {
    setDialogs(dialogs => dialogs.filter(n => n.id !== id))
  }

  _createDialog = (props: FormDialogProps<unknown>) => {
    // eslint-disable-next-line react/prop-types
    const id = props.id ?? Math.random()

    // replace notification with same id
    if (dialogs.some(n => n.id === id)) {
      setDialogs(dialogs => dialogs.map(n => (n.id === id ? { ...n, ...props } : n)))
      return
    }

    setDialogs(dialogs => [...dialogs, { ...props, id, show: true }])

    return id
  }

  return (
    <div>
      {dialogs.map(item => (
        <FormDialog key={item.id} title={item.title} closeModal={() => closeModal(item.id)}>
          <item.FormElement
            data={item.item}
            onCancel={() => closeModal(item.id)}
            onSubmit={data => {
              item.onSubmit(data, item.item)
              closeModal(item.id)
            }}
          />
        </FormDialog>
      ))}
    </div>
  )
}

export default function useEditDialog<T>(
  title: string,
  FormElement: FunctionComponent<FormComponentProps<T>>,
  onSubmit: (data: unknown, intiItem?: T) => void
) {
  const open = (item?: T) => {
    if (!_createDialog) {
      console.error('DialogContainer not found')
    } else {
      _createDialog<T>({
        title,
        FormElement,
        onSubmit,
        item: item ?? undefined,
      })
    }
  }

  const openDialog = () => {
    open()
  }

  const openEditDialog = (data: T) => {
    open(data)
  }

  return {
    openDialog,
    openEditDialog,
  }
}
