import { useTranslation } from 'react-i18next'

import useEditDialog from 'hooks/Dialog/useEditDialog'

import DeleteForm from 'components/Common/DeleteForm'

export default function useDeleteDialog<T>(
  onSubmit: (data: unknown, intiItem?: T) => void,
  title?: string
) {
  const { t } = useTranslation()

  const { openEditDialog: openDeleteDialog } = useEditDialog<T>(
    title || t('common.delete'),
    DeleteForm,
    onSubmit
  )

  return openDeleteDialog
}
