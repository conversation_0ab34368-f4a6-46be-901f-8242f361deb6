import { useMemo } from 'react'

import { Brewtool } from 'services/BrewtoolService'

export type DeviceStatus = 'fermentation' | 'coldCrash' | 'standby' | 'offline'

export const COLD_CRUSH = 4

export default function useDeviceStatus(device: Brewtool) {
  const status = useMemo(() => {
    if (!device.statistic?.createdAt) {
      return 'offline'
    }

    if (Date.now() - new Date(device.statistic?.createdAt).getTime() > 5 * 60 * 1000) {
      return 'offline'
    }

    if (!device.config?.temperature) {
      return 'standby'
    }

    if (device.config?.temperature === COLD_CRUSH) {
      return 'coldCrash'
    }

    return 'fermentation'
  }, [device.statistic?.createdAt, device.config?.temperature])

  return status
}
