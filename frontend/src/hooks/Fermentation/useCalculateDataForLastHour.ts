import { useMemo } from 'react'

export default function useCalculateDataForLastHour<T extends string>(
  data: ({ createdAt: string } & Record<T, number>)[] | undefined,
  field: T
): {
  changeType: 'increase' | 'decrease'
  changesForLastHour: number | null
} {
  const changesForLastHour = useMemo(() => {
    if (!data) return null
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    let hourData = data.filter(entry => {
      const entryDate = new Date(entry.createdAt)
      return entryDate >= oneHourAgo && entryDate <= now
    })

    // Sort hourData by date ascending
    hourData = hourData.sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    )

    if (hourData.length == 0) {
      return null
    }

    const end = hourData[0][field]
    const start = hourData[hourData.length - 1][field]

    const value = start - end

    if (value === 0) {
      return null
    }

    return Number(value.toFixed(2))
  }, [data])

  const changeType = useMemo(() => {
    const isPlus = changesForLastHour && changesForLastHour > 0
    return isPlus ? 'increase' : 'decrease'
  }, [changesForLastHour])

  return {
    changeType,
    changesForLastHour,
  }
}
