import { API_URL } from 'constants'
import { useEffect, useRef } from 'react'
import useAuthenticationStore from 'stores/AuthenticationStore'

const WS_SERVER_URL = `${API_URL.replace('http', 'ws').replace('https', 'wss')}/ws`

export function useWebsocketMessage<T>(
  route: string,
  onMessage: (data: T | Record<string, unknown>) => void
) {
  const _ws = useRef<WebSocket | null>(null)

  useEffect(() => {
    const token = useAuthenticationStore.getState().token

    const ws = new WebSocket(`${WS_SERVER_URL}/${route}`, [token])

    ws.addEventListener('error', e => console.error('WebSocket error:', e))

    ws.addEventListener('message', message => {
      try {
        const data = JSON.parse(message.data)
        onMessage(data)
      } catch (error) {
        onMessage(message.data)
      }
    })

    ws.addEventListener('close', () => {
      console.log('WebSocket closed')
    })

    _ws.current = ws

    return () => {
      ws.close()
      _ws.current = null
    }
  }, [])

  const sendMessage = async (data: string | object) => {
    _ws.current!.send(JSON.stringify(data))
  }

  return sendMessage
}
