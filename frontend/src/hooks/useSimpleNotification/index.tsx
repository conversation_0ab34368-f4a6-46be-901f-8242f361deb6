import { useState } from 'react'

import SimpleNotification, {
  SimpleNotificationProps,
} from 'hooks/useSimpleNotification/SimpleNotification'

type NotificationProps = SimpleNotificationProps & {
  id?: string | number
  debounce?: number
}

let _createNotification: null | ((props: NotificationProps) => void) = null

export const NotificationContainer = () => {
  const [notifications, setNotifications] = useState<NotificationProps[]>([])

  _createNotification = (props: NotificationProps) => {
    // eslint-disable-next-line react/prop-types
    const debounce = props.debounce ?? 5_000
    // eslint-disable-next-line react/prop-types
    const id = props.id ?? Math.random()
    // replace notification with same id
    if (notifications.some(n => n.id === id)) {
      setNotifications(notifications =>
        notifications.map(n => (n.id === id ? { ...n, ...props } : n))
      )
      return
    }
    setNotifications(notifications => [...notifications, { ...props, id, show: true }])

    // if debounce is 0, the notification will not be removed automatically
    if (debounce === 0) return

    setTimeout(() => {
      setNotifications(notifications => notifications.filter(n => n.id !== id))
    }, debounce)
  }

  return (
    <div
      aria-live="assertive"
      className="pointer-events-none fixed inset-0 z-20 flex items-end px-4 py-6 sm:items-start sm:p-6"
    >
      {/* Global notification live region, render this permanently at the end of the document */}
      <div className="flex w-full flex-col items-center space-y-4 sm:items-end">
        {/* Notification panel, dynamically insert this into the live region when it needs to be displayed */}
        {notifications.map(item => (
          <SimpleNotification key={item.id} {...item} />
        ))}
      </div>
    </div>
  )
}

export const createError = (message: string) => {
  if (!_createNotification) {
    console.error('NotificationContainer not found')
  } else {
    _createNotification({ message, type: 'error' })
  }
}

export const createSuccess = (message: string) => {
  if (!_createNotification) {
    console.error('NotificationContainer not found')
  } else {
    _createNotification({ message, type: 'success' })
  }
}

export default function useSimpleNotification() {
  const createNotification = (props: NotificationProps) => {
    if (!_createNotification) {
      console.error('NotificationContainer not found')
    } else {
      _createNotification(props)
    }
  }

  const createError = (message: string) => {
    createNotification({ message, type: 'error' })
  }

  const createSuccess = (message: string) => {
    createNotification({ message, type: 'success' })
  }

  return {
    createNotification,
    createError,
    createSuccess,
  }
}
