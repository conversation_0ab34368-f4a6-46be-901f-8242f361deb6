import { Transition } from '@headlessui/react'
import { CheckCircleIcon, XMarkIcon } from '@heroicons/react/24/solid'
import { Fragment, ReactNode, useState } from 'react'
import { useTranslation } from 'react-i18next'

export type SimpleNotificationProps = {
  type: 'success' | 'error'
  message: string
  info?: string
  isDismissible?: boolean
}

export default function SimpleNotification(props: SimpleNotificationProps) {
  const [show, setShow] = useState(true)
  const { t } = useTranslation()

  return (
    <Transition
      show={show}
      as={Fragment as unknown as ReactNode as undefined}
      enter="transform ease-out duration-300 transition"
      enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
      enterTo="translate-y-0 opacity-100 sm:translate-x-0"
      leave="transition ease-in duration-100"
      leaveFrom="opacity-100"
      leaveTo="opacity-0"
    >
      <div className="pointer-events-auto z-20 w-full max-w-sm overflow-hidden rounded-lg bg-white shadow-lg ring-1 ring-black ring-opacity-5">
        <div className="p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {props.type === 'success' && (
                <CheckCircleIcon className="h-6 w-6 text-green-400" aria-hidden="true" />
              )}
              {props.type === 'error' && (
                <XMarkIcon className="h-6 w-6 text-red-400" aria-hidden="true" />
              )}
            </div>
            <div className="ml-3 w-0 flex-1 pt-0.5">
              <p className="text-sm font-medium text-stone-900">{props.message}</p>
              <p className="mt-1 text-sm text-stone-500">{props.info}</p>
            </div>
            {props.isDismissible !== false && (
              <div className="ml-4 flex flex-shrink-0">
                <button
                  type="button"
                  className="inline-flex rounded-md bg-white text-stone-400 hover:text-stone-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  onClick={() => {
                    setShow(false)
                  }}
                >
                  <span className="sr-only">{t('common.close')}</span>
                  <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Transition>
  )
}
