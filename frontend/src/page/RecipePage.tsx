import { Menu, Transition } from '@headlessui/react'
import { EllipsisVerticalIcon } from '@heroicons/react/20/solid'
import { ArrowUpTrayIcon, DocumentTextIcon } from '@heroicons/react/24/solid'
import { Fragment, ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import useEditDialog from 'hooks/Dialog/useEditDialog'

// import { ButtonWithIcon } from 'components/Common/Button'
import { useGetRecipes } from 'services/RecipeService'

import { EmptyListBox } from 'components/Common/EmptyListBox'
import PageHead from 'components/Common/Page/PageHead'
import PageWrapper from 'components/Common/Page/PageWrapper'
import BraurekaRecipe from 'components/Recipe/BraurekaRecipe'
import DeleteForm from 'components/Recipe/DeleteForm'
import UploadForm from 'components/Recipe/UploadForm'
import UploadPdfForm from 'components/Recipe/UploadPdfForm'

const statuses = {
  notCooked: 'text-green-700 bg-green-50 ring-green-600/20',
  cooked: 'text-gray-600 bg-gray-50 ring-gray-500/10',
  inProgress: 'text-yellow-800 bg-yellow-50 ring-yellow-600/20',
}

export default function RecipePage(): ReactElement {
  const { t } = useTranslation()

  const recipeRequest = useGetRecipes()

  const { openDialog } = useEditDialog<boolean>(t('recipe.beerXmlUpload'), UploadForm, async () => {
    recipeRequest.refetch()
  })

  const { openEditDialog: openDeleteDialog } = useEditDialog(
    t('recipe.delete'),
    DeleteForm,
    async () => {
      recipeRequest.refetch()
    }
  )

  const { openEditDialog: openPdfDialog } = useEditDialog<boolean>(
    t('recipe.uploadPdf'),
    UploadPdfForm,
    async () => {
      recipeRequest.refetch()
    }
  )

  return (
    <>
      <PageHead
        title={t('navigation.recipe')}
        buttons={[
          {
            style: 'secondary',
            children: (
              <>
                <DocumentTextIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                {t('recipe.uploadPdf')}
              </>
            ),
            onClick: openPdfDialog,
          },
          {
            children: (
              <>
                <ArrowUpTrayIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                {t('recipe.beerXmlUpload')}
              </>
            ),
            onClick: openDialog,
          },
        ]}
      />
      <PageWrapper>
        <div className="mt-5">
          {/** Empty State */}
          <EmptyListBox data={recipeRequest.data} onAdd={openDialog}>
            <>
              <ArrowUpTrayIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              {t('recipe.beerXmlUpload')}
            </>
          </EmptyListBox>
          {recipeRequest.data && (
            <ul role="list" className="divide-y divide-gray-100">
              {recipeRequest.data.map(item =>
                item.file === 'braureka' ? (
                  <BraurekaRecipe key={item.id} item={item} />
                ) : (
                  <li key={item.id} className="flex items-center justify-between gap-x-6 py-5">
                    <div className="min-w-0">
                      <div className="flex items-start gap-x-3">
                        <Link to={`/recipe/${item.id}/view`}>
                          <p className="text-sm font-semibold leading-6 text-gray-900">
                            {item.name}
                          </p>
                        </Link>
                        <p
                          className={twMerge(
                            statuses['notCooked'],
                            'mt-0.5 whitespace-nowrap rounded-md px-1.5 py-0.5 text-xs font-medium ring-1 ring-inset'
                          )}
                        >
                          {t(`recipe.${item?.log ?? 'notCooked'}`)}
                        </p>
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                        {/* <p className="whitespace-nowrap">
                        Due on <time dateTime={item.dueDateTime}>{item.dueDate}</time>
                      </p> */}
                        <p className="whitespace-nowrap">{t('recipe.createdByBeerXml')}</p>
                        <svg viewBox="0 0 2 2" className="h-0.5 w-0.5 fill-current">
                          <circle cx={1} cy={1} r={1} />
                        </svg>
                        <p className="truncate">
                          {t('recipe.createdAt', {
                            date: new Date(item.createdAt as string).toLocaleString(),
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-none items-center gap-x-4">
                      <Link
                        to={`/recipe/${item.id}/view`}
                        className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:block"
                      >
                        {t('recipe.viewRecipe')} <span className="sr-only">, {item.name}</span>
                      </Link>
                      {/* <ButtonWithIcon
                      onClick={() => openEditDialog(item)}
                      style="secondary"
                      className="flex-none"
                      size="sm"
                    >
                      <PencilIcon />
                      {t('common.edit')}
                    </ButtonWithIcon> */}
                      <Menu as="div" className="relative flex-none">
                        <Menu.Button className="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
                          <span className="sr-only">{t('common.openContextMenu')}</span>
                          <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
                        </Menu.Button>
                        <Transition
                          as={Fragment}
                          enter="transition ease-out duration-100"
                          enterFrom="transform opacity-0 scale-95"
                          enterTo="transform opacity-100 scale-100"
                          leave="transition ease-in duration-75"
                          leaveFrom="transform opacity-100 scale-100"
                          leaveTo="transform opacity-0 scale-95"
                        >
                          <Menu.Items className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                            <Menu.Item>
                              {({ active }) => (
                                <a
                                  href="#"
                                  className={twMerge(
                                    active ? 'bg-gray-50' : '',
                                    'block px-3 py-1 text-sm leading-6 text-gray-900'
                                  )}
                                >
                                  {t('common.edit')}
                                  <span className="sr-only">, {item.name}</span>
                                </a>
                              )}
                            </Menu.Item>
                            <Menu.Item>
                              {({ active }) => (
                                <button
                                  onClick={() => openDeleteDialog(item)}
                                  className={twMerge(
                                    active ? 'bg-gray-50' : '',
                                    'block px-3 py-1 text-sm leading-6 text-gray-900'
                                  )}
                                >
                                  {t('common.delete')}
                                  <span className="sr-only">, {item.name}</span>
                                </button>
                              )}
                            </Menu.Item>
                          </Menu.Items>
                        </Transition>
                      </Menu>
                    </div>
                  </li>
                )
              )}
            </ul>
          )}
        </div>
      </PageWrapper>
    </>
  )
}
