import { useParams } from 'react-router-dom'

import { useGetRecipe } from 'services/RecipeService'

import SkeletonLoading from 'components/Common/SkeletonLoading'
import BaseView from 'components/RecipeView/BaseView'

export default function RecipeViewPage() {
  const params = useParams()

  const recipeRequest = useGetRecipe(params.id as string)

  if (recipeRequest.isLoading) {
    return <SkeletonLoading />
  }

  if (recipeRequest.data === null) {
    return <div>not found</div>
  }

  if (recipeRequest.isError || recipeRequest.data === undefined) {
    return <div>Error</div>
  }

  const recipe = recipeRequest.data

  const onUpdate = () => {
    recipeRequest.refetch()
  }

  return <BaseView recipe={recipe} onUpdate={onUpdate} />
}
