import { UserCircleIcon, ViewfinderCircleIcon } from '@heroicons/react/20/solid'
import { PlayCircleIcon } from '@heroicons/react/24/solid'
import type { BrewingFermentable } from '@prisma/client'
import { UseQueryResult } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import { post, put, useHttpNotifications } from 'helpers/http'
import { readableLableForKilo } from 'helpers/unit'
import { useWebsocketMessage } from 'hooks/useWebsocket'
import { BrewingResponse, useGetBrewing } from 'services/BrewingService'

import BoilingCard from 'components/Brewing/BoilingCard'
import MashingCard from 'components/Brewing/MashingCard'
import Button from 'components/Common/Button'
import Card from 'components/Common/Card'
import HiddenCard from 'components/Common/HiddenCard'
import Loading from 'components/Common/Loading'
import FancyPageHead from 'components/Common/Page/FancyPageHead'

const HIERARCHY = ['WAITING', 'PREMASHING', 'MASHING', 'LAUTERING', 'BOILING', 'FERMENTATION']

export default function BrewingViewPage() {
  const params = useParams()

  const brewingRequest = useGetBrewing(params.id as string)

  if (brewingRequest.isLoading === null) {
    return <Loading />
  }

  if (!brewingRequest.data) {
    return <div>not found</div>
  }

  return <BrewingLoadedViewPage brewing={brewingRequest.data} brewingRequest={brewingRequest} />
}

function BrewingLoadedViewPage(props: {
  brewing: BrewingResponse
  brewingRequest: UseQueryResult<BrewingResponse, Error>
}) {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()
  const [brewing, setBrewing] = useState<BrewingResponse>(props.brewing)
  const [hIndex, setHIndex] = useState(HIERARCHY.indexOf(props.brewing.status) ?? 0)
  const [showFermentables, setShowFermentables] = useState(!(hIndex > 1))
  const [showMashing, setShowMashing] = useState(!(hIndex > 2))

  const params = useParams()

  const { brewingRequest } = props

  useEffect(() => {
    if (brewingRequest.data === undefined) {
      return
    }

    setBrewing(brewingRequest.data)
    setHIndex(HIERARCHY.indexOf(brewingRequest.data.status) ?? 0)
  }, [brewingRequest.data])

  useWebsocketMessage(`brewing/${params.id}`, _message => {
    brewingRequest.refetch()
  })

  const startMashing = async () => {
    if (!brewing) {
      return
    }

    await resolveResponse(() =>
      post(`brewing/${brewing?.id}/mashing`, {
        ...brewing,
        mashSteps: { create: brewing.mashSteps },
      })
    )

    brewingRequest.refetch()
  }

  const addedFermentable = (fermentable: BrewingFermentable) => {
    setBrewing(brewing => {
      if (!brewing) {
        return brewing
      }

      const index = brewing?.fermentables.findIndex(f => f.id === fermentable.id)

      if (index < -1) {
        return brewing
      }

      const fermentables = [...brewing.fermentables]
      const model = { ...fermentables.splice(index, 1)[0] }
      model.added = true
      fermentables.push(model)

      put(`brewing/${brewing.id}/fermentable/${fermentable.id}`, {
        ...model,
      })

      return {
        ...brewing,
        fermentables: fermentables,
      }
    })
  }

  return (
    <main>
      <FancyPageHead title={t('brew.single')} name={brewing?.recipe?.name ?? 'Kein Name'} />

      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto grid max-w-2xl grid-cols-1 grid-rows-1 items-start gap-x-8 gap-y-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          <div className="lg:col-start-3 lg:row-end-1">
            <h2 className="sr-only">Summary</h2>
            <div className="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
              <dl className="flex flex-wrap">
                <div className="flex-auto pl-6 pt-6">
                  <dt className="text-sm font-semibold leading-6 text-gray-900">
                    {t('recipe.startBrew')}
                  </dt>
                  <dd className="mt-1 text-base font-semibold leading-6 text-gray-900">
                    {t('recipe.allTimeForToday', {
                      time: brewing?.mashSteps?.reduce((acc, cur) => acc + cur.time, 0) ?? 0,
                    })}
                  </dd>
                </div>
                <div className="flex-none self-end px-3 pt-4">
                  <button
                    onClick={startMashing}
                    className="flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-600 ring-1 ring-inset ring-green-600/20"
                  >
                    <PlayCircleIcon className="mr-3 h-8 w-8" aria-hidden="true" />{' '}
                    {t('recipe.start')}
                  </button>
                </div>
                <div className="mt-6 flex w-full flex-none gap-x-4 border-t border-gray-900/5 px-6 pt-6">
                  <dt className="flex-none">
                    <span className="sr-only">{t('recipe.brewer')}</span>
                    <UserCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                  </dt>
                  <dd className="text-sm font-medium leading-6 text-gray-900">
                    {brewing?.recipe?.brewer}
                  </dd>
                </div>
                {brewing?.recipe?.ibu && (
                  <div className="mt-4 flex w-full flex-none gap-x-4 px-6">
                    <dt className="flex-none">
                      <span className="sr-only">{t('recipe.ibu')}</span>
                      <ViewfinderCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                    </dt>
                    <dd className="text-sm leading-6 text-gray-500">
                      <time dateTime="2023-01-31">{brewing?.recipe.ibu}</time>
                    </dd>
                  </div>
                )}
                {brewing?.recipe?.abv && (
                  <div className="mt-4 flex w-full flex-none gap-x-4 px-6">
                    <dt className="flex-none">
                      <span className="sr-only">{t('recipe.alcohol')}</span>
                      <ViewfinderCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                    </dt>
                    <dd className="text-sm leading-6 text-gray-500">{brewing?.recipe.abv}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Main Part */}

          <div className="lg:col-span-2 lg:row-span-2 lg:row-end-2">
            <div className="flex flex-col gap-3">
              {showFermentables ? (
                <Card title={t('brew.fermentables')}>
                  <div className="divide-y divide-gray-200 border-b border-t border-gray-200">
                    {brewing.fermentables.map(fermentable => (
                      <div key={fermentable.id} className="relative flex items-start py-4">
                        <div className="min-w-0 flex-1 text-sm leading-6">
                          <label
                            htmlFor={`fermentable-${fermentable.id}`}
                            className={twMerge(
                              'select-none font-medium text-gray-900',
                              fermentable.added ? 'line-through' : ''
                            )}
                          >
                            {readableLableForKilo(fermentable.amount)} - {fermentable.name}
                          </label>
                        </div>
                        <div className="ml-3 flex h-6 items-center">
                          <input
                            id={`fermentable-${fermentable.id}`}
                            name={`fermentable-${fermentable.id}`}
                            type="checkbox"
                            checked={fermentable.added}
                            onChange={() => addedFermentable(fermentable)}
                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button className="mt-4" onClick={startMashing}>
                    {t('brew.startMashing')}
                  </Button>
                </Card>
              ) : (
                <HiddenCard
                  title={t('brew.fermentables')}
                  onOpen={() => setShowFermentables(true)}
                />
              )}

              {showMashing ? (
                <MashingCard brewing={brewing} brewingRequest={props.brewingRequest} />
              ) : (
                <HiddenCard title={t('brew.mashing')} onOpen={() => setShowMashing(true)} />
              )}

              <BoilingCard brewing={brewing} hIndex={hIndex} brewingRequest={brewingRequest} />

              <Card title={t('brew.fermentation')}>
                Test Fermenatation
                {/* <YeastTable
                yeasts={recipe.yeasts}
                onUpdate={() => {}}
                setFermentationTemp={temperature =>
                  setBrewing(b => ({ ...b, fermentationTemperature: temperature }))
                }
              /> */}
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
