import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import type { Device } from '@prisma/client'
import { iconUrl } from 'constants'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import useIntegrationStore from 'stores/IntegrationStore'

import tuyaIcon from 'assets/tuya.png'

import { del, post, put, useGet, useHttpNotifications } from 'helpers/http'
import useDeleteDialog from 'hooks/Dialog/useDeleteDialog'
import useEditDialog from 'hooks/Dialog/useEditDialog'

import Button from 'components/Common/Button'
import Card from 'components/Common/Card'
import ContextMenu from 'components/Common/ContextMenu'
import { EmptyListBox } from 'components/Common/EmptyListBox'
import PageHead from 'components/Common/Page/PageHead'
import PageWrapper from 'components/Common/Page/PageWrapper'
import SkeletonLoading from 'components/Common/SkeletonLoading'
import EditForm from 'components/Device/EditForm'
import MqttDialog from 'components/Device/MqttDialog'
import SelectDeviceForm from 'components/Device/SelectDeviceForm'
import SmartLifeForm from 'components/Device/SmartLifeForm'
import TuyaForm from 'components/Device/TuyaForm'

const deviceStatus = {
  online: 'text-green-700 bg-green-50 ring-green-600/20',
  undefined: 'text-gray-600 bg-gray-50 ring-gray-500/10',
  offline: 'text-red-700 bg-red-50 ring-red-600/10',
}

export default function DevicePage(): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()
  const integration = useIntegrationStore.getState()

  const reqFermentationDevices = useGet<Device[]>('fermentation/device')

  const reqBrewDevices = useGet<Device[]>('brewing/device')

  const { openDialog, openEditDialog } = useEditDialog<Device>(
    t('device.name'),
    EditForm,
    async (data, initData) => {
      if (initData) {
        await resolveResponse(() => put(`device/${initData.id}`, data))
      } else {
        await resolveResponse(() => post('device', { ...data, operator: 'CUSTOM' }))
      }
      deviceRequest.refetch()
    }
  )

  const { openDialog: openTuyaDialog } = useEditDialog<Device>(
    t('device.addTuyaDevice'),
    TuyaForm,
    async (data, initData) => {
      if (initData) {
        await resolveResponse(() => put(`device/${initData.id}`, data))
      } else {
        await resolveResponse(() => post('device', data))
      }
      deviceRequest.refetch()
    }
  )

  const { openDialog: openSmartLifeDialog } = useEditDialog<Device>(
    t('device.addSmartLifeDevice'),
    SmartLifeForm,
    async (data, initData) => {
      deviceRequest.refetch()
    }
  )

  const openDeleteDialog = useDeleteDialog(async data => {
    await resolveResponse(() => del(`device/${data.id}`))
    deviceRequest.refetch()
  })

  const openDeleteDialogForBrewing = useDeleteDialog(async data => {
    await resolveResponse(() => del(`brewing/device/${data.id}`))
    reqBrewDevices.refetch()
  })

  const deviceRequest = useGet<Device[]>('device')

  const { openDialog: openBrewDialog } = useEditDialog(
    t('device.name'),
    SelectDeviceForm,
    async data => {
      await resolveResponse(() =>
        post('brewing/device', {
          deviceId: data.id,
          type: data.type,
        })
      )
      reqBrewDevices.refetch()
    }
  )

  const { openEditDialog: openMqtt } = useEditDialog<Device>(
    t('device.mqtt'),
    MqttDialog,
    async data => {
      // nope
    }
  )

  const buttons = [
    {
      children: (
        <>
          <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
          {t('device.addDevice')}
        </>
      ),
      onClick: openDialog,
      style: 'primary',
    },
  ]

  const contextMenu = []

  if (integration.tuya) {
    contextMenu.unshift({
      children: t('device.addTuyaDevice'),
      onClick: openTuyaDialog,
    })
  }

  if (integration.smartlife) {
    buttons.unshift({
      children: (
        <>
          <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
          {t('device.addSmartLifeDevice')}
        </>
      ),
      onClick: openSmartLifeDialog,
      style: 'secondary',
      icon: true,
    })
  }

  return (
    <>
      <PageHead title={t('navigation.device')} buttons={buttons} contextMenu={contextMenu} />
      <PageWrapper>
        <div className="mt-5 flex h-full flex-col">
          <div className="grid grid-cols-2 gap-4">
            <Card
              title={t('device.fermentation')}
              className="col-span-1"
              buttons={[
                {
                  children: (
                    <>
                      <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                      {t('device.addDevice')}
                    </>
                  ),
                  onClick: openBrewDialog,
                  style: 'primary',
                  icon: true,
                },
              ]}
            >
              <>
                {reqFermentationDevices.isLoading && <SkeletonLoading />}
                {!reqFermentationDevices.isLoading && (
                  <EmptyListBox data={reqFermentationDevices.data}></EmptyListBox>
                )}
                {reqFermentationDevices.data && (
                  <ul role="list" className="divide-y divide-gray-100">
                    {reqFermentationDevices.data?.map(device => (
                      <li key={device.id} className="flex justify-between gap-x-6 py-5">
                        <div className="flex min-w-0 gap-x-4">
                          <img
                            className="h-12 w-12 flex-none rounded-full bg-gray-50"
                            src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                            alt=""
                          />
                          <div className="min-w-0 flex-auto">
                            <p className="text-sm font-semibold leading-6 text-gray-900">
                              {device.name}
                            </p>
                            <p className="mt-1 truncate text-xs leading-5 text-gray-500">
                              {device.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex shrink-0 items-center">
                          <Button
                            style="secondary"
                            size="sm"
                            icon={true}
                            onClick={e => openDeleteDialogForBrewing(device)}
                          >
                            <TrashIcon className="h-5 w-5" aria-hidden="true" />
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </>
            </Card>
            <Card
              title={t('device.brewing')}
              className="col-span-1"
              buttons={[
                {
                  children: (
                    <>
                      <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                      {t('device.addDevice')}
                    </>
                  ),
                  onClick: openBrewDialog,
                  style: 'primary',
                  icon: true,
                },
              ]}
            >
              <>
                {reqBrewDevices.isLoading && <SkeletonLoading />}
                {!reqBrewDevices.isLoading && (
                  <EmptyListBox data={reqBrewDevices.data}></EmptyListBox>
                )}
                {reqBrewDevices.data && (
                  <ul role="list" className="divide-y divide-gray-100">
                    {reqBrewDevices.data?.map(device => (
                      <li key={device.id} className="flex justify-between gap-x-6 py-5">
                        <div className="flex min-w-0 gap-x-4">
                          <img
                            className="h-12 w-12 flex-none rounded-full bg-gray-50"
                            src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                            alt=""
                          />
                          <div className="min-w-0 flex-auto">
                            <p className="text-sm font-semibold leading-6 text-gray-900">
                              {device.name}
                            </p>
                            <p className="mt-1 truncate text-xs leading-5 text-gray-500">
                              {device.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex shrink-0 items-center">
                          <Button
                            style="secondary"
                            size="sm"
                            icon={true}
                            onClick={e => openDeleteDialogForBrewing(device)}
                          >
                            <TrashIcon className="h-5 w-5" aria-hidden="true" />
                          </Button>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </>
            </Card>
          </div>
        </div>

        <div className="mt-5">
          <EmptyListBox data={deviceRequest.data} onAdd={openDialog} />
        </div>

        <ul role="list" className="grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          {deviceRequest.data?.map(device => (
            <li key={device.id} className="rounded-xl border border-gray-200">
              <div className="flex items-center gap-x-4 border-b border-gray-900/5 bg-white p-6">
                <div className="flex items-center gap-x-4 overflow-hidden">
                  <img
                    src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                    alt={device.name ?? ''}
                    className="h-12 w-12 flex-none rounded-lg bg-white object-cover ring-1 ring-gray-900/10"
                  />
                  <div className="text-sm font-medium leading-6 text-gray-900">
                    {device.name}
                    {/* <div
                    className={twMerge(
                      deviceStatus['online'],
                      'rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset'
                    )}
                  >
                    online
                  </div> */}
                  </div>
                </div>
                <ContextMenu
                  buttons={[
                    {
                      children: t('common.edit'),
                      onClick: () => openEditDialog(device),
                    },
                    { children: t('device.mqtt'), onClick: () => openMqtt(device) },
                    {
                      children: t('common.delete'),
                      onClick: () => openDeleteDialog(device),
                    },
                  ]}
                />
              </div>
              <dl className="-my-3 divide-y divide-gray-100 overflow-hidden px-6 py-4 text-sm leading-6">
                <div className="flex justify-between gap-x-4 py-3">
                  <dt className="text-gray-500">{t('device.deviceType')}</dt>
                  <dd className="text-gray-700">{t('device.type.' + device.type.toLowerCase())}</dd>
                </div>
              </dl>
            </li>
          ))}
        </ul>
      </PageWrapper>
    </>
  )
}
