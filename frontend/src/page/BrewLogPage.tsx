import { Menu, Transition } from '@headlessui/react'
import { EllipsisVerticalIcon } from '@heroicons/react/20/solid'
import { Device } from '@prisma/client'
import { Fragment, ReactElement, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import { del, get, useGet, useHttpNotifications } from 'helpers/http'
import useDeleteDialog from 'hooks/Dialog/useDeleteDialog'
import { BrewingResponse, useGetBrewings } from 'services/BrewingService'

import Button from 'components/Common/Button'
import Card from 'components/Common/Card'
import ContextMenu from 'components/Common/ContextMenu'
import EmptyListBox from 'components/Common/EmptyListBox'
import PageHead from 'components/Common/Page/PageHead'
import PageWrapper from 'components/Common/Page/PageWrapper'

export default function BrewLogPage(): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()

  const [activeBrews, setActiveBrews] = useState<BrewingResponse[]>([])

  const requestBrews = useGetBrewings()

  useEffect(() => {
    setActiveBrews(
      requestBrews.data?.filter(
        b => b.status !== 'DONE' && b.status !== 'CANCELED' && b.status !== 'WAITING'
      ) ?? []
    )
  }, [requestBrews.data])

  const deviceConnected = useGet<Device[]>('brewing/device')

  const openDeleteDialog = useDeleteDialog(async item => {
    await resolveResponse(async () => await del(`brewing/${item.id}`))
    requestBrews.refetch()
  })

  const getTitleForBrew = (brew: BrewingResponse): string => {
    // if (brew.status === 'HEATING') {
    //   return t('brew.heating')
    // }
    if (brew.status === 'MASHING' && brew.mashSteps.length > 0) {
      return t('brew.timerTitle', brew.mashSteps[0])
    }

    return ''
  }

  return (
    <>
      <PageHead
        title={t('navigation.brewlog')}
        buttons={[
          {
            children: t('brew.selectDevices') + ` (${deviceConnected.data?.length})`,
            onClick: () => {},
          },
        ]}
      />
      <PageWrapper>
        <div className="mt-5 flex h-full flex-col">
          <Card title={t('brew.actualProcess')}>
            <>
              {activeBrews.length === 0 && <span>{t('brew.noProcessAtTheMoment')}</span>}
              {activeBrews.length > 0 && (
                <ul role="list" className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {activeBrews.map(brew => (
                    <li
                      key={brew.id}
                      className="col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow"
                    >
                      <div className="flex w-full items-center justify-between space-x-6 p-6">
                        <div className="flex-1 truncate">
                          <div className="flex items-center space-x-3">
                            <h3
                              className="truncate text-sm font-medium text-gray-900"
                              title={getTitleForBrew(brew)}
                            >
                              {getTitleForBrew(brew)}
                            </h3>
                            <span className="inline-flex flex-shrink-0 items-center rounded-full bg-green-50 px-1.5 py-0.5 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                              {brew.status}
                            </span>
                          </div>
                          <p className="mt-1 truncate text-sm text-gray-500">
                            {brew.recipe?.name ?? 'Keine Name'}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <Button to={`/brewing/${brew.id}`}>{t('common.open')}</Button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </>
          </Card>

          <div className="mt-5">
            <EmptyListBox data={requestBrews.data} />
            {requestBrews.data && (
              <ul role="list" className="divide-y divide-gray-100">
                {requestBrews.data.map(brew => (
                  <li key={brew.id} className="flex items-center justify-between gap-x-6 py-5">
                    <div className="min-w-0">
                      <div className="flex items-start gap-x-3">
                        <Link to={`/recipe/${brew.id}`}>
                          <p className="text-sm font-semibold leading-6 text-gray-900">
                            {brew.recipe?.name ?? 'Keine Name'}
                          </p>
                        </Link>
                      </div>
                      <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                        <p className="whitespace-nowrap">
                          {brew.recipe && (
                            <>
                              {t('brew.createdByRecipe')}{' '}
                              <Link
                                className="text-gray-900 hover:underline"
                                to={`/recipe/${brew.recipe.id}/view`}
                              >
                                {brew.recipe.name}
                              </Link>
                            </>
                          )}
                        </p>
                        <svg viewBox="0 0 2 2" className="h-0.5 w-0.5 fill-current">
                          <circle cx={1} cy={1} r={1} />
                        </svg>
                        <p className="whitespace-nowrap">
                          {t('common.createdAt', {
                            date: new Date(brew.createdAt as string).toLocaleString(),
                          })}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-none items-center gap-x-4">
                      <Link
                        to={`/brewing/${brew.id}`}
                        className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:block"
                      >
                        {t('common.open')}
                      </Link>
                      <ContextMenu
                        buttons={[
                          { onClick: () => openDeleteDialog(brew), children: t('common.delete') },
                          {
                            onClick: () => get(`brewing/${brew.id}/test-hop`),
                            children: 'Test Nachricht',
                          },
                        ]}
                      />
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </PageWrapper>
    </>
  )
}
