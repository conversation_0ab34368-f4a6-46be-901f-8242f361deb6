import { InventaryItemModel } from '@backendZod/inventaryitem'
import { Menu, Transition } from '@headlessui/react'
import { EllipsisVerticalIcon, PencilIcon, PlusIcon } from '@heroicons/react/20/solid'
import type { InventaryItem } from '@prisma/client'
import { Fragment, ReactElement, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'
import * as z from 'zod'

import { del, post, put, useGet, useHttpNotifications } from 'helpers/http'
import useDeleteDialog from 'hooks/Dialog/useDeleteDialog'
import useEditDialog from 'hooks/Dialog/useEditDialog'
import types from 'hooks/useInventoryTypes'

import { ButtonWithIcon } from 'components/Common/Button'
import { EmptyListBox } from 'components/Common/EmptyListBox'
import PageHead from 'components/Common/Page/PageHead'
import PageWrapper from 'components/Common/Page/PageWrapper'
import EditForm from 'components/Inventory/EditForm'

export default function InventoryPage(): ReactElement {
  const { t } = useTranslation()
  const [currentTab, setCurrentTab] = useState<string | null>(null)
  const resolveResponse = useHttpNotifications()

  const inventoryRequest = useGet<InventaryItem[]>('inventory')

  type CompleteInventaryItem = z.infer<typeof InventaryItemModel>

  const onSubmit = async (data: CompleteInventaryItem, initItem?: InventaryItem) => {
    if (initItem) {
      await resolveResponse(() => put(`inventory/${initItem.id}`, data))
    } else {
      await resolveResponse(() => post('inventory', data))
    }
    inventoryRequest.refetch()
  }
  const { openDialog, openEditDialog } = useEditDialog<InventaryItem>(
    t('inventory.newEntry'),
    EditForm,
    onSubmit
  )

  const openDeleteDialog = useDeleteDialog<InventaryItem>(async (item: InventaryItem) => {
    await resolveResponse(() => del(`inventory/${item.id}`))
    inventoryRequest.refetch()
  })

  const tabs = [
    { name: t('inventory.everything'), value: null },
    ...types.map(type => ({ name: type.name, value: type.id })),
  ]

  const items = useMemo(() => {
    if (currentTab == null || !inventoryRequest.data) {
      return inventoryRequest.data
    }

    return inventoryRequest.data.filter(item => item.type == currentTab)
  }, [currentTab, inventoryRequest])

  return (
    <>
      <PageHead
        title={t('inventory.name')}
        buttons={[
          {
            children: (
              <>
                <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
                {t('inventory.newEntry')}
              </>
            ),
            onClick: openDialog,
          },
        ]}
      />
      <PageWrapper>
        <div className="relative border-b border-gray-200 pb-5 sm:pb-0">
          <div className="mt-4">
            <div className="sm:hidden">
              <label htmlFor="current-tab" className="sr-only">
                {t('common.selectTab')}
              </label>
              <select
                id="current-tab"
                name="current-tab"
                className="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
                defaultValue={
                  tabs.find(tab => tab.value == currentTab)?.name ?? t('inventory.everything')
                }
              >
                {tabs.map(tab => (
                  <option key={tab.name}>{tab.name}</option>
                ))}
              </select>
            </div>
            <div className="hidden sm:block">
              <nav className="-mb-px flex space-x-8">
                {tabs.map(tab => (
                  <span
                    key={tab.name}
                    onClick={() => setCurrentTab(tab.value)}
                    className={twMerge(
                      tab.value == currentTab
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                      'whitespace-nowrap border-b-2 px-1 pb-4 text-sm font-medium'
                    )}
                    aria-current={tab.value == currentTab ? 'page' : undefined}
                  >
                    {tab.name}
                  </span>
                ))}
              </nav>
            </div>
          </div>
        </div>
        <div className="mt-5">
          {/** Empty State */}
          <EmptyListBox data={items} onAdd={openDialog} />
          {items && (
            <ul role="list" className="divide-y divide-gray-100">
              {items.map(item => (
                <li key={item.id} className="flex justify-between gap-x-6 py-5">
                  <div className="flex min-w-0 gap-x-4">
                    <div className="min-w-0 flex-auto">
                      <p className="text-sm font-semibold leading-6 text-gray-900">{item.name}</p>
                      <p className="mt-1 truncate text-xs leading-5 text-gray-500">
                        <span>
                          {item.amount} {item.unit}
                        </span>
                        {item?.extraData &&
                          Object.keys(item.extraData).map(key => (
                            <span className="ml-3" key={key}>
                              {t(`${item.type}.${key}`)} {item.extraData[key] ?? ''}
                            </span>
                          ))}{' '}
                        {/** TODO: add extra values from the fields */}
                      </p>
                    </div>
                  </div>
                  <div className="flex shrink-0 items-center gap-x-4">
                    <div className="hidden sm:flex sm:flex-col sm:items-end">
                      <p className="text-sm leading-6 text-gray-900">
                        {/** TODO: remove id with something useful */ item.id}
                      </p>
                      <div className="mt-1 flex items-center gap-x-1.5">
                        <div className="h-5 w-5 flex-none">
                          {types?.find(t => t.id == item.type)?.icon}
                        </div>
                        <p className="text-xs leading-5 text-gray-500">
                          {types?.find(t => t.id == item.type)?.name}
                        </p>
                      </div>
                    </div>
                    <ButtonWithIcon
                      onClick={() => openEditDialog(item)}
                      style="secondary"
                      className="flex-none"
                      size="sm"
                    >
                      <PencilIcon />
                      {t('common.edit')}
                    </ButtonWithIcon>
                    <Menu as="div" className="relative flex-none">
                      <Menu.Button className="-m-2.5 block p-2.5 text-gray-500 hover:text-gray-900">
                        <span className="sr-only">{t('common.openContextMenu')}</span>
                        <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
                      </Menu.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-2 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                onClick={() => openDeleteDialog(item)}
                                className={twMerge(
                                  active ? 'bg-gray-50' : '',
                                  'block px-3 py-1 text-sm leading-6 text-gray-900'
                                )}
                              >
                                {t('common.delete')}
                                <span className="sr-only">, {item.name}</span>
                              </button>
                            )}
                          </Menu.Item>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </PageWrapper>
    </>
  )
}
