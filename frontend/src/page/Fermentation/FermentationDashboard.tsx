import { useTranslation } from 'react-i18next'

import { useGetBrewtools } from 'services/BrewtoolService'

import DeviceList from 'components/Fermentation/DeviceList'

const FermentationDashboard = () => {
  const { t } = useTranslation()

  const { data: devices } = useGetBrewtools()

  return (
    <div className="mx-auto max-w-7xl p-4 md:p-6">
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 md:text-3xl">
          {t('fermentation.monitor')}
        </h1>
        <p className="text-gray-600">{t('fermentation.trackAndControl')}</p>
      </header>
      <DeviceList devices={devices} />
    </div>
  )
}
export default FermentationDashboard
