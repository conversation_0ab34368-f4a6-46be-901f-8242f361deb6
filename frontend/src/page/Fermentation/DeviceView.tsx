import { ArrowLeftIcon } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'

import { createSuccess } from 'hooks/useSimpleNotification'
import {
  createBrewtoolFermentation,
  updateBrewtoolFermentation,
  useGetBrewtool,
} from 'services/BrewtoolService'

import DeviceDetail from 'components/Fermentation/DeviceDetail'
import FermentationDialog from 'components/Fermentation/FermentationDialog'
import PlatoAlertDialog from 'components/Fermentation/PlatoAlertDialog'
import SpindelSelectDialog from 'components/Fermentation/SpindelSelectDialog'

const COLD_CRUSH = 4

const DeviceView = () => {
  const { t } = useTranslation()
  const { deviceId } = useParams()
  const navigate = useNavigate()
  const { data: device, refetch } = useGetBrewtool(deviceId ?? '')

  const [showFermentationDialog, setShowFermentationDialog] = useState(false)
  const [showPlatoAlertDialog, setShowPlatoAlertDialog] = useState(false)
  const [showSpindelSelectDialog, setShowSpindelSelectDialog] = useState(false)

  const { mutate } = createBrewtoolFermentation()
  const { mutate: update } = updateBrewtoolFermentation()

  useEffect(() => {
    const intervalId = setInterval(() => {
      refetch()
    }, 1000 * 30)
    return () => clearInterval(intervalId)
  }, [])

  if (!deviceId || !device) {
    return (
      <div className="flex h-screen flex-col items-center justify-center">
        <p className="text-xl text-gray-700">{t('fermentation.deviceNotFound')}</p>
        <button
          className="mt-4 flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white"
          onClick={() => navigate(-1)}
        >
          <ArrowLeftIcon size={16} className="mr-2" /> {t('fermentation.backToDashboard')}
        </button>
      </div>
    )
  }

  const handleStartFermentation = data => {
    if (!device.id) return

    mutate({
      deviceId: device.id,
      data: {
        recipeName: data.title ?? undefined,
        temperature: parseFloat(data.targetTemp),
        sendPush: false,
      },
    })
    createSuccess(
      t('fermentation.notificationTemperatureSet', { temperature: parseFloat(data.targetTemp) })
    )

    setShowFermentationDialog(false)
  }

  const handleColdCrash = () => {
    mutate({
      deviceId: device.id,
      data: {
        temperature: COLD_CRUSH,
        sendPush: false,
      },
    })
    createSuccess(t('fermentation.notificationTemperatureSet', { temperature: COLD_CRUSH }))
  }

  const handleTurnOff = () => {
    update({
      deviceId: device.id,
      data: {
        endDate: new Date(),
      },
    })
    createSuccess(t('fermentation.notificationPowerOff'))
    // createError(t('Gärschrank ausgeschalten fehlgeschlagen'))
  }

  const handleSetPlatoAlert = threshold => {
    // In a real app, this would set up an alert
    console.log(`Alert set for when Plato reaches ${threshold}`)
    setShowPlatoAlertDialog(false)
  }

  const handleConnectSpindle = () => {
    setShowSpindelSelectDialog(true)
  }

  const handleSpindleConnected = () => {
    setShowSpindelSelectDialog(false)
    refetch()
  }

  return (
    <div className="mx-auto max-w-7xl p-4 md:p-6">
      <button
        className="mb-4 flex items-center rounded-lg bg-gray-200 px-3 py-1.5 text-gray-700 hover:bg-gray-300"
        onClick={() => navigate(-1)}
      >
        <ArrowLeftIcon size={16} className="mr-1" /> {t('fermentation.back')}
      </button>
      <DeviceDetail
        device={device}
        onStartFermentation={() => setShowFermentationDialog(true)}
        onColdCrash={handleColdCrash}
        onTurnOff={handleTurnOff}
        onPlatoAlert={() => setShowPlatoAlertDialog(true)}
        onConnectSpindle={handleConnectSpindle}
      />
      {showFermentationDialog && (
        <FermentationDialog
          onClose={() => setShowFermentationDialog(false)}
          onSubmit={handleStartFermentation}
          initialTitle={''}
        />
      )}
      {showPlatoAlertDialog && (
        <PlatoAlertDialog
          onClose={() => setShowPlatoAlertDialog(false)}
          onSubmit={handleSetPlatoAlert}
          currentPlato={device.plato}
        />
      )}
      {showSpindelSelectDialog && (
        <SpindelSelectDialog
          onClose={() => setShowSpindelSelectDialog(false)}
          onSubmit={handleSpindleConnected}
          brewtool={device}
        />
      )}
    </div>
  )
}
export default DeviceView
