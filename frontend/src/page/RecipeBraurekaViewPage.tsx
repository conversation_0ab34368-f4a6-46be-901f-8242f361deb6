import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'

import { generateRecipeFromBeerXml } from 'helpers/beerXml'
import { useGet } from 'helpers/http'
import { RecipeRequestResponse } from 'services/RecipeService'

import SkeletonLoading from 'components/Common/SkeletonLoading'
import BaseView from 'components/RecipeView/BaseView'

export default function RecipeBraurekaViewPage() {
  const params = useParams()

  const recipeRequest = useGet<{ beerXml: string }>('integration/braureka/recipe/' + params.id)
  const [recipe, setRecipe] = useState<RecipeRequestResponse | null>(null)

  useEffect(() => {
    if (!recipeRequest.data) {
      return
    }

    const fetchRecipe = async () => {
      const recipe = await generateRecipeFromBeerXml(recipeRequest.data.beerXml, false)
      setRecipe(recipe as unknown as RecipeRequestResponse)
    }

    fetchRecipe()
  }, [recipeRequest.data])

  if (recipeRequest.isLoading) {
    return <SkeletonLoading />
  }

  if (recipe === null) {
    return <div>not found</div>
  }

  if (recipeRequest.isError || recipeRequest.data === undefined) {
    return <div>Error</div>
  }

  return <BaseView recipe={recipe} onUpdate={() => {}} />
}
