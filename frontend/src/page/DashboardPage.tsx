import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'

import { useGetRecipes } from 'services/RecipeService'

import Card from 'components/Common/Card'
import { EmptyListBox } from 'components/Common/EmptyListBox'
import PageHead from 'components/Common/Page/PageHead'
import PageWrapper from 'components/Common/Page/PageWrapper'
import SkeletonLoading from 'components/Common/SkeletonLoading'
import FermentationCard from 'components/Dashboard/FermentationCard'

export default function DashboardPage(): ReactElement {
  const { t } = useTranslation()

  const recipes = useGetRecipes('limit=5')

  return (
    <>
      <PageHead title={t('navigation.dashboard')} />
      <PageWrapper>
        <div className="mt-5 flex h-full flex-col">
          <div className="grid grid-flow-row grid-cols-1 gap-4 sm:grid-cols-4">
            <FermentationCard />
            <Card title={t('navigation.recipe')} className="col-span-2">
              <div>
                {/** Empty State */}
                {recipes.isLoading && <SkeletonLoading />}
                {!recipes.isLoading && <EmptyListBox data={recipes.data}></EmptyListBox>}
                {recipes.data && (
                  <ul role="list" className="divide-y divide-gray-100">
                    {recipes.data.map(recipe => (
                      <li
                        key={recipe.id}
                        className="flex items-center justify-between gap-x-6 py-5"
                      >
                        <div className="min-w-0">
                          <div className="flex items-start gap-x-3">
                            <Link to={`/recipe/${recipe.id}/view`}>
                              <p className="text-sm font-semibold leading-6 text-gray-900">
                                {recipe.name}
                              </p>
                            </Link>
                          </div>
                          <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
                            <p className="whitespace-nowrap">{t('recipe.createdByBeerXml')}</p>
                            <svg viewBox="0 0 2 2" className="h-0.5 w-0.5 fill-current">
                              <circle cx={1} cy={1} r={1} />
                            </svg>
                            <p className="truncate">
                              {t('recipe.createdAt', {
                                date: new Date(recipe.createdAt as string).toLocaleString(),
                              })}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-none items-center gap-x-4">
                          <Link
                            to={`/recipe/${recipe.id}/view`}
                            className="hidden rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:block"
                          >
                            {t('recipe.viewRecipe')}{' '}
                            <span className="sr-only">, {recipe.name}</span>
                          </Link>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </Card>
          </div>
        </div>
      </PageWrapper>
    </>
  )
}
