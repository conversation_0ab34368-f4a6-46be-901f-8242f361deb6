import { UserCircleIcon } from '@heroicons/react/24/outline'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import useUser from 'hooks/useUser'

import PageHead from 'components/Common/Page/PageHead'
import BraurekaSection from 'components/Settings/BraurekaSection'
import ChangePasswordSection from 'components/Settings/ChangePasswordSection'
import DeleteAccountSection from 'components/Settings/DeleteAccountSection'
import PushSubscriptionSection from 'components/Settings/PushSubscriptionSection'
import Section from 'components/Settings/Section'
import SmartLifeSection from 'components/Settings/SmartLifeSection'
import TuyaSection from 'components/Settings/TuyaSection'

export default function UserSettingsPage() {
  const { t } = useTranslation()

  const user = useUser()
  const userData = [
    { title: t('user.fullName'), value: user.name },
    { title: t('user.email'), value: user.email },
  ]

  const secondaryNavigation = useMemo(
    () => [
      { name: t('user.account'), href: '#', icon: UserCircleIcon, current: true },
      //   { name: 'Security', href: '#', icon: FingerPrintIcon, current: false },
      //   { name: 'Notifications', href: '#', icon: BellIcon, current: false },
      //   { name: 'Plan', href: '#', icon: CubeIcon, current: false },
      //   { name: 'Billing', href: '#', icon: CreditCardIcon, current: false },
      //   { name: 'Team members', href: '#', icon: UsersIcon, current: false },
    ],
    []
  )

  return (
    <>
      <PageHead title={t('navigation.userSettings')} />
      <div className="mx-auto max-w-7xl lg:flex lg:gap-x-16 lg:px-8">
        <h1 className="sr-only">General Settings</h1>

        <aside className="flex overflow-x-auto border-b border-gray-900/5 py-4 lg:block lg:w-64 lg:flex-none lg:border-0 lg:py-20">
          <nav className="flex-none px-4 sm:px-6 lg:px-0">
            <ul role="list" className="flex gap-x-3 gap-y-1 whitespace-nowrap lg:flex-col">
              {secondaryNavigation.map(item => (
                <li key={item.name}>
                  <a
                    href={item.href}
                    className={twMerge(
                      item.current
                        ? 'bg-gray-50 text-indigo-600'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-indigo-600',
                      'group flex gap-x-3 rounded-md py-2 pl-2 pr-3 text-sm font-semibold leading-6'
                    )}
                  >
                    <item.icon
                      className={twMerge(
                        item.current
                          ? 'text-indigo-600'
                          : 'text-gray-400 group-hover:text-indigo-600',
                        'h-6 w-6 shrink-0'
                      )}
                      aria-hidden="true"
                    />
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        <main className="px-4 py-16 sm:px-6 lg:flex-auto lg:px-0 lg:py-20">
          <div className="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
            <Section title={t('user.profile')} helpText={t('user.profileHelp')} list={userData} />

            <ChangePasswordSection />

            <BraurekaSection />

            <SmartLifeSection />

            <TuyaSection />

            <PushSubscriptionSection />

            <DeleteAccountSection />
          </div>
        </main>
      </div>
    </>
  )
}
