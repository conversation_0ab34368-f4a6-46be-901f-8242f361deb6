import { zodResolver } from '@hookform/resolvers/zod'
import { ReactElement, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { generateRecipeFromBeerXml } from 'helpers/beerXml'
import { post, useHttpNotifications } from 'helpers/http'
import { parseBeerXml } from 'helpers/xml'
import { BeerXml } from 'types/beerXml'

import FileInput from 'components/Common/Form/FileInput'

export default function UploadForm(
  props: Readonly<{
    data?: unknown
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()
  const [beerXml, setBeerXml] = useState<BeerXml | null>(null)

  const { handleSubmit, setValue } = useForm({
    resolver: zodResolver(
      z.object({
        file: z.string(),
      })
    ),
  })

  const onUpload = async (file: File | null) => {
    if (!file) {
      return
    }

    if (file.type != 'text/xml') {
      throw new Error('Invalid file type')
    }

    const text = await file.text()

    setValue('file', text)
    setBeerXml(parseBeerXml(text))
  }

  const onSubmit = async (data: { file: string }) => {
    if (!beerXml) {
      throw new Error('No beerXml')
    }

    const recipe = await generateRecipeFromBeerXml(beerXml, true, data.file)

    await resolveResponse(async () => await post('recipe', recipe))

    props.onSubmit(true)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="">
        <p className="mt-2 text-sm leading-6 text-gray-600">{t('recipe.beerXmlDescription')}</p>
        <div className="mt-1">
          <FileInput name="recipeFile" onChange={onUpload} />
        </div>
      </div>

      <div className="border-b border-gray-900/10 pb-6"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.upload')}
        </button>
      </div>
    </form>
  )
}
