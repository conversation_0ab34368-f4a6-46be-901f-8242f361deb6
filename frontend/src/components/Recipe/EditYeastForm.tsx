import { RecipeYeastModel } from '@backendZod/recipeyeast'
import { zodResolver } from '@hookform/resolvers/zod'
import type { RecipeYeast, SearchIndex } from '@prisma/client'
import { ReactElement } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { get } from 'helpers/http'

import SearchInput from 'components/Common/Form/SearchInput'
import { NumberInput } from 'components/Common/Form/TextInput'

type CompleteRecipeYeast = z.infer<typeof RecipeYeastModel>

export default function EditYeastForm(
  props: Readonly<{
    data?: RecipeYeast
    onCancel: () => void
    onSubmit: (data: CompleteRecipeYeast) => void
  }>
): ReactElement {
  const { t } = useTranslation()

  const methods = useForm({
    resolver: zodResolver(RecipeYeastModel.omit({ id: true, recipeId: true })),
    defaultValues: props.data ?? {},
  })

  const onSubmit = data => {
    props.onSubmit({ ...data } as unknown as CompleteRecipeYeast)
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>
        <div className="space-y-12">
          <div className="mt-3">
            <SearchInput
              label={t('common.name')}
              onSelect={item => {
                methods.setValue('name', item.name)
              }}
              onChange={async value => {
                methods.setValue('name', value)
                const data = await get<SearchIndex[]>(`yeast/search?q=${value}`)
                return data
              }}
              defaultValue={props.data?.name ?? ''}
            />

            <NumberInput name="amount" className="mt-3" label={t('common.amount')} />
          </div>
        </div>

        <div className="border-b border-gray-900/10 pb-12"></div>

        <div className="mt-3 flex items-center justify-end gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-gray-900"
            onClick={() => props.onCancel()}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            {t('common.save')}
          </button>
        </div>
      </form>
    </FormProvider>
  )
}
