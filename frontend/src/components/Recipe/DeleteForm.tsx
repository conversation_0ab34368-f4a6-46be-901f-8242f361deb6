import type { <PERSON>ci<PERSON>, <PERSON><PERSON>peHop, RecipeYeast } from '@prisma/client'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'

import { del, useHttpNotifications } from 'helpers/http'

export default function DeleteForm(
  props: Readonly<{
    data?: Recipe
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()

  const onDelete = async () => {
    if (props.data !== undefined) {
      await resolveResponse(async () => await del(`recipe/${props.data.id}`))
    }

    props.onSubmit(true)
  }

  return (
    <div>
      <div className="">
        <p className="mt-2 text-sm leading-6 text-gray-600">{t('recipe.beerXmlDescription')}</p>
      </div>

      <div className="border-b border-gray-900/10 pb-6"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="button"
          onClick={() => onDelete()}
          className="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
        >
          {t('common.delete')}
        </button>
      </div>
    </div>
  )
}
