import { CheckCircleIcon } from '@heroicons/react/24/solid'
import { FormEventHandler, ReactElement, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { post } from 'helpers/http'
import { RecipeRequestResponse } from 'services/RecipeService'

import router from 'routes/router'

import Alert from 'components/Common/Alert'
import FileInput, { FileFormats } from 'components/Common/Form/FileInput'
import Loading from 'components/Common/Loading'

export default function UploadPdfForm(
  props: Readonly<{
    data?: unknown
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const { t } = useTranslation()
  const [file, setFile] = useState<File | null>(null)
  const [status, setStatus] = useState<'form' | 'loading' | 'error' | 'success'>('form')
  const [recipe, setRecipe] = useState<RecipeRequestResponse | null>(null)

  const onUpload = async (file: File | null) => {
    if (!file) {
      return
    }

    setFile(file)
  }

  const onClose = () => {
    props.onSubmit(true)
  }

  const onOpenRecipe = async () => {
    router.navigate(`/recipe/${recipe?.id}/view`)
    props.onSubmit(true)
  }

  const onSubmit: FormEventHandler<HTMLFormElement> = async e => {
    e.preventDefault()
    setStatus('loading')
    if (!file) {
      return
    }

    const data = new FormData()
    data.append('file', file)

    try {
      const response = await post<{ createdRecipe: RecipeRequestResponse }>(
        'file-to-recipe-detection',
        data
      )

      const recipe = await response.json()
      setRecipe(recipe.createdRecipe)
      setStatus('success')
    } catch (error) {
      setStatus('error')
    }
  }

  if (status === 'success') {
    return (
      <form onSubmit={onSubmit}>
        <div className="mt-3 flex items-start">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-6 w-6 text-green-400" aria-hidden="true" />
          </div>
          <div className="ml-3 w-0 flex-1 pt-0.5">
            <p className="text-sm font-medium text-stone-900">{t('recipe.recipeCreated')}</p>
          </div>
        </div>

        <div className="border-b border-gray-900/10 pb-6"></div>

        <div className="mt-3 flex items-center justify-end gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-gray-900"
            onClick={() => onClose()}
          >
            {t('common.close')}
          </button>
          <button
            onClick={() => onOpenRecipe()}
            className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            {t('recipe.openRecipe')}
          </button>
        </div>
      </form>
    )
  }

  return (
    <form onSubmit={onSubmit}>
      <div className="">
        <p className="mt-2 text-sm leading-6 text-gray-600">{t('recipe.uploadPdfDescription')}</p>
        {status === 'error' && <Alert style="error" text={t('common.errorOccured')} />}
        <div className="mt-1">
          <FileInput name="recipeFile" onChange={onUpload} accept={FileFormats.PDF} />
        </div>
      </div>

      <div className="border-b border-gray-900/10 pb-6"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        {status === 'error' && (
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-gray-900"
            onClick={() => props.onCancel()}
          >
            {t('common.close')}
          </button>
        )}
        {status === 'form' && (
          <>
            <button
              type="button"
              className="text-sm font-semibold leading-6 text-gray-900"
              onClick={() => props.onCancel()}
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              {t('common.upload')}
            </button>
          </>
        )}
        {status === 'loading' && <Loading />}
      </div>
    </form>
  )
}
