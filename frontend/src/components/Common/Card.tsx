import { memo, ReactElement } from 'react'
import { twMerge } from 'tailwind-merge'

import Button, { ButtonProps } from 'components/Common/Button'

const Card = memo(function Card({
  title,
  children,
  className,
  buttons,
}: {
  title: string
  children: ReactElement
  className?: string
  buttons?: ButtonProps[]
}): ReactElement {
  return (
    <div
      className={twMerge(
        'divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow',
        className
      )}
    >
      <div className="flex items-center px-4 py-5 sm:px-6">
        <span className="grow">{title}</span>
        {/* We use less vertical padding on card headers on desktop than on body sections */}
        {buttons && buttons.map((b, index) => <Button key={index} {...b} />)}
      </div>
      <div className="px-4 py-5 sm:p-6">{children}</div>
    </div>
  )
})

export default Card
