import { BellAlertIcon, XCircleIcon } from '@heroicons/react/20/solid'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import { ButtonProps } from 'components/Common/Button'

export enum AlertStyles {
  WARNING = 'warning',
  ERROR = 'error',
}

const AlertStyleColor: Record<AlertStyles, string> = {
  [AlertStyles.WARNING]: 'yellow',
  [AlertStyles.ERROR]: 'red',
}

const AlertStyleIcon: Record<AlertStyles, React.ElementType> = {
  [AlertStyles.WARNING]: BellAlertIcon,
  [AlertStyles.ERROR]: XCircleIcon,
}

type AlertProps = {
  title?: string
  text: string
  style: AlertStyles
  children?: React.ReactNode
}

export default function Alert(props: AlertProps) {
  const color = AlertStyleColor[props.style]
  const Icon = AlertStyleIcon[props.style]

  return (
    <div className={`rounded-md bg-${color}-50 p-4`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <Icon aria-hidden="true" className={`text-${color}-400 h-5 w-5`} />
        </div>
        <div className="ml-3">
          {props.title && (
            <h3 className={`text-${color}-800 text-sm font-medium`}>{props.title}</h3>
          )}
          <div className={twMerge(`text-sm text-${color}-700`, props.title && 'mt-2')}>
            <p>{props.text}</p>
          </div>
          {props.children}
        </div>
      </div>
    </div>
  )
}

export function AlertWithButton(
  props: AlertProps & { buttons: ButtonProps[]; shouldDismiss?: boolean }
) {
  const color = AlertStyleColor[props.style]
  const buttonStyles = `rounded-md bg-${color}-50 px-2 py-1.5 text-sm font-medium text-${color}-800 hover:bg-${color}-100 focus:outline-none focus:ring-2 focus:ring-${color}-600 focus:ring-offset-2 focus:ring-offset-${color}-50`

  if (props.shouldDismiss) {
    const { t } = useTranslation()
    const [dismissed, setDismissed] = useState(false)

    props.buttons.push({
      children: t('common.dismiss'),
      onClick: () => {
        setDismissed(true)
      },
    })

    if (dismissed) {
      return null
    }
  }

  return (
    <Alert {...props}>
      <div className="mt-4">
        <div className="-mx-2 -my-1.5 flex gap-3">
          {props.buttons.map((button, index) =>
            button.onClick ? (
              <button key={index} type="button" onClick={button.onClick} className={buttonStyles}>
                {props.children}
              </button>
            ) : (
              <Link key={index} to={button.to} className={buttonStyles}>
                {props.children}
              </Link>
            )
          )}
        </div>
      </div>
    </Alert>
  )
}
