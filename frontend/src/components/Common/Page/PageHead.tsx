import { BarsArrowUpIcon, ChevronDownIcon, MagnifyingGlassIcon } from '@heroicons/react/24/solid'
import { ReactElement } from 'react'

import <PERSON>ton, { ButtonProps } from '../Button'
import ContextMenu from '../ContextMenu'

function PageHeadWrapper(props: { children: ReactElement | ReactElement[] }) {
  return (
    <header className="bg-white shadow">
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">{props.children}</div>
    </header>
  )
}

export default function PageHead(
  props:
    | {
        title: string
      }
    | PageHeadWithButtonsProps
) {
  if (props?.buttons) {
    return <PageHeadWithButtons {...(props as PageHeadWithButtonsProps)} />
  }

  return (
    <PageHeadWrapper>
      <h1 className="text-3xl font-bold tracking-tight text-gray-900">{props.title}</h1>
    </PageHeadWrapper>
  )
}

type PageHeadWithButtonsProps = {
  title: string
  buttons: ButtonProps[]
  contextMenu?: ButtonProps[]
  onSearch?: (q: string) => void
}

export function PageHeadWithButtons(props: Readonly<PageHeadWithButtonsProps>) {
  return (
    <PageHeadWrapper>
      <div className="md:flex md:items-center md:justify-between">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            {props.title}
          </h2>
        </div>
        {props.onSearch !== undefined && (
          <div className="flex rounded-md shadow-sm">
            <div className="relative flex-grow focus-within:z-10">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                name="mobile-search-candidate"
                id="mobile-search-candidate"
                className="block w-full rounded-none rounded-l-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:hidden"
                placeholder="Search"
                onChange={e => props.onSearch?.(e.target.value)}
              />
              <input
                type="text"
                name="desktop-search-candidate"
                id="desktop-search-candidate"
                className="hidden w-full rounded-none rounded-l-md border-0 py-1.5 pl-10 text-sm leading-6 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:block"
                placeholder="Search"
                onChange={e => props.onSearch?.(e.target.value)}
              />
            </div>
            <button
              type="button"
              className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
              <BarsArrowUpIcon className="-ml-0.5 h-5 w-5 text-gray-400" aria-hidden="true" />
              Sort
              <ChevronDownIcon className="-mr-1 h-5 w-5 text-gray-400" aria-hidden="true" />
            </button>
          </div>
        )}
        <div className="mt-4 flex items-center gap-3 md:ml-4 md:mt-0">
          {props.buttons.map((button, index) => (
            <Button key={index} {...button} className="inline-flex items-center" />
          ))}
          {props.contextMenu && <ContextMenu buttons={props.contextMenu} />}
        </div>
      </div>
    </PageHeadWrapper>
  )
}
