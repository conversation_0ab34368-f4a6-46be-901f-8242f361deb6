import { ReactElement } from 'react'
import { Link } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

type CommonButtonProps = {
  children: string | ReactElement | (string | ReactElement)[]
  className?: string
  onClick?: (e: unknown) => void
  size?: keyof typeof ButtonSizes
  style?: keyof typeof ButtonStyles
  type?: 'button' | 'submit' | 'reset'
  icon?: boolean
}

type LinkButtonProps = Omit<CommonButtonProps, 'type' | 'onClick'> & { to: string }

export type ButtonProps = LinkButtonProps | CommonButtonProps

const ButtonSizes = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-2 py-1 text-sm',
  md: 'px-2.5 py-1.5 text-sm',
  lg: 'px-3 py-2 text-sm',
  xl: 'px-3.5 py-2.5 text-base',
}

const ButtonStyles = {
  primary: 'bg-indigo-600 hover:bg-indigo-500 focus-visible:ring-indigo-600 text-white',
  secondary: 'bg-white ring-1 ring-inset ring-gray-300 text-gray-900 hover:bg-gray-50',
  danger: 'bg-red-500 font-semibold text-white hover:bg-red-400',
}

const getClassForButtonSize = (props: Pick<ButtonProps, 'size' | 'style' | 'className' | 'icon'>) =>
  twMerge([
    'rounded-md font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2',
    ButtonSizes[props.size ?? 'lg'],
    ButtonStyles[props.style ?? 'primary'],
    props.icon ? 'inline-flex items-center gap-x-1.5' : '',
    props.className,
  ])

export function LinkButton(props: LinkButtonProps) {
  return (
    <Link className={getClassForButtonSize(props)} to={props.to}>
      {props.children}
    </Link>
  )
}

function CommonButton(props: CommonButtonProps) {
  return (
    <button
      type={props.type ?? 'button'}
      className={getClassForButtonSize(props)}
      onClick={props.onClick}
    >
      {props.children}
    </button>
  )
}

export default function Button(props: ButtonProps) {
  if ('to' in props) {
    return <LinkButton {...props} />
  }

  return (
    <CommonButton
      className={twMerge(['inline-flex items-center gap-x-1.5', props.className])}
      {...props}
    />
  )
}

export function ButtonWithIcon(props: ButtonProps) {
  return <Button {...props} icon={true} />
}
