export type StatsItem = { name: string; stat: string; help?: string }

export default function Stats(props: { stats: StatsItem[] }): React.ReactElement {
  return (
    <div>
      <dl className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3">
        {props.stats.map(item => (
          <div
            key={item.name}
            className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
          >
            <dt className="truncate text-sm font-medium text-gray-500">{item.name}</dt>
            <dd className="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
              {item.stat}
            </dd>
            {item.help && (
              <dd className="mt-1 text-sm font-semibold tracking-tight text-gray-500">
                {item.help}
              </dd>
            )}
          </div>
        ))}
      </dl>
    </div>
  )
}
