import { HTMLAttributes, ReactElement } from 'react'
import { twMerge } from 'tailwind-merge'

import { Label } from 'components/Common/Form/Common'

export enum FileFormats {
  PDF = 'application/pdf',
  XML = 'text/xml',
}

export default function FileInput(
  props: HTMLAttributes<HTMLDivElement> & {
    name: string
    label?: string
    onChange: (file: File | null) => void
    accept?: FileFormats | FileFormats[]
  }
): ReactElement {
  const { name, label, onChange, ...divProps } = props

  const hasError = false

  return (
    <div {...divProps}>
      {label && <Label for={name}>{label}</Label>}
      <div className="relative mt-2 rounded-md shadow-sm">
        <input
          id={name}
          type="file"
          onChange={e => {
            if (e.target.files) {
              onChange(e.target.files.item(0))
            }
          }}
          accept={Array.isArray(props.accept) ? props.accept.join(',') : props.accept}
          className={twMerge(
            'block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6',
            hasError &&
              'pr-10 text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500'
          )}
        />
      </div>
    </div>
  )
}
