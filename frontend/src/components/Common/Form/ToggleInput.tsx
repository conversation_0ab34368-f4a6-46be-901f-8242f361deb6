import { useFormContext, useWatch } from 'react-hook-form'
import { twMerge } from 'tailwind-merge'

export default function ToggleInput({
  name,
  label,
  className,
  disabled,
}: {
  name: string
  label: string
  help?: string
  className?: string
  disabled?: boolean
}) {
  const { register, setValue, control } = useFormContext()
  const value = useWatch({ name: name, control })

  return (
    <div className={className}>
      <label
        htmlFor={name}
        className="m-1 block text-sm font-medium text-gray-900 hover:text-indigo-900"
      >
        {label}
      </label>
      <input type="hidden" {...register(name, { setValueAs: v => Bo<PERSON><PERSON>(v) })} />
      <button
        type="button"
        id={name}
        className={twMerge(
          value ? 'bg-indigo-600 disabled:bg-indigo-400' : 'bg-gray-200 disabled:bg-gray-50',
          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2 disabled:cursor-default  disabled:text-gray-500 disabled:ring-1  disabled:ring-gray-300'
        )}
        role="switch"
        aria-checked={!!value}
        aria-labelledby={label}
        onClick={() => {
          setValue(name, !value, { shouldDirty: true })
        }}
        disabled={disabled}
      >
        <span
          aria-hidden="true"
          className={twMerge(
            value ? 'translate-x-5' : 'translate-x-0',
            'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out'
          )}
        />
      </button>
    </div>
  )
}
