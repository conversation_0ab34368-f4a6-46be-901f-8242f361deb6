import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { render } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'

import { NumberInput } from 'components/Common/Form/TextInput'

const TestForm = (props: {
  children: JSX.Element
  onSave: (data: unknown) => void
  data?: unknown
}) => {
  const methods = useForm({
    resolver: zodResolver(
      z.object({
        age: z.number(),
      })
    ),
    defaultValues: props.data ?? {},
  })

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(data => props.onSave(data))}>
        {props.children}
        <button type="submit" data-testid="btnSubmit">
          Submit
        </button>
      </form>
    </FormProvider>
  )
}

describe('NumberInput', () => {
  it('should render', async () => {
    const fn = vi.fn()

    const value = 10.5
    const input = '10,5'

    const user = userEvent.setup()

    const { getByTitle, getByTestId } = render(
      <TestForm onSave={fn}>
        <NumberInput name="age" label="Numberfeld" />
      </TestForm>
    )

    const $input = getByTitle('age') as HTMLInputElement

    await user.click($input)
    await user.keyboard(input)

    await user.click(getByTestId('btnSubmit'))

    expect(fn).toHaveBeenCalledWith({ age: value })
  })
})
