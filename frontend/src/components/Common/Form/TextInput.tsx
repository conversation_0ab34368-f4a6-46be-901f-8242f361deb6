import { ExclamationCircleIcon } from '@heroicons/react/24/solid'
import { HTMLAttributes, InputHTMLAttributes, ReactElement } from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import { Label } from 'components/Common/Form/Common'

function PlainInput(
  props: {
    name: string
    label: string
    inputProps: InputHTMLAttributes<HTMLInputElement>
  } & HTMLAttributes<HTMLDivElement>
): ReactElement {
  const { t } = useTranslation()

  const { name, label, inputProps, ...divProps } = props

  const {
    formState: { errors },
  } = useFormContext()

  const hasError = errors && errors[name]
  return (
    <div {...divProps}>
      <Label for={name}>{label}</Label>
      <div className="relative mt-2 rounded-md shadow-sm">
        <input
          id={name}
          className={twMerge(
            'block w-full rounded-md border-0 px-2 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6',
            hasError &&
              'pr-10 text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500'
          )}
          {...inputProps}
        />
        {hasError && (
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500" aria-hidden="true" />
          </div>
        )}
      </div>
      {hasError && (
        <p className="mt-2 text-sm text-red-600" id="email-error">
          <span>{(errors[name]?.message as string) ?? t('common.errorOccured')}</span>
        </p>
      )}
    </div>
  )
}

export default function TextInput(
  props: {
    name: string
    label: string
    type?: 'text' | 'password'
    placeholder?: string
  } & HTMLAttributes<HTMLDivElement>
): ReactElement {
  const { register } = useFormContext()

  const divProps = { ...props, type: undefined }

  return (
    <PlainInput
      inputProps={{
        placeholder: props.placeholder ?? undefined,
        ...register(props.name),
        type: props.type ?? 'text',
      }}
      {...divProps}
    />
  )
}

export function NumberInput(
  props: { name: string; label: string } & HTMLAttributes<HTMLDivElement>
): ReactElement {
  const { register } = useFormContext()

  return (
    <PlainInput
      inputProps={{
        ...register(props.name, {
          setValueAs: v => (typeof v === 'string' ? Number(v.replace(',', '.')) : v),
        }),
        type: 'text',
        title: props.name,
      }}
      {...props}
    />
  )
}
