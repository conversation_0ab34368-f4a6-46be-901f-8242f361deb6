import { Combobox } from '@headlessui/react'
import { MagnifyingGlassIcon } from '@heroicons/react/24/solid'
import type { SearchIndex } from '@prisma/client'
import { ChangeEvent, ReactElement, useState } from 'react'
import { twMerge } from 'tailwind-merge'

import { getTypeById } from 'hooks/useInventoryTypes'

export default function SearchInput(props: {
  label: string
  onSelect: (data: SearchIndex) => void
  onChange: (value: string) => Promise<SearchIndex[]>
  onDisplayValue?: (value: string) => string
  defaultValue?: string
}): ReactElement {
  const [data, setData] = useState<SearchIndex[]>([])
  const [value, setValue] = useState<string>(props.defaultValue ?? '')

  const onSelect = (data: SearchIndex) => {
    setValue(data.name)
    props.onSelect(data)
  }

  const onChange = async (event: ChangeEvent<HTMLInputElement>) => {
    setValue(event.target.value)
    const data = await props.onChange(event.target.value)
    setData(data ?? [])
  }

  return (
    <Combobox as="div" value={value} onChange={onSelect} className="mt-3">
      <Combobox.Label className="block text-sm font-medium leading-6 text-gray-900">
        {props.label}
      </Combobox.Label>
      <div className="relative mt-2">
        <Combobox.Input
          className="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          onChange={onChange}
          displayValue={(value: string) => {
            return value
          }}
        />
        <Combobox.Button className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </Combobox.Button>

        {data.length > 0 && (
          <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
            {data.map(item => (
              <Combobox.Option
                key={item.id}
                value={item}
                className={({ active }) =>
                  twMerge(
                    'relative cursor-default select-none py-2 pl-3 pr-9',
                    active ? 'bg-indigo-600 text-white' : 'text-gray-900'
                  )
                }
              >
                <div className="flex items-center">
                  <div className="h-5 w-5 flex-shrink-0 rounded-full">
                    {getTypeById(item.type).icon}
                  </div>
                  <span className="ml-3 truncate">{item.name}</span>
                </div>
              </Combobox.Option>
            ))}
          </Combobox.Options>
        )}
      </div>
    </Combobox>
  )
}
