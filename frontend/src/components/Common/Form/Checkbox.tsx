import { useFormContext } from 'react-hook-form'
import { twMerge } from 'tailwind-merge'

type CheckboxProps = {
  name: string
  label: string
  className?: string
}

export default function Checkbox({ name, label, className }: CheckboxProps) {
  const { register } = useFormContext()

  return (
    <div className={twMerge('flex items-center', className)}>
      <input
        type="checkbox"
        id={name}
        {...register(name)}
        className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
      />
      <label htmlFor={name} className="ml-3 block text-sm leading-6 text-gray-900">
        {label}
      </label>
    </div>
  )
}
