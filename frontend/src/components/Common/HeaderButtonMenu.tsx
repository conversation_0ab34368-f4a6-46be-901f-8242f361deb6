import { Menu, Transition } from '@headlessui/react'
import { EllipsisVerticalIcon } from '@heroicons/react/20/solid'
import { Fragment } from 'react'
import { twMerge } from 'tailwind-merge'

import Button, { ButtonProps } from 'components/Common/Button'

export default function HeaderButtonMenu(props: { main: ButtonProps; items: ButtonProps[] }) {
  return (
    <div className="flex items-center gap-x-4 sm:gap-x-6">
      {props.items.map((button, index) =>
        button.to !== undefined ? (
          <a
            key={button.to}
            href={button.to}
            className="hidden text-sm font-semibold leading-6 text-gray-900 sm:block"
          >
            {button.children}
          </a>
        ) : (
          <button
            key={index}
            type="button"
            onClick={button.onClick}
            className="hidden text-sm font-semibold leading-6 text-gray-900 sm:block"
          >
            {button.children}
          </button>
        )
      )}
      <Button {...props.main} />

      <Menu as="div" className="relative sm:hidden">
        <Menu.Button className="-m-3 block p-3">
          <span className="sr-only">More</span>
          <EllipsisVerticalIcon className="h-5 w-5 text-gray-500" aria-hidden="true" />
        </Menu.Button>

        <Transition
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <Menu.Items className="absolute right-0 z-10 mt-0.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
            {props.items.map((button, index) =>
              button.to ? (
                <Menu.Item key={button.to}>
                  {({ active }) => (
                    <a
                      href={button.to}
                      className={twMerge(
                        active ? 'bg-gray-50' : '',
                        'block px-3 py-1 text-sm leading-6 text-gray-900'
                      )}
                    >
                      {button.children}
                    </a>
                  )}
                </Menu.Item>
              ) : (
                <Menu.Item key={index}>
                  {({ active }) => (
                    <button
                      type="button"
                      onClick={button.onClick}
                      className={twMerge(
                        active ? 'bg-gray-50' : '',
                        'block w-full px-3 py-1 text-left text-sm leading-6 text-gray-900'
                      )}
                    >
                      {button.children}
                    </button>
                  )}
                </Menu.Item>
              )
            )}
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  )
}
