import { CheckCircleIcon } from '@heroicons/react/24/solid'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'

const HiddenCard = memo(function HiddenCard({
  title,
  onOpen,
}: {
  title: string
  onOpen: () => void
}) {
  const { t } = useTranslation()

  return (
    <div className="divide-y divide-gray-200 overflow-hidden rounded-lg bg-white opacity-60 shadow">
      <div className="flex items-center px-4 py-5 sm:px-6">
        <span className="flex grow items-center">
          <CheckCircleIcon className="mr-3 h-5 w-5 text-green-600" />
          {title}
        </span>
        <div>
          <button
            onClick={onOpen}
            className="text-sm font-medium text-gray-500 hover:text-gray-700"
          >
            {t('common.open')}
          </button>
        </div>
      </div>
    </div>
  )
})

export default HiddenCard
