import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'

export default function DeleteForm(
  props: Readonly<{
    data?: unknown
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const { t } = useTranslation()

  const onDelete = async () => {
    if (props.data !== undefined) {
      props.onSubmit(props.data)
    } else {
      props.onCancel()
    }
  }

  return (
    <div>
      <div className="">
        <p className="mt-2 text-sm leading-6 text-gray-600">{t('common.deleteDescription')}</p>
      </div>

      <div className="border-b border-gray-900/10 pb-6"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="button"
          onClick={() => onDelete()}
          className="rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
        >
          {t('common.delete')}
        </button>
      </div>
    </div>
  )
}
