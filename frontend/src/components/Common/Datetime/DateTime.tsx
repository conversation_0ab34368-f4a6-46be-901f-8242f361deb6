import { memo, useState } from 'react'
import { useTranslation } from 'react-i18next'

const Datetime = memo(function Datetime(date: Date | string) {
  const { t } = useTranslation()
  const [data, setData] = useState(typeof date === 'string' ? new Date(date) : date)

  return (
    <time dateTime={data.toISOString()}>{t('common.createdAt', { date: data, time: null })}</time>
  )
})

export default Datetime
