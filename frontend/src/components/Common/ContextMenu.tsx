import { Menu, Transition } from '@headlessui/react'
import { EllipsisHorizontalIcon } from '@heroicons/react/20/solid'
import { Fragment } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import { ButtonProps } from 'components/Common/Button'

export default function ContextMenu(props: { buttons: ButtonProps[] }) {
  const { t } = useTranslation()

  return (
    <Menu as="div" className="relative z-10 ml-auto">
      <Menu.Button className="-m-2.5 block p-2.5 text-gray-400 hover:text-gray-500">
        <span className="sr-only">{t('common.openContextMenu')}</span>
        <EllipsisHorizontalIcon className="h-5 w-5" aria-hidden="true" />
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 z-20 mt-0.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
          {props.buttons &&
            props.buttons.map((button, index) => (
              <Menu.Item key={index}>
                {({ active }) =>
                  button.onClick ? (
                    <button
                      onClick={button.onClick}
                      className={twMerge(
                        active ? 'bg-gray-50' : '',
                        'block px-3 py-1 text-sm leading-6 text-gray-900'
                      )}
                    >
                      {button.children}
                    </button>
                  ) : (
                    <Link
                      to={button.to}
                      className={twMerge(
                        active ? 'bg-gray-50' : '',
                        'block px-3 py-1 text-sm leading-6 text-gray-900'
                      )}
                    >
                      {button.children}
                    </Link>
                  )
                }
              </Menu.Item>
            ))}
        </Menu.Items>
      </Transition>
    </Menu>
  )
}
