import { ReactElement, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import useAuthenticationStore from 'stores/AuthenticationStore'
import useIntegrationStore from 'stores/IntegrationStore'

export default function AuthenticationGuard({ children }: { children: ReactElement }) {
  const isAuthenticated = useAuthenticationStore(state => state.isAuthenticated)

  useEffect(() => {
    if (isAuthenticated) {
      useIntegrationStore.getState().loadIntregration()
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return <Navigate to="/login" replace={true} />
  }

  return <>{children}</>
}
