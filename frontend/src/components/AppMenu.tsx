import { Disclosure, Menu, Transition } from '@headlessui/react'
import { Bars3Icon, BellIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { ServerStackIcon } from '@heroicons/react/24/solid'
import { iconUrl } from 'constants'
import { BeerIcon, ListIcon, PackageIcon } from 'lucide-react'
import { Fragment, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Link, Outlet, useLocation } from 'react-router-dom'
import useAuthenticationStore, { User } from 'stores/AuthenticationStore'
import { twMerge } from 'tailwind-merge'

import useEditDialog from 'hooks/Dialog/useEditDialog'
import useUser from 'hooks/useUser'

import SearchDevicesForm from 'components/Device/SearchDevicesForm'
import NotificationPopUp from 'components/Notification/NotificationPopUp'

const toHex = (chars: string) => {
  let hash = 0
  for (let i = 0; i < chars.length; i++) {
    hash = chars.charCodeAt(i) + ((hash << 5) - hash)
    hash = hash & hash
  }
  let color = '#'
  for (let i = 0; i < 3; i++) {
    const value = (hash >> (i * 8)) & 255
    color += ('00' + value.toString(16)).substr(-2)
  }
  return color
}

const getUserInitials = (user: User) =>
  user.name
    .split(' ')
    .map(n => n[0].toUpperCase())
    .join('')

export default function AppMenu() {
  const { t } = useTranslation()
  const location = useLocation()

  const user = useUser()

  const { openDialog: openSearchDevices } = useEditDialog(
    t('device.searchDevices'),
    SearchDevicesForm,
    async (data, initItem) => {
      console.log(data, initItem)
    }
  )

  const navigation = useMemo(() => {
    const navigation = [
      { name: t('navigation.dashboard'), route: '/overview', current: false },
      { name: t('navigation.fermentation'), route: '/fermentation', current: false },
      { name: t('navigation.brewlog'), route: '/brewlog', current: false },
      { name: t('navigation.inventory'), route: '/inventory', current: false },
      { name: t('navigation.recipe'), route: '/recipe', current: false },
      { name: t('navigation.device'), route: '/device', current: false },
    ]

    const current = navigation.find(item => item.route === location.pathname)
    if (current) {
      current.current = true
    }

    return navigation
  }, [location])

  const logout = () => {
    useAuthenticationStore.getState().logout()
  }

  const userNavigation = useMemo(
    () => [
      { name: t('navigation.userSettings'), href: '/user/settings' },
      { name: t('navigation.logout'), onClick: logout },
    ],
    []
  )

  const onNavigate = (view: string) => {}

  const currentView = 'test'

  return (
    <div className="min-h-full">
      <Disclosure as="nav" className="bg-gray-800">
        {({ open }) => (
          <>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="flex h-16 items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <img className="h-10 w-10" src={iconUrl} alt="Your Company" />
                  </div>
                  <div className="hidden md:block">
                    <div className="ml-10 flex items-baseline space-x-4">
                      {navigation.map(item => (
                        <Link
                          key={item.name}
                          to={item.route}
                          className={twMerge(
                            item.current
                              ? 'bg-gray-900 text-white'
                              : 'text-gray-300 hover:bg-gray-700 hover:text-white',
                            'rounded-md px-3 py-2 text-sm font-medium'
                          )}
                          aria-current={item.current ? 'page' : undefined}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="hidden md:block">
                  <div className="ml-4 flex items-center md:ml-6">
                    <button
                      onClick={() => openSearchDevices()}
                      type="button"
                      className="relative mr-2 rounded-full bg-gray-800 p-1 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
                    >
                      <span className="absolute -inset-0.5" />
                      <span className="sr-only">{t('device.searchDevices')}</span>
                      <ServerStackIcon className="block h-6 w-6" aria-hidden="true" />
                    </button>

                    <NotificationPopUp />

                    {/* Profile dropdown */}
                    <Menu as="div" className="relative ml-3">
                      <div>
                        <Menu.Button className="relative flex max-w-xs items-center rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                          <span className="absolute -inset-1.5" />
                          <span className="sr-only">Open user menu</span>
                          {user.imageUrl ? (
                            <img className="h-8 w-8 rounded-full" src={user.imageUrl} alt="" />
                          ) : (
                            <div
                              style={{ backgroundColor: toHex(user.name) }}
                              className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-white"
                            >
                              {getUserInitials(user)}
                            </div>
                          )}
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                          {userNavigation.map(item => (
                            <Menu.Item key={item.name}>
                              {({ active }) =>
                                item.href ? (
                                  <Link
                                    to={item.href}
                                    className={twMerge(
                                      active ? 'bg-gray-100' : '',
                                      'block px-4 py-2 text-sm text-gray-700'
                                    )}
                                  >
                                    {item.name}
                                  </Link>
                                ) : (
                                  <button
                                    onClick={item.onClick}
                                    className={twMerge(
                                      active ? 'bg-gray-100' : '',
                                      'block px-4 py-2 text-sm text-gray-700'
                                    )}
                                  >
                                    {item.name}
                                  </button>
                                )
                              }
                            </Menu.Item>
                          ))}
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  </div>
                </div>
                <div className="-mr-2 flex md:hidden">
                  {/* Mobile menu button */}
                  <button
                    onClick={() => openSearchDevices()}
                    className="relative mr-3 inline-flex items-center justify-center rounded-md bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
                  >
                    <span className="absolute -inset-0.5" />
                    <span className="sr-only">Search devices</span>
                    <ServerStackIcon className="block h-6 w-6" aria-hidden="true" />
                  </button>
                  <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md bg-gray-800 p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
                    <span className="absolute -inset-0.5" />
                    <span className="sr-only">Open main menu</span>
                    {open ? (
                      <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                    ) : (
                      <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                    )}
                  </Disclosure.Button>
                </div>
              </div>
            </div>

            <Disclosure.Panel className="md:hidden">
              <div className="space-y-1 px-2 pb-3 pt-2 sm:px-3">
                {navigation.map(item => (
                  <Disclosure.Button
                    key={item.name}
                    as={Link}
                    to={item.route}
                    className={twMerge(
                      item.current
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white',
                      'block rounded-md px-3 py-2 text-base font-medium'
                    )}
                    aria-current={item.current ? 'page' : undefined}
                  >
                    {item.name}
                  </Disclosure.Button>
                ))}
              </div>
              <div className="border-t border-gray-700 pb-3 pt-4">
                <div className="flex items-center px-5">
                  <div className="flex-shrink-0">
                    <img className="h-10 w-10 rounded-full" src={user.imageUrl} alt="" />
                  </div>
                  <div className="ml-3">
                    <div className="text-base font-medium leading-none text-white">{user.name}</div>
                    <div className="text-sm font-medium leading-none text-gray-400">
                      {user.email}
                    </div>
                  </div>
                  <button
                    type="button"
                    className="relative ml-auto flex-shrink-0 rounded-full bg-gray-800 p-1 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
                  >
                    <span className="absolute -inset-1.5" />
                    <span className="sr-only">View notifications</span>
                    <BellIcon className="h-6 w-6" aria-hidden="true" />
                    <span className="absolute bottom-1 right-1 block size-1.5 rounded-full bg-gray-300 ring-2 ring-white" />
                  </button>
                </div>
                <div className="mt-3 space-y-1 px-2">
                  {userNavigation.map(item =>
                    item.href ? (
                      <Disclosure.Button
                        key={item.name}
                        as="a"
                        href={item.href}
                        className="block rounded-md px-3 py-2 text-base font-medium text-gray-400 hover:bg-gray-700 hover:text-white"
                      >
                        {item.name}
                      </Disclosure.Button>
                    ) : (
                      <Disclosure.Button
                        key={item.name}
                        as="button"
                        type="button"
                        onClick={item.onClick}
                        className="block rounded-md px-3 py-2 text-base font-medium text-gray-400 hover:bg-gray-700 hover:text-white"
                      >
                        {item.name}
                      </Disclosure.Button>
                    )
                  )}
                </div>
              </div>
            </Disclosure.Panel>
          </>
        )}
      </Disclosure>

      <main>
        <Outlet />
      </main>
      <div className="visible md:invisible">
        <footer className="fixed bottom-0 w-full border-t border-gray-200 bg-white py-2">
          <div className="container mx-auto flex justify-center">
            <nav className="flex w-full max-w-md justify-around">
              <button
                onClick={() => onNavigate('recipes')}
                className={`flex flex-col items-center p-2 ${currentView === 'recipes' ? 'text-amber-600' : 'text-gray-600 hover:text-gray-900'}`}
              >
                <ListIcon size={24} />
                <span className="mt-1 text-xs">Recipes</span>
              </button>
              <button
                onClick={() => onNavigate('brewing')}
                className={`flex flex-col items-center p-2 ${currentView === 'brewing' ? 'text-amber-600' : 'text-gray-600 hover:text-gray-900'}`}
                disabled={currentView === 'brewing'}
              >
                <BeerIcon size={24} />
                <span className="mt-1 text-xs">Brewing</span>
              </button>
              <button
                onClick={() => onNavigate('inventory')}
                className={`flex flex-col items-center p-2 ${currentView === 'inventory' ? 'text-amber-600' : 'text-gray-600 hover:text-gray-900'}`}
              >
                <PackageIcon size={24} />
                <span className="mt-1 text-xs">Inventory</span>
              </button>
            </nav>
          </div>
        </footer>
        {/* Add padding to account for fixed footer */}
        <div className="h-20"></div>
      </div>
    </div>
  )
}
