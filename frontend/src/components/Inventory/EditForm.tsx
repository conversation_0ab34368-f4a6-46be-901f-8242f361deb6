import { InventaryItemModel } from '@backendZod/inventaryitem'
import { zodResolver } from '@hookform/resolvers/zod'
import type { InventaryItem } from '@prisma/client'
import { ReactElement, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import types, { InventoryTypeDefinition } from 'hooks/useInventoryTypes'

import TextInput, { NumberInput } from 'components/Common/Form/TextInput'
import TitleSearchInput from 'components/Inventory/TitleSearchInput'
import TypeDropdownInput from 'components/Inventory/TypeDropdownInput'

type CompleteInventaryItem = z.infer<typeof InventaryItemModel>

export default function EditForm(
  props: Readonly<{
    data?: InventaryItem
    onCancel: () => void
    onSubmit: (data: CompleteInventaryItem) => void
  }>
): ReactElement {
  const { t } = useTranslation()

  const [selectedType, setSelectedType] = useState<InventoryTypeDefinition | null>(
    types?.find(t => t.id == props.data?.type) ?? null
  )

  const methods = useForm({
    resolver: zodResolver(InventaryItemModel.omit({ id: true, createdAt: true, extraData: true })),
    defaultValues: props.data ?? {},
  })

  const getZodValidation = () => {
    if (selectedType == null) {
      return z.undefined()
    }

    const fields = Object.fromEntries(
      selectedType.extraFields?.map(field => [
        field.name,
        field.type === 'number' ? z.number() : z.string(),
      ]) ?? []
    )

    return z.object(fields)
  }

  const extraFieldsMethods = useForm({
    resolver: zodResolver(getZodValidation()),
    defaultValues: (props.data?.extraData as unknown) ?? {},
  })

  const handleSubmitForForms = e => {
    if (selectedType?.extraFields === undefined) {
      methods.handleSubmit(data => {
        props.onSubmit({ ...data, extraData: {} } as unknown as CompleteInventaryItem)
      })(e)
      return
    }

    Promise.all([
      new Promise((resolve, reject) => methods.handleSubmit(resolve, reject)(e)),
      new Promise((resolve, reject) => extraFieldsMethods.handleSubmit(resolve, reject)(e)),
    ]).then(res => {
      props.onSubmit({
        ...(res[0] as CompleteInventaryItem),
        extraData: res[1],
      } as unknown as CompleteInventaryItem)
    })
  }

  const valueType = methods.watch('type')
  const onWatch = () => {
    if (valueType === selectedType?.id) {
      return
    }

    if (!valueType) {
      setSelectedType(null)
      return
    }

    const type = types.find(t => t.id === valueType)
    setSelectedType(type ?? null)

    const defaultUnit = methods.watch('unit')
    if (type != null && (defaultUnit == '' || defaultUnit == null)) {
      methods.setValue('unit', type.defaultUnit)
    }
  }
  onWatch()

  return (
    <form onSubmit={handleSubmitForForms}>
      <FormProvider {...methods}>
        <div className="space-y-12">
          <div className="mt-3">
            <TitleSearchInput />

            <NumberInput name="amount" className="mt-3" label={t('inventory.amount')} />

            <TextInput name="unit" className="mt-3" label={t('inventory.unit')} />

            <TypeDropdownInput />
          </div>
        </div>
      </FormProvider>

      {selectedType !== null && selectedType.extraFields !== undefined && (
        <FormProvider {...extraFieldsMethods}>
          <div className="space-y-12">
            {selectedType.extraFields.map(field => {
              switch (field.type) {
                case 'number':
                  return (
                    <NumberInput
                      className="mt-3"
                      key={field.name}
                      name={field.name}
                      label={field.label}
                    />
                  )
                case 'text':
                  return (
                    <TextInput
                      className="mt-3"
                      key={field.name}
                      name={field.name}
                      label={field.label}
                    />
                  )
              }
            })}
          </div>
        </FormProvider>
      )}

      <div className="border-b border-gray-900/10 pb-12"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.save')}
        </button>
      </div>
    </form>
  )
}
