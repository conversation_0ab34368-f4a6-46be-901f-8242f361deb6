import { Listbox } from '@headlessui/react'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/react/24/solid'
import { ReactElement } from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import types from 'hooks/useInventoryTypes'

export default function TypeDropdownInput(): ReactElement {
  const { t } = useTranslation()

  const { watch, setValue, getValues } = useFormContext()

  const type = watch('type')

  return (
    <Listbox
      as="div"
      className="mt-3"
      value={type}
      onChange={(value: string) => {
        setValue('type', value)
      }}
    >
      <Listbox.Label className="block text-sm font-medium leading-6 text-gray-900">
        {t('inventory.type')}
      </Listbox.Label>
      <div className="relative mt-2">
        <Listbox.Button className="w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
          {types.find(t => t.id === getValues().type)?.name ?? ' - '}
          <div className="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
            <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          </div>
        </Listbox.Button>

        <Listbox.Options className="absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
          {types?.map(entry => (
            <Listbox.Option
              key={`${entry.id}`}
              value={entry.id}
              className={({ active }) =>
                twMerge(
                  'relative cursor-default select-none py-2 pl-3 pr-9',
                  active ? 'bg-indigo-600 text-white' : 'text-gray-900'
                )
              }
            >
              {({ active, selected }) => (
                <>
                  <span className={twMerge('block truncate', selected && 'font-semibold')}>
                    {entry.name}
                  </span>

                  {selected && (
                    <span
                      className={twMerge(
                        'absolute inset-y-0 right-0 flex items-center pr-4',
                        active ? 'text-white' : 'text-indigo-600'
                      )}
                    >
                      <CheckIcon className="h-5 w-5" aria-hidden="true" />
                    </span>
                  )}
                </>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </div>
    </Listbox>
  )
}
