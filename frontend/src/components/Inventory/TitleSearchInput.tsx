import type { SearchIndex } from '@prisma/client'
import { ReactElement } from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { get } from 'helpers/http'

import SearchInput from 'components/Common/Form/SearchInput'

export default function TitleSearchInput(): ReactElement {
  const { t } = useTranslation()

  const { watch, setValue } = useFormContext()

  const name: string = watch('name')

  const onSelect = (data: SearchIndex) => {
    setValue('name', data.name)
    setValue('type', data.type)
  }

  const onChange = async (value: string) => {
    setValue('name', value)

    const data = await get<SearchIndex[]>(`items/search?q=${value}`)
    return data
  }

  return (
    <SearchInput
      label={t('inventory.nameLabel')}
      onSelect={onSelect}
      onChange={onChange}
      defaultValue={name}
    />
  )
}
