import type { InventaryItem, BrewingHop } from '@prisma/client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { get } from 'helpers/http'

export default function HopsTable(
  props: Readonly<{ hops: BrewingHop[]; searchInventory?: boolean }>
) {
  const { t } = useTranslation()
  const [hops, setHops] = useState<(BrewingHop & { inventoryAmount: string })[]>([])
  const [view, setView] = useState<'time' | 'amount'>('amount')

  const showSearchInventory = props.searchInventory !== false

  const loadAmountHops = async () => {
    if (!props.hops) {
      setHops([])
    }

    const response = []
    for (const hop of props.hops) {
      const index = response.findIndex(item => item.name === hop.name)
      if (index === -1) {
        response.push({ ...hop, inventoryAmount: '' })
      } else {
        response[index].amount += hop.amount
      }
    }

    if (showSearchInventory) {
      for (const hop of response) {
        const data = await get<InventaryItem[]>(`inventory/search?q=${hop.name}&type=hop`)
        if (data && data.length > 0) {
          hop.inventoryAmount = Number(data[0].amount) + ' ' + data[0].unit
        }
      }
    }

    setHops(response)
  }

  useEffect(() => {
    if (!props.hops) {
      setHops([])
    } else if (view === 'time') {
      const response = [...props.hops].sort((a, b) => b.time - a.time)
      setHops(response)
    } else {
      loadAmountHops()
    }
  }, [props.hops, view])

  const showReadableAmount = (amount: number) => {
    if (!amount) {
      return ''
    } else if (amount.toFixed() == '0') {
      return (amount * 1000).toFixed(2) + ' g'
    }

    return amount + ' kg'
  }

  const showReadableBoilTime = (hop: BrewingHop, index: number) => {
    if (
      index > 0 &&
      hop.time === props.hops[index - 1].time &&
      hop.use === props.hops[index - 1].use
    ) {
      return ''
    }

    if (hop.use === 'First Wort') {
      return t('recipe.firstWort')
    } else if (hop.use === 'Dry Hop') {
      return t('recipe.dryHop')
    } else if (hop.time == 0) {
      return t('recipe.hopOnWhirlpool')
    } else {
      return t('recipe.hopOnBoil', { time: hop.time })
    }
  }

  return (
    <>
      <button
        className="cursor-pointer text-xs text-blue-600 hover:underline"
        onClick={() => setView(v => (v === 'time' ? 'amount' : 'time'))}
      >
        {view === 'time' ? t('recipe.showAmountHops') : t('recipe.showHopsRecipe')}
      </button>
      {view === 'amount' ? (
        <table className="mt-3 w-full whitespace-nowrap text-left text-sm leading-6">
          <colgroup>
            <col className="w-full" />
            <col />
            <col />
            <col />
          </colgroup>
          <thead className="border-b border-gray-200 text-gray-900">
            <tr>
              <th scope="col" className="px-0 py-3 font-semibold">
                {t('hop.title')}
              </th>
              <th
                scope="col"
                className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell"
              >
                {t('inventory.amount')}
              </th>
              <th
                scope="col"
                className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell"
              >
                {t('hop.alpha')}
              </th>
              {showSearchInventory && (
                <th scope="col" className="py-3 pl-8 pr-0 text-right font-semibold">
                  {t('recipe.inInventory')}
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {hops?.map(item => (
              <tr key={item.id} className="border-b border-gray-100">
                <td className="max-w-0 px-0 py-5 align-top">
                  <div className="truncate font-medium text-gray-900">{item.name}</div>
                  <div className="truncate text-gray-500">{/* item.description */}</div>
                </td>
                <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                  {showReadableAmount(item.amount)}
                </td>
                <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                  {item.alpha + ' %'}
                </td>
                {showSearchInventory && (
                  <td className="py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700">
                    {item.inventoryAmount}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <table className="mt-8 w-full whitespace-nowrap text-left text-sm leading-6">
          <colgroup>
            <col />
            <col />
            <col />
            <col />
          </colgroup>
          <thead className="border-b border-gray-200 text-gray-900">
            <tr>
              <th scope="col" className="px-0 py-3 font-semibold">
                {t('recipe.time')}
              </th>
              <th scope="col" className="px-0 py-3 font-semibold">
                {t('hop.title')}
              </th>
              <th
                scope="col"
                className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell"
              >
                {t('inventory.amount')}
              </th>
              <th
                scope="col"
                className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell"
              >
                {t('hop.alpha')}
              </th>
            </tr>
          </thead>
          <tbody>
            {hops?.map((item, index) => (
              <tr key={item.id} className="border-b border-gray-100">
                <td className="px-0 py-5 align-top">
                  <div className="truncate font-medium text-gray-900">
                    {showReadableBoilTime(item, index)}
                  </div>
                </td>
                <td className="px-0 py-5 align-top">
                  <div className="truncate font-medium text-gray-900">{item.name}</div>
                  <div className="truncate text-gray-500">{/* item.description */}</div>
                </td>
                <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                  {showReadableAmount(item.amount)}
                </td>
                <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
                  {item.alpha + ' %'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </>
  )
}
