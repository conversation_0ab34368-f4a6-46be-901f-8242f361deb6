import type { BrewingMashStep } from '@prisma/client'
import { UseQueryResult } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { useWebsocketMessage } from 'hooks/useWebsocket'
import { BrewingResponse } from 'services/BrewingService'

import MashTimeline from 'components/Brewing/MashTimeline'
import Timer from 'components/Brewing/Timer'
import Card from 'components/Common/Card'

export default function MashingCard(props: {
  brewing: BrewingResponse
  brewingRequest: UseQueryResult<BrewingResponse, Error>
}) {
  const { t } = useTranslation()
  const { brewing } = props
  const [isMashing, setIsMashing] = useState(props.brewing.status === 'MASHING')
  const [time, setTime] = useState(new Date())
  const [step, setStep] = useState<BrewingMashStep | null>(null)

  useEffect(() => {
    if (props.brewing.status === 'MASHING') {
      setIsMashing(true)
      startTimer(props.brewing)
    }
  }, [props.brewing])

  useWebsocketMessage(`brewing/${props.brewing.id}`, _message => {
    props.brewingRequest.refetch()
  })

  const startTimer = (item: BrewingResponse) => {
    if (item === null || item.startTime == null) {
      return
    }

    const step = item.mashSteps.find(step => step.status === 'ACTIVE' || step.status === 'HEATING')
    if (step === undefined) {
      // no step is running => is cooking hops
      return
    }

    setStep(step as BrewingMashStep)

    setTime(getTimerStartTime(step))
  }

  const getTimerStartTime = (step: BrewingMashStep) => {
    switch (step.status) {
      case 'ACTIVE':
        return new Date(step.startTime)
      case 'HEATING':
        return new Date(step.heatingStart)
      default:
        return new Date()
    }
  }

  const refreshMashSteps = () => {
    if (!brewing) {
      return
    }

    if (brewing.id) {
      props.brewingRequest.refetch()
      //   get<BrewingResponse>(`brewing/${brewing.id}`).then(response => {
      //     if (response) {
      //       startTimer(response)
      //     }
      //   })
    }
  }

  return (
    <Card title={t('brew.mashing')} className="mt-3">
      {brewing.startTime !== null && isMashing && (
        <div className="mb-6 mt-6 flex flex-col items-start text-sm leading-6">
          <div className="mb-5 flex items-center sm:pr-4">
            <div className="mr-3 inline-block h-5 w-5 rounded-full bg-green-600"></div>
            <p className="text-lg text-gray-800">
              {step?.status === 'ACTIVE' && t('brew.timerTitle', step ?? {})}
              {step?.status === 'HEATING' &&
                t('brew.heating', {
                  temperature: step.temperature,
                })}
            </p>
          </div>
          <div className="flex items-center sm:pr-4">
            <div className="mr-3 inline text-gray-500">{t('brew.mashboil')}</div>
            <div className="inline text-gray-700">
              {step?.status === 'ACTIVE' ? (
                <Timer
                  targetTime={step.time}
                  startTime={time}
                  onTimeReached={() => refreshMashSteps()}
                />
              ) : (
                <Timer startTime={time} />
              )}
            </div>
          </div>
        </div>
      )}

      <h2 className="mb-3 py-3 text-base font-semibold leading-6 text-gray-900">
        {t('recipe.boil')}
      </h2>
      <MashTimeline mashSteps={brewing.mashSteps} />
    </Card>
  )
}
