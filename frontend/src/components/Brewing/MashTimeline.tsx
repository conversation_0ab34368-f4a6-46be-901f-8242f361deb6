import { CheckCircleIcon, FireIcon, PlayCircleIcon } from '@heroicons/react/24/solid'
import { BrewingMashStep } from '@prisma/client'
import { twMerge } from 'tailwind-merge'

export type TimelineEvent = {
  id: number
  content: string
  target: string
  to?: string
  date: string
  datetime: string
  icon: unknown
  iconBackground: string
}

export default function MashTimeline({
  mashSteps,
}: Readonly<{
  mashSteps: Omit<BrewingMashStep, 'id' | 'createdAt' | 'brewingId'>[]
}>) {
  const getStatusIcon = (status: BrewingMashStep['status']) => {
    switch (status) {
      case 'DONE':
        return <CheckCircleIcon className="h-5 w-5 text-white" aria-hidden="true" />
      case 'ACTIVE':
        return <PlayCircleIcon className="h-5 w-5 text-white" aria-hidden="true" />
      case 'HEATING':
        return <FireIcon className="h-5 w-5 text-white" aria-hidden="true" />
      default:
        return null
    }
  }

  const getStatusBg = (status: BrewingMashStep['status']) => {
    switch (status) {
      case 'DONE':
        return 'bg-green-600'
      case 'ACTIVE':
        return 'bg-blue-600'
      case 'HEATING':
        return 'bg-yellow-600'
      default:
        return 'bg-gray-100'
    }
  }

  return (
    <div className="flow-root">
      <ul role="list" className="-mb-8">
        {mashSteps.map((step, eventIdx) => (
          <li key={eventIdx}>
            <div className="relative pb-8">
              {eventIdx !== mashSteps.length - 1 ? (
                <span
                  className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                  aria-hidden="true"
                />
              ) : null}
              <div className="relative flex space-x-3">
                <div>
                  <span
                    className={twMerge(
                      getStatusBg(step.status),
                      'flex h-8 w-8 items-center justify-center rounded-full ring-8 ring-white'
                    )}
                  >
                    {step.status === 'WAITING' ? (
                      <div className="h-5 w-5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
                    ) : (
                      getStatusIcon(step.status)
                    )}
                  </span>
                </div>
                <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                  <div>
                    <p className="ml-3 text-sm font-medium text-gray-900">
                      {`${step.name} bei ${step.temperature} °C für ${step.time} min (${step.type})`}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  )
}
