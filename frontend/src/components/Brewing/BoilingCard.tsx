import { CheckCircleIcon, PlayCircleIcon } from '@heroicons/react/24/solid'
import { BrewingHop } from '@prisma/client'
import { UseQueryResult } from '@tanstack/react-query'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import { post } from 'helpers/http'
import { BrewingResponse } from 'services/BrewingService'

import Timer from 'components/Brewing/Timer'
import Button from 'components/Common/Button'
import Card from 'components/Common/Card'
import HiddenCard from 'components/Common/HiddenCard'

export default function BoilingCard(
  props: Readonly<{
    brewing: BrewingResponse
    hIndex: number
    brewingRequest: UseQueryResult<BrewingResponse, Error>
  }>
) {
  const { t } = useTranslation()
  const [showBoiling, setShowBoiling] = useState(props.hIndex <= 4)
  const boilStartTime = useMemo(
    () => new Date(props.brewing.boilStartTime as unknown as string),
    [props.brewing.boilStartTime]
  )

  console.log('BoilingCard', props.brewing.boilStartTime, boilStartTime)

  const onBoilStart = async () => {
    await post(`brewing/${props.brewing?.id}/boiling`)
    props.brewingRequest.refetch()
  }

  const [groupedHops, setGroupedHops] = useState<Record<string, BrewingHop[]>>({})

  useEffect(() => {
    if (!props.brewing.hops) {
      return
    }

    const grouped = props.brewing.hops
      .filter(h => h.use.toLowerCase() === 'boil')
      .reduce(
        (acc, hop) => {
          if (!acc[hop.time]) {
            acc[hop.time] = []
          }

          acc[hop.time].push(hop)

          return acc
        },
        {} as Record<string, BrewingHop[]>
      )

    setGroupedHops(grouped)
  }, [props.brewing.hops])

  // const loadAmountHops = async (hops: BrewingHop[]) => {
  //   if (!hops) {
  //     return []
  //   }

  //   const accumulated = []
  //   for (const hop of hops) {
  //     const index = accumulated.findIndex(item => item.name === hop.name)
  //     if (index === -1) {
  //       accumulated.push({ ...hop })
  //     } else {
  //       accumulated[index].amount += hop.amount
  //     }
  //   }

  //   const inventoryItems: { id: number; name: string; amount: number; alpha: number }[] = []
  //   for (const hop of accumulated) {
  //     const data = await searchByType('hop', hop.name)

  //     if (!data) {
  //       continue
  //     }

  //     for (const item of data) {
  //       if (!item.extraData?.alpha) {
  //         continue
  //       }

  //       const alpha = item.extraData?.alpha as number
  //       const shouldAmount = calculateHops(hop.amount, hop.alpha, alpha)
  //       if (shouldAmount > item.amount) {
  //         continue
  //       }

  //       inventoryItems.push({
  //         id: item.id,
  //         name: hop.name,
  //         amount: item.amount,
  //         alpha: alpha,
  //       })
  //     }
  //   }

  //   console.log(inventoryItems)

  //   const response = hops.map(hop => {
  //     const inventory = inventoryItems.find(item => item.name === hop.name)
  //     if (inventory && hop.alpha) {
  //       console.log(hop, calculateHops(hop.amount, hop.alpha, inventory.alpha), inventory.alpha)
  //       return {
  //         ...hop,
  //         amount: calculateHops(hop.amount, hop.alpha, inventory.alpha),
  //         alpha: inventory.alpha,
  //       }
  //     } else {
  //       return hop
  //     }
  //   })

  //   return response
  // }

  // const [hops, setHops] = useState<(BrewingHop & { inventoryAmount: string })[]>([])

  // useEffect(() => {
  //   if (props.brewing === undefined) {
  //     setHops([])
  //     return
  //   }

  //   loadAmountHops(props.brewing.hops).then(h => {
  //     console.log('hops', h)
  //     setHops(h)
  //   })
  // }, [props.brewing])

  if (!showBoiling) {
    return <HiddenCard title={t('brew.boil')} onOpen={() => setShowBoiling(true)} />
  }

  const getStatusIcon = (stepTime: string) => {
    switch (stepTime) {
      case 'DONE':
        return <CheckCircleIcon className="h-5 w-5 text-white" aria-hidden="true" />
      case 'ACTIVE':
        return <PlayCircleIcon className="h-5 w-5 text-white" aria-hidden="true" />
      case 'WAITING':
        return <div className="h-5 w-5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
      default:
        return null
    }
  }

  const getStatusBg = (stepTime: string) => {
    switch (stepTime) {
      case 'DONE':
        return 'bg-green-600'
      case 'ACTIVE':
        return 'bg-blue-600'
      case 'HEATING':
        return 'bg-yellow-600'
      default:
        return 'bg-gray-100'
    }
  }

  return (
    <Card title={t('brew.boil')} className="mt-3">
      <>
        {props.brewing.boilStartTime !== null && props.brewing.status === 'BOILING' ? (
          <div className="mb-6 mt-6 flex flex-col items-start text-sm leading-6">
            <div className="mb-5 flex items-center sm:pr-4">
              <div className="mr-3 inline-block h-5 w-5 rounded-full bg-green-600"></div>
              <p className="text-lg text-gray-800">{t('brew.boilTimer')}</p>
            </div>
            <div className="flex items-center sm:pr-4">
              <div className="mr-3 inline text-gray-500">{t('brew.mashboil')}</div>
              <div className="inline text-gray-700">
                <Timer
                  startTime={boilStartTime}
                  targetTime={props.brewing.boilTime}
                  onTimeReached={() => props.brewingRequest.refetch()}
                />
              </div>
            </div>
          </div>
        ) : (
          <Button className="mb-6" onClick={onBoilStart}>
            {t('brew.startBoil')}
          </Button>
        )}

        {/* <HopsTable hops={hops} searchInventory={false} /> */}

        <div className="flow-root">
          <ul role="list" className="-mb-8">
            {Object.keys(groupedHops).map((time, eventIdx, steps) => (
              <li key={eventIdx}>
                <div className="relative pb-8">
                  {eventIdx !== steps.length - 1 ? (
                    <span
                      className="absolute left-4 top-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    />
                  ) : null}
                  <div className="relative flex space-x-3">
                    <div>
                      <span
                        className={twMerge(
                          getStatusBg(time),
                          'flex h-8 w-8 items-center justify-center rounded-full ring-8 ring-white'
                        )}
                      >
                        {getStatusIcon(time)}
                      </span>
                    </div>
                    <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                      <div>
                        <p className="ml-3 text-sm font-medium text-gray-900">
                          {`Bei ${time} Minuten ${groupedHops[time].map(h => `von ${h.name} ${h.amount}g`).join(', ')} hinzufügen`}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </>
    </Card>
  )
}
