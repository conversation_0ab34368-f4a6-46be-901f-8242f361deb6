import * as luxon from 'luxon'
import { memo, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'

const Timer = memo(function Timer({
  targetTime,
  startTime,
  onTimeReached,
}: {
  targetTime?: number
  startTime?: Date
  onTimeReached?: () => void
}) {
  const { t } = useTranslation()
  const [minutes, setMinutes] = useState(0)
  const [seconds, setSeconds] = useState(0)
  const timerInterval = useRef<number | null>(null)

  useEffect(() => {
    if (timerInterval.current) {
      clearInterval(timerInterval.current)
    }

    if (!startTime) {
      startTime = new Date()
    }

    const time = luxon.DateTime.fromJSDate(startTime)
      .diffNow(['minutes', 'seconds'])
      .toObject() as { minutes?: number; seconds?: number }

    setMinutes(Math.abs(time.minutes ?? 0))
    setSeconds(Math.abs(Number(time.seconds?.toFixed(0) ?? 0)))

    timerInterval.current = setInterval(() => {
      setSeconds(s => {
        const seconds = s + 1

        if (seconds >= 60) {
          setMinutes(setMinute)
          return 0
        }

        return seconds
      })
    }, 1000) as unknown as number
  }, [startTime])

  const setMinute = (m: number) => {
    const minute = m + 1

    if (targetTime && minute >= targetTime) {
      onTimeReached && onTimeReached()
    }

    return minute
  }

  return (
    <span>
      {t('brew.timer', {
        m: minutes,
        s: seconds,
        count: targetTime ?? 0,
      })}
    </span>
  )
})

export default Timer
