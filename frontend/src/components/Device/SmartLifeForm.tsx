import type { Device } from '@prisma/client'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'

import { useGet } from 'helpers/http'
import { useGetDevices } from 'services/DeviceService'

export default function SmartLifeForm(
  props: Readonly<{
    data?: Device
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const deviceRequest = useGetDevices()
  const homesRequest = useGet<unknown[]>('integration/smartlife/home')
  const { t } = useTranslation()

  console.log(deviceRequest.data, homesRequest.data)

  return (
    <form onSubmit={() => props.onCancel()}>
      <p>No homes found</p>

      <div className="border-b border-gray-900/10 pb-12"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.save')}
        </button>
      </div>
    </form>
  )
}
