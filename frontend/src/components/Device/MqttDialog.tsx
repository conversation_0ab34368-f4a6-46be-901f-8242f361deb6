import type { Device, MqttClient } from '@prisma/client'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'

import { post, useGet, useHttpNotifications } from 'helpers/http'
import { FormComponentProps } from 'hooks/Dialog/useEditDialog'

import Button from 'components/Common/Button'

export default function MqttDialog(props: FormComponentProps<Device>): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()

  const deviceRequest = useGet<MqttClient>(`device/${props.data?.id}/mqtt`)

  return (
    <div>
      {!deviceRequest.data && (
        <div>
          <Button
            style="primary"
            onClick={async () => {
              await resolveResponse(() => post(`device/${props.data?.id}/mqtt`, {}))
              deviceRequest.refetch()
            }}
          >
            {t('device.createMqtt')}
          </Button>
        </div>
      )}

      {deviceRequest.data && (
        <dl className="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
          <div className="flex justify-between gap-x-4 py-3">
            <dt className="text-gray-500">{t('device.mqttClientId')}</dt>
            <dd className="text-gray-700">{deviceRequest.data.username}</dd>
          </div>
          <div className="flex justify-between gap-x-4 py-3">
            <dt className="text-gray-500">{t('common.username')}</dt>
            <dd className="text-gray-700">{deviceRequest.data.username}</dd>
          </div>
          <div className="flex justify-between gap-x-4 py-3">
            <dt className="text-gray-500">{t('common.password')}</dt>
            <dd className="flex items-start gap-x-2">
              <div className="font-medium text-gray-900">{deviceRequest.data.password}</div>
            </dd>
          </div>
        </dl>
      )}
    </div>
  )
}
