import { DeviceModel } from '@backendZod/device'
import { zodResolver } from '@hookform/resolvers/zod'
import type { Device } from '@prisma/client'
import { ReactElement } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import DropdownInput from 'components/Common/Form/DropdownInput'
import TextInput from 'components/Common/Form/TextInput'

const Model = DeviceModel.omit({
  id: true,
  operator: true,
  mqttClientId: true,
  mqttPassword: true,
  mqttUsername: true,
  createdAt: true,
  updatedAt: true,
  type: true,
}).merge(
  z.object({
    type: z.enum(['TEMPERATURE', 'SWITCH', 'ISPINDEL', 'OTHER']),
  })
)

type CompleteDevice = z.infer<typeof Model>

const deviceTypes = ['TEMPERATURE', 'SWITCH', 'ISPINDEL', 'OTHER']

export default function EditForm(
  props: Readonly<{
    data?: Device
    onCancel: () => void
    onSubmit: (data: CompleteDevice) => void
  }>
): ReactElement {
  const { t } = useTranslation()

  const methods = useForm({
    resolver: zodResolver(Model),
    defaultValues: props.data ?? {},
  })

  return (
    <form onSubmit={methods.handleSubmit(props.onSubmit)}>
      <FormProvider {...methods}>
        <div className="space-y-12">
          <div className="mt-3">
            <TextInput name="name" className="mt-3" label={t('common.name')} />

            <DropdownInput
              name="type"
              label={t('device.deviceType')}
              data={deviceTypes.map(key => {
                return {
                  text: t('device.type.' + key.toLowerCase()),
                  value: key,
                }
              })}
            />
          </div>
        </div>
      </FormProvider>

      <div className="border-b border-gray-900/10 pb-12"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.save')}
        </button>
      </div>
    </form>
  )
}
