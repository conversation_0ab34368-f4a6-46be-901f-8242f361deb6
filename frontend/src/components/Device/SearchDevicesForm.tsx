import { zodResolver } from '@hookform/resolvers/zod'
import type { BluetoothRemoteGATTService } from '@types/web-bluetooth'
import { ReactElement, Ref, useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'
import { z } from 'zod'

import {
  createApiKey,
  DeviceOperator,
  DeviceType,
  registerDevice,
  useGetDevicesByType,
} from 'services/DeviceService'

import Button from 'components/Common/Button'
import Loading from 'components/Common/Loading'

const statuses = {
  offline: 'text-gray-500 bg-gray-100/10',
  online: 'text-green-400 bg-green-400/10',
  error: 'text-rose-400 bg-rose-400/10',
}

export default function SearchDevicesForm(): ReactElement {
  const { t } = useTranslation()

  const [isBleWorks, setIsBleWorks] = useState(true)
  const [deviceHasWifi, setDeviceHasWifi] = useState(false)
  const [status, setStatus] = useState<
    'start' | 'searching' | 'loaded' | 'error' | 'notFound' | 'register'
  >('start')

  const brewtoolDevices = useGetDevicesByType(DeviceType.BREWTOOL)

  const [deviceId, setDeviceId] = useState<string | null>(null)
  const [device, setDevice] = useState<BluetoothDevice | null>(null)

  const serviceGeneral = useRef<BluetoothRemoteGATTService | null>(null)
  const inputWifi = useRef<unknown>(null)

  useEffect(() => {
    if (navigator.bluetooth != undefined) {
      navigator.bluetooth.getAvailability().then(available => {
        if (available) {
          setIsBleWorks(true)
        } else {
          setIsBleWorks(false)
        }
      })
    } else {
      setIsBleWorks(false)
    }
  }, [])

  const model = z.object({
    ssid: z.string(),
    password: z.string(),
  })

  const methods = useForm({
    resolver: zodResolver(model),
    defaultValues: {
      ssid: '',
      password: '',
    },
  })

  const getDevice = async () => {
    return await navigator.bluetooth.requestDevice({
      filters: [
        {
          services: ['91bad492-b950-4226-aa2b-4ede9fa42f59'],
        },
      ],
    })
  }

  const decoder = new TextDecoder('utf-8')
  const readData = async (charRef: Ref<unknown> | unknown) => {
    const dataView = await (charRef.current ? charRef.current?.readValue() : charRef?.readValue())
    const value = decoder.decode(dataView)
    return value.replaceAll('\0', '')
  }

  async function readJsonData<T>(charRef: Ref<unknown>): Promise<T> {
    const value = await readData(charRef)
    return JSON.parse(value) as T
  }

  const register = async () => {
    try {
      const device = await registerDevice({
        name: 'Philun Brewtool',
        operator: DeviceOperator.BREWTOOL,
        operatorDeviceId: deviceId,
        type: DeviceType.BREWTOOL,
      })

      const apiKey = await createApiKey(device)

      const characteristic = serviceGeneral.current?.getCharacteristic(
        'c68a2e95-b47a-4880-8b90-ed5f92f8aee0'
      )
      characteristic?.writeValue(new TextEncoder().encode(apiKey.key))
    } catch (error) {
      setStatus('error')
    }
  }

  const onSearch = async () => {
    try {
      setStatus('searching')
      const device = await getDevice()
      const response = await device.gatt?.connect()

      setDevice(device)

      console.log('device', device)
      if (!response?.connected) {
        throw new Error('Device not connected')
      }

      const service = await device.gatt?.getPrimaryService('91bad492-b950-4226-aa2b-4ede9fa42f59')
      serviceGeneral.current = service

      const characterDeviceId = await service?.getCharacteristic(
        '678fb259-4af8-4e47-a249-08fbbdbe5845'
      )
      const deviceId = await readData(characterDeviceId)
      setDeviceId(deviceId)

      inputWifi.current = await service?.getCharacteristic('cba1d466-344c-4be3-ab3f-189f80dd7518')

      const wifi = await readJsonData<{ ssid: string; status: boolean }>(inputWifi)
      setDeviceHasWifi(wifi.status)
      methods.setValue('ssid', wifi.ssid)

      if (!brewtoolDevices.data?.find(device => device.operatorDeviceId === deviceId)) {
        setStatus('register')
        return
      }

      setStatus('loaded')
    } catch (error) {
      console.log(error)
      if (error instanceof Error && error.name === 'NotFoundError') {
        setStatus('notFound')
      } else {
        console.error(error)
        setStatus('error')
      }
    }
  }

  const loadWifiScan = async () => {
    const inputWifiScan = await serviceGeneral.current?.getCharacteristic(
      '594228c8-9eb8-497c-8886-960d1c9cdb67'
    )
    const wifiScan = await readJsonData<string[]>(inputWifiScan)
    console.log('wifiScan', wifiScan)
  }

  const saveWifi = async (data: z.infer<typeof model>) => {
    try {
      const encoder = new TextEncoder()

      await inputWifi.current?.writeValue(
        encoder.encode(JSON.stringify({ ssid: data.ssid, password: data.password }))
      )

      const password = await readData(inputWifi)
      console.log('password', password)
      setDeviceHasWifi(password.toLowerCase() == 'ok')
    } catch (error) {
      console.error(error)
    }
  }

  if (!isBleWorks) {
    return <div>{t('device.bluetoothNotWork')}</div>
  }

  return (
    <div>
      {status === 'register' && (
        <div className="flex flex-col gap-y-3">
          <h3>{t('device.connectedWithDevice', { name: device?.name })}</h3>

          <Button type="button" onClick={() => register()}>
            {t('device.registerDevice')}
          </Button>

          <a onClick={() => onSearch()} className="text-sm/6 text-indigo-600 hover:underline">
            {t('device.searchForDevices')}
          </a>
        </div>
      )}
      {status === 'loaded' && (
        <div className="flex flex-col gap-y-3">
          <h3>{t('device.connectedWithDevice', { name: device?.name })}</h3>
          <div className="p-x-6 p-y-3 flex items-center gap-x-3">
            <div
              className={twMerge(
                deviceHasWifi ? statuses['online'] : statuses['error'],
                'flex-none rounded-full p-1'
              )}
            >
              <div className="h-2 w-2 rounded-full bg-current" />
            </div>
            <h2 className="min-w-0 text-sm/6 font-semibold text-gray-900">
              <div className="flex gap-x-2">
                {deviceHasWifi
                  ? t('device.hasInternetConnection')
                  : t('device.hasNoInternetConnection')}
              </div>
            </h2>
          </div>
          <form onSubmit={methods.handleSubmit(saveWifi)}>
            <div className="grid gap-x-6 gap-y-4 pb-3 md:gap-y-8">
              <div>
                <label htmlFor="ssid" className="block text-sm/6 font-medium text-gray-900">
                  {t('device.ssid')}
                </label>
                <div className="mt-2">
                  <input
                    id="ssid"
                    type="text"
                    {...methods.register('ssid')}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm/6 font-medium text-gray-900">
                  {t('device.password')}
                </label>
                <div className="mt-2">
                  <input
                    id="password"
                    type="password"
                    autoComplete="off"
                    placeholder="********"
                    {...methods.register('password')}
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm/6"
                  />
                </div>
              </div>
            </div>

            <Button type="submit">{t('common.save')}</Button>
          </form>

          <a onClick={() => onSearch()} className="text-sm/6 text-indigo-600 hover:underline">
            {t('device.searchForDevices')}
          </a>
        </div>
      )}

      {status === 'searching' && <Loading />}
      {status === 'notFound' && <div>{t('device.deviceNotFound')}</div>}
      {(status === 'notFound' || status === 'error' || status === 'start') && isBleWorks && (
        <div className="mb-3 mt-3 flex">
          <Button onClick={() => onSearch()}>{t('device.searchForDevices')}</Button>
        </div>
      )}
    </div>
  )
}
