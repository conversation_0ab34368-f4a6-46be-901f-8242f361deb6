import type { Device } from '@prisma/client'
import { iconUrl } from 'constants'
import { ReactElement, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import tuyaIcon from 'assets/tuya.png'

import { useGet } from 'helpers/http'

import Button from 'components/Common/Button'
import SkeletonLoading from 'components/Common/SkeletonLoading'

export default function SelectDeviceForm(
  props: Readonly<{
    data?: Device
    onCancel: () => void
    onSubmit: (data: unknown) => void
  }>
): ReactElement {
  const { t } = useTranslation()

  const [socket, setSocket] = useState<Device | null>(null)

  const deviceRequest = useGet<Device[]>('device')

  console.log(deviceRequest.data)

  if (deviceRequest.isLoading) {
    return <SkeletonLoading />
  }

  if (socket) {
    return (
      <>
        <div className="py-6">
          <h3 className="text-base font-semibold leading-7 text-gray-900">
            {t('device.deviceType')}
          </h3>
          <p className="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
            {t('device.deviceTypeDescription')}
          </p>
        </div>

        <div className="flex flex-wrap gap-4">
          {[
            { value: 'HEATING', text: t('common.heating') },
            { value: 'COOLING', text: t('common.cooling') },
          ].map(option => (
            <button
              key={option.value}
              value={option.value}
              onClick={() => props.onSubmit({ ...socket, type: option.value })}
              className={twMerge(
                'cursor-pointer hover:ring-2 hover:ring-indigo-600 hover:ring-offset-2 focus:outline-none',
                'bg-white text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50',
                'flex items-center justify-center rounded-md px-3 py-3 text-sm font-semibold uppercase sm:flex-1'
              )}
            >
              <span>{option.text}</span>
            </button>
          ))}
        </div>
      </>
    )
  }

  return (
    <>
      <div className="space-y-12">
        <ul role="list" className="divide-y divide-gray-100">
          {deviceRequest.data?.map(device => (
            <li key={device.id} className="flex justify-between gap-x-6 py-5">
              <div className="flex min-w-0 gap-x-4">
                <img
                  className="h-12 w-12 flex-none rounded-full bg-gray-50"
                  src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                  alt=""
                />
                <div className="min-w-0 flex-auto">
                  <p className="text-sm font-semibold leading-6 text-gray-900">{device.name}</p>
                  <p className="mt-1 truncate text-xs leading-5 text-gray-500">{device.type}</p>
                </div>
              </div>
              <div className="flex shrink-0 items-center">
                <Button
                  style="secondary"
                  size="sm"
                  onClick={() => {
                    if (device.type === 'SWITCH') {
                      setSocket(device)
                    } else {
                      props.onSubmit(device)
                    }
                  }}
                >
                  {t('common.add')}
                </Button>
              </div>
            </li>
          ))}
        </ul>
      </div>

      <div className="border-b border-gray-900/10 pb-12"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.save')}
        </button>
      </div>
    </>
  )
}
