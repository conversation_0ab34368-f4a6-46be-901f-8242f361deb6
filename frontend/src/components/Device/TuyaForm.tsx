import { zodResolver } from '@hookform/resolvers/zod'
import type { Devi<PERSON> } from '@prisma/client'
import { ReactElement } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { get } from 'helpers/http'
import { useGetDevices } from 'services/DeviceService'

import TextInput from 'components/Common/Form/TextInput'

const model = z.object({
  tuyaDeviceId: z.string(),
})

export default function TuyaForm(
  props: Readonly<{
    data?: Device
    onCancel: () => void
    onSubmit: (data: z.infer<typeof model>) => void
  }>
): ReactElement {
  const deviceRequest = useGetDevices()
  const { t } = useTranslation()

  const methods = useForm({
    resolver: zodResolver(model),
  })

  const onSearchTuyaDevice = async (data: z.infer<typeof model>) => {
    const alreadyExist = deviceRequest.data?.find(
      device => device.tuyaDeviceId === data.tuyaDeviceId
    )

    if (alreadyExist) {
      methods.setError('tuyaDeviceId', {
        type: 'manual',
        message: t('device.tuyaDeviceIdAlreadyExist'),
      })
      return
    }

    const response = await get<{
      result: {
        name: string
        category: string
      }
    }>(`integration/tuya/device/${data.tuyaDeviceId}`)

    if (!response) {
      methods.setError('tuyaDeviceId', {
        type: 'manual',
        message: t('device.tuyaDeviceIdNotFound'),
      })
      return
    }

    const device: Partial<Device> = {
      tuyaDeviceId: data.tuyaDeviceId,
      name: response.result.name,
      type: response.result.category === 'cz' ? 'SWITCH' : 'TEMPERATURE',
      operator: 'TUYA',
    }

    props.onSubmit(device)
  }

  return (
    <form onSubmit={methods.handleSubmit(onSearchTuyaDevice)}>
      <FormProvider {...methods}>
        <div className="space-y-12">
          <div className="mt-3">
            <TextInput name="tuyaDeviceId" className="mt-3" label={t('device.tuyaDeviceId')} />
          </div>
        </div>
      </FormProvider>

      <div className="border-b border-gray-900/10 pb-12"></div>

      <div className="mt-3 flex items-center justify-end gap-x-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-gray-900"
          onClick={() => props.onCancel()}
        >
          {t('common.cancel')}
        </button>
        <button
          type="submit"
          className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          {t('common.save')}
        </button>
      </div>
    </form>
  )
}
