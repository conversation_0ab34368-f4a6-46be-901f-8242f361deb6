import { BoltSlashIcon } from '@heroicons/react/24/solid'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'

import { formatTemperature } from 'helpers/format'
import { useActualDeviceTemperature, useActualTemperature } from 'services/FermentationService'

import Card from 'components/Common/Card'
import SkeletonLoading from 'components/Common/SkeletonLoading'

export default function FermentationCard(): ReactElement {
  const { t } = useTranslation()

  const temperature = useActualTemperature()

  const deviceTemperature = useActualDeviceTemperature()

  const pathFermentation = '/fermentation'

  return (
    <Card title={t('fermentation.name')} className="h-min">
      <>
        {temperature.isLoading && <SkeletonLoading />}
        {!temperature.isLoading && !temperature.data && (
          <div className="text-center">
            <BoltSlashIcon className="mx-auto h-10 w-10" />
            <h3 className="mt-2 text-sm font-semibold text-gray-900">
              {t('fermentation.noActiveFermentation')}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {t('fermentation.noActiveFermentationHelp')}
            </p>
            <div className="mt-6">
              <Link
                to={pathFermentation}
                className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                {t('fermentation.startFermentation')}
              </Link>
            </div>
          </div>
        )}
        {temperature.data && (
          <div className="text-center">
            <p className="text-md mt-2 flex items-center justify-center font-semibold text-gray-900">
              <div className="mr-2 h-3 w-3 rounded-full bg-green-600"></div>
              {t('fermentation.activeFermentation')}
            </p>
            <h3 className="mt-2 text-3xl font-semibold text-gray-900">
              {formatTemperature(deviceTemperature.data?.temperature)}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {t('fermentation.shouldTemperature')}{' '}
              {formatTemperature(deviceTemperature.data?.temperature)}
            </p>
            <div className="mt-6">
              <Link
                to={pathFermentation}
                className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                {t('common.open')}
              </Link>
            </div>
          </div>
        )}
      </>
    </Card>
  )
}
