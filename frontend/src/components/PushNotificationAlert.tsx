import { useTranslation } from 'react-i18next'

import {
  createPushNotifications,
  hasPushNotificationPermission,
  hasPushNotificationSupport,
} from 'helpers/onPushNotifications'

import { AlertStyles, AlertWithButton } from 'components/Common/Alert'

export function PushNotificationAlert() {
  const hasPush = hasPushNotificationSupport() && !hasPushNotificationPermission()
  const { t } = useTranslation()

  if (!hasPush) {
    return null
  }

  return (
    <div className="mt-3">
      <AlertWithButton
        style={AlertStyles.WARNING}
        title={t('notification.push.alert.title')}
        text={t('notification.push.alert.body')}
        buttons={[
          {
            children: t('common.activate'),
            onClick: () => createPushNotifications(),
          },
        ]}
      />
    </div>
  )
}
