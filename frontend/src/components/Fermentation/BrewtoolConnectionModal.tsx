import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { X, Wifi, Refresh<PERSON>w, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'

import BrewtoolBLEService, {
  type BrewtoolDevice,
  type BrewtoolConfig,
  type ConnectionStatus,
} from 'services/BrewtoolBLEService'

// Zod schema for BrewtoolConfig
const brewtoolConfigSchema = z.object({
  wifiSSID: z.string(),
  wifiPassword: z.string().optional(),
  temperatureUnit: z.enum(['celsius', 'fahrenheit']).optional(),
  updateInterval: z.number().min(1).optional(),
})

type BrewtoolConfigForm = z.infer<typeof brewtoolConfigSchema>

interface BrewtoolConnectionModalProps {
  isOpen: boolean
  onClose: () => void
  onConnect?: (device: BrewtoolDevice, config?: BrewtoolConfig) => void
  onSave?: (device: BrewtoolDevice, config: BrewtoolConfig) => void
}

export default function BrewtoolConnectionModal({
  isOpen,
  onClose,
  onConnect,
  onSave,
}: BrewtoolConnectionModalProps) {
  const { t } = useTranslation()

  const [device, setDevice] = useState<BrewtoolDevice | null>(null)
  const [isConnecting, setIsConnecting] = useState(false)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [isScanningWifi, setIsScanningWifi] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null)
  const [availableNetworks, setAvailableNetworks] = useState<string[]>([])
  const [deviceId, setDeviceId] = useState<string | null>(null)

  // react-hook-form setup
  const {
    register,
    handleSubmit,
    setValue,
    reset,
    watch,
    formState: { errors },
  } = useForm<BrewtoolConfigForm>({
    resolver: zodResolver(brewtoolConfigSchema),
    defaultValues: {
      wifiSSID: '',
      wifiPassword: '',
      temperatureUnit: 'celsius',
      updateInterval: 30,
    },
  })

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setDevice(null)
      setConnectionStatus(null)
      setAvailableNetworks([])
      reset({
        wifiSSID: '',
        wifiPassword: '',
        temperatureUnit: 'celsius',
        updateInterval: 30,
      })
    }
  }, [isOpen, reset])

  // Request device selection
  const handleRequestDevice = async () => {
    setIsConnecting(true)
    try {
      const selectedDevice = await BrewtoolBLEService.requestDevice()
      await BrewtoolBLEService.connectToDevice(selectedDevice)

      setDevice(selectedDevice)

      const wifiData = await BrewtoolBLEService.readWifiStatus()
      setConnectionStatus({ hasInternet: wifiData.status } as ConnectionStatus)
      setValue('wifiSSID', wifiData.ssid || '')

      const deviceId = await BrewtoolBLEService.readDeviceId()
      setDeviceId(deviceId)
    } catch (error) {
      console.error('Device selection failed:', error)
      alert(error instanceof Error ? error.message : t('ble.unknownError'))
    } finally {
      setIsConnecting(false)
    }
  }

  // Test internet connection
  // const testInternetConnection = async () => {
  //   if (!device) return

  //   setIsTestingConnection(true)
  //   try {
  //     const status = await BrewtoolBLEService.testInternetConnection()
  //     setConnectionStatus(status)

  //     // Update config with current WiFi SSID if available
  //     const wifiStatus = await BrewtoolBLEService.readWifiStatus()
  //     if (wifiStatus.ssid) {
  //       setValue('wifiSSID', wifiStatus.ssid)
  //     }

  //     if (!status.hasInternet) {
  //       alert(t('ble.noInternetConnection'))
  //     }
  //   } catch (error) {
  //     console.error('Connection test failed:', error)
  //     alert(error instanceof Error ? error.message : t('ble.connectionTestFailed'))
  //   } finally {
  //     setIsTestingConnection(false)
  //   }
  // }

  // Scan for WiFi networks
  const scanWifiNetworks = async () => {
    if (!device) return

    setIsScanningWifi(true)
    try {
      const networks = await BrewtoolBLEService.scanWifiNetworks()
      setAvailableNetworks(networks)
    } catch (error) {
      console.error('WiFi scan failed:', error)
      alert(error instanceof Error ? error.message : t('ble.wifiScanFailed'))
    } finally {
      setIsScanningWifi(false)
    }
  }

  // Handle save configuration
  const onSaveForm = async (data: BrewtoolConfigForm) => {
    // if (!device) return

    // if (!data.wifiSSID?.trim()) {
    //   alert(t('ble.wifiSSIDRequired'))
    //   return
    // }

    try {
      await BrewtoolBLEService.writeWifiConfig(data.wifiSSID, data.wifiPassword || '')

      onSave?.(device as BrewtoolDevice, data as BrewtoolConfig)
      onClose()
    } catch (error) {
      console.error('Failed to save WiFi configuration:', error)
      alert(error instanceof Error ? error.message : t('ble.saveError'))
    }
  }

  // Handle connect
  // const handleConnect = () => {
  //   if (!device) return

  //   const data = watch()
  //   try {
  //     onConnect?.(device, data as BrewtoolConfig)
  //     onClose()
  //   } catch (error) {
  //     console.error('Connection failed:', error)
  //     alert(error instanceof Error ? error.message : t('ble.connectionError'))
  //   }
  // }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b p-4">
          <h2 className="text-xl font-semibold">{device?.name || t('ble.unknownBrewtool')}</h2>
          <button onClick={onClose} className="rounded-full p-2 hover:bg-gray-100">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6 p-6">
          {/* Device Selection */}
          {!device && (
            <div className="text-center">
              <div className="mb-4">
                <div className="mb-4 text-6xl">🍺</div>
                <h3 className="mb-2 text-lg font-medium">{t('ble.selectBrewtool')}</h3>
                <p className="mb-4 text-gray-600">{t('ble.selectBrewtoolDescription')}</p>
              </div>
              <button
                onClick={handleRequestDevice}
                disabled={isConnecting}
                className="inline-flex items-center rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
              >
                {isConnecting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Wifi className="mr-2 h-4 w-4" />
                )}
                {isConnecting ? t('ble.connecting') : t('ble.selectDevice')}
              </button>
            </div>
          )}

          {/* Device Connected */}
          {device && (
            <form onSubmit={handleSubmit(onSaveForm)}>
              {/* Device Info */}
              {/* <div className="rounded-lg bg-gray-50 p-4">
                <h3 className="mb-2 font-medium">{t('ble.deviceInformation')}</h3>
                <div className="flex items-center">
                  <div className="mr-3 text-2xl">🍺</div>
                  <div>
                    <div className="font-medium">{device.name || t('ble.unknownBrewtool')}</div>
                    <div className="text-sm text-gray-600">{device.id}</div>
                  </div>
                </div>
              </div> */}

              {/* Connection Status */}
              <div className="rounded-lg bg-gray-50 p-4">
                <div className="mb-3 flex items-center justify-between">
                  <h3 className="font-medium">{t('ble.connectionStatus')}</h3>
                  {/* <button
                    onClick={testInternetConnection}
                    disabled={isTestingConnection}
                    className="inline-flex items-center rounded bg-blue-100 px-3 py-1 text-sm text-blue-700 hover:bg-blue-200 disabled:opacity-50"
                  >
                    {isTestingConnection ? (
                      <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="mr-1 h-4 w-4" />
                    )}
                    {t('ble.testConnection')}
                  </button> */}
                </div>

                {connectionStatus && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('ble.internetConnection')}:</span>
                      <div
                        className={`inline-flex items-center rounded px-2 py-1 text-xs font-medium ${
                          connectionStatus.hasInternet
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {connectionStatus.hasInternet ? (
                          <CheckCircle className="mr-1 h-3 w-3" />
                        ) : (
                          <AlertCircle className="mr-1 h-3 w-3" />
                        )}
                        {connectionStatus.hasInternet ? t('ble.online') : t('ble.offline')}
                      </div>
                    </div>
                    {connectionStatus.firmwareVersion && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">{t('ble.firmwareVersion')}:</span>
                        <span className="font-mono text-sm">
                          {connectionStatus.firmwareVersion}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{t('ble.deviceId')}:</span>
                      <span className="font-mono text-sm">{deviceId ?? ''}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* WiFi Configuration */}
              <div className="my-3">
                <h3 className="mb-3 font-medium">{t('ble.wifiConfiguration')}</h3>
                <div className="space-y-4">
                  <div>
                    <div className="mb-1 flex items-center justify-between">
                      <label className="block text-sm font-medium text-gray-700">
                        {t('ble.wifiNetwork')}
                      </label>
                      <button
                        type="button"
                        onClick={scanWifiNetworks}
                        disabled={isScanningWifi}
                        className="inline-flex items-center rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 hover:bg-gray-200 disabled:opacity-50"
                      >
                        {isScanningWifi ? (
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                        ) : (
                          <RefreshCw className="mr-1 h-3 w-3" />
                        )}
                        {t('ble.scanNetworks')}
                      </button>
                    </div>

                    {availableNetworks.length > 0 && (
                      <div className="mb-2 max-h-32 overflow-y-auto rounded border border-gray-200">
                        {availableNetworks.map((network, index) => (
                          <button
                            type="button"
                            key={index}
                            onClick={() => setValue('wifiSSID', network)}
                            className={`w-full border-b border-gray-100 px-3 py-2 text-left text-sm last:border-b-0 hover:bg-gray-50 ${
                              watch('wifiSSID') === network ? 'bg-blue-50 text-blue-700' : ''
                            }`}
                          >
                            📶 {network}
                          </button>
                        ))}
                      </div>
                    )}

                    <input
                      type="text"
                      {...register('wifiSSID')}
                      autoComplete="off"
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={t('ble.enterWifiName')}
                    />
                    {errors.wifiSSID && (
                      <span className="text-xs text-red-600">{errors.wifiSSID.message}</span>
                    )}
                  </div>

                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      {t('ble.wifiPassword')}
                    </label>
                    <input
                      type="password"
                      {...register('wifiPassword')}
                      autoComplete="off"
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder={t('ble.enterWifiPassword')}
                    />
                    {errors.wifiPassword && (
                      <span className="text-xs text-red-600">{errors.wifiPassword.message}</span>
                    )}
                  </div>
                </div>

                <button
                  type="submit"
                  className="mt-3 rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700"
                >
                  {t('ble.connectToWifi')}
                </button>
              </div>

              {/* Basic Configuration */}
              {/* <div>
                <h3 className="mb-3 font-medium">{t('ble.basicConfiguration')}</h3>
              </div> */}

              {/* Action Buttons */}
              {/* <div className="flex justify-end space-x-3 border-t pt-4">
                <button
                  type="submit"
                  className="rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50"
                >
                  {t('common.save')}
                </button>
              </div> */}
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
