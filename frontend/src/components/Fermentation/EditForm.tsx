import { TemperatureModel } from '@backendZod/index'
import { zodResolver } from '@hookform/resolvers/zod'
import { ReactElement } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

const Model = TemperatureModel.omit({ id: true, createdAt: true, updatedAt: true, startDate: true })

type FermentationTemperature = z.infer<typeof Model>

export default function EditForm(
  props: Readonly<{
    onCancel: () => void
    onSubmit: (data: FermentationTemperature) => void
    data?: FermentationTemperature
  }>
): ReactElement {
  const { t } = useTranslation()

  const methods = useForm<FermentationTemperature>({
    resolver: zodResolver(Model),
    defaultValues: props.data ?? { temperature: 10, sendPush: false },
  })

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(props.onSubmit)}>
        <div className="space-y-12">
          <div className="border-b border-gray-900/10 pb-12">
            <p className="mt-2 text-sm leading-6 text-gray-600">
              {t('fermentation.formDescription')}
            </p>
            <div className="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <div className="sm:col-span-4">
                <label
                  htmlFor="temperature"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  {t('fermentation.temperature')}
                </label>
                <div className="mt-2">
                  <div className="flex rounded-md shadow-sm ring-1 ring-inset ring-gray-300 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-600 sm:max-w-md">
                    <input
                      type="number"
                      id="temperature"
                      className="block flex-1 border-0 bg-transparent py-1.5 pr-1 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm sm:leading-6"
                      placeholder="www.example.com"
                      {...methods.register('temperature', {
                        setValueAs: v => (typeof v === 'string' ? Number(v.replace(',', '.')) : v),
                      })}
                    />
                    <span className="flex select-none items-center pl-1 pr-3 text-gray-500 sm:text-sm">
                      {' '}
                      °C
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative mt-2 flex items-start">
              <div className="flex h-6 items-center">
                <input
                  id="sendPushOnReached"
                  aria-describedby="sendPushOnReached-description"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                  {...methods.register('sendPush')}
                />
              </div>
              <div className="ml-3 text-sm leading-6">
                <label htmlFor="sendPushOnReached" className="font-medium text-gray-900">
                  {t('fermentation.hasPushOnReached')}
                </label>{' '}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-3 flex items-center justify-end gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-gray-900"
            onClick={() => props.onCancel()}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            className="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            {t('common.save')}
          </button>
        </div>
      </form>
    </FormProvider>
  )
}
