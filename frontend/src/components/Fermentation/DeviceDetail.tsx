import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import useDeviceStatus, { COLD_CRUSH } from 'hooks/Fermentation/useDeviceStatus'
import { Brewtool } from 'services/BrewtoolService'
import { DeviceType } from 'services/DeviceService'

import SpindelDisplay from 'components/Fermentation/SpindelDisplay'
import StatisticsSection from 'components/Fermentation/StatisticsSection'
import StatusBadge from 'components/Fermentation/StatusBadge'
import TemperatureDisplay from 'components/Fermentation/TemperatureDisplay'
import BrewtoolConnectionModal from 'components/Fermentation/BrewtoolConnectionModal'

type DeviceDetailProps = {
  device: Brewtool
  onStartFermentation: () => void
  onColdCrash: () => void
  onTurnOff: () => void
  onPlatoAlert: () => void
  onConnectSpindle: () => void
}

const DeviceDetail = ({
  device,
  onStartFermentation,
  onColdCrash,
  onTurnOff,
  onPlatoAlert,
  onConnectSpindle,
}: DeviceDetailProps) => {
  const { t } = useTranslation()
  const [showBrewtoolModal, setShowBrewtoolModal] = useState(false)

  const spindle = useMemo(
    () => device.deviceConnections.find(d => d.type === DeviceType.ISPINDEL),
    [device.deviceConnections]
  )

  const status = useDeviceStatus(device)

  // Handle Brewtool connection
  const handleBrewtoolConnect = () => {
    setShowBrewtoolModal(true)
  }

  // Handle modal connect
  const handleModalConnect = (brewtoolDevice: any, config?: any) => {
    console.log('Connecting to Brewtool:', brewtoolDevice, config)
    // Here you could implement the actual connection logic
    // For now, just close the modal
    setShowBrewtoolModal(false)
  }

  // Handle modal save
  const handleModalSave = (brewtoolDevice: any, config: any) => {
    console.log('Saving Brewtool configuration:', brewtoolDevice, config)
    // Here you could implement saving the configuration
    setShowBrewtoolModal(false)
  }

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow">
      {/* Header */}
      <div className="border-b p-4 md:p-6">
        <div className="flex flex-wrap items-center justify-between gap-2">
          <h1 className="text-xl font-bold text-gray-800 md:text-2xl">
            {device.name || t('fermentation.unnamedDevice')}
          </h1>
          <div className="flex justify-center gap-2">
            <button
              type="button"
              onClick={handleBrewtoolConnect}
              className="inline-flex items-center rounded-lg border border-blue-200 bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {t('ble.connectToBrewtool')}
            </button>
            <StatusBadge status={status} large />
          </div>
        </div>
      </div>
      {/* Main content */}
      <div className="p-4 md:p-6">
        {/* Temperature section */}
        <div className="mb-6">
          <TemperatureDisplay brewtool={device} />
        </div>

        {/* Spindle information - only shown if present */}
        <SpindelDisplay spindle={spindle} onConnectSpindle={onConnectSpindle} />

        {/* Action buttons */}
        <div className="mb-6 flex flex-wrap gap-3">
          {!device.config?.temperature && (
            <button
              onClick={onStartFermentation}
              className="rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700"
            >
              {t('fermentation.startFermentation')}
            </button>
          )}
          {device.config?.temperature && device.config.temperature !== COLD_CRUSH && (
            <button
              onClick={onColdCrash}
              className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
            >
              {t('fermentation.coldCrash')}
            </button>
          )}
          {device.config?.temperature && (
            <button
              onClick={onTurnOff}
              className="rounded-lg bg-gray-600 px-4 py-2 text-white transition-colors hover:bg-gray-700"
            >
              {t('fermentation.turnOff')}
            </button>
          )}
          {/* Plato alert button - only shown during fermentation and if spindle is present */}
          {device.config?.temperature && spindle && (
            <button
              onClick={onPlatoAlert}
              className="rounded-lg border border-gray-300 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-100"
            >
              {t('fermentation.setPlatoAlert')}
            </button>
          )}
        </div>
        {/* Statistics section */}
        {device.config?.temperature && <StatisticsSection device={device} spindle={spindle} />}
      </div>

      {/* Brewtool Connection Modal */}
      <BrewtoolConnectionModal
        isOpen={showBrewtoolModal}
        onClose={() => setShowBrewtoolModal(false)}
        onConnect={handleModalConnect}
        onSave={handleModalSave}
      />
    </div>
  )
}
export default DeviceDetail
