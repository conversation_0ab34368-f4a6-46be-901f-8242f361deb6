import { ArrowDownIcon, ArrowUpIcon, DocumentTextIcon } from '@heroicons/react/24/solid'
import { formatDistance } from 'date-fns'
import { de } from 'date-fns/locale'
import { TargetIcon, ThermometerIcon } from 'lucide-react'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { formatTemperature } from 'helpers/format'
import { Brewtool } from 'services/BrewtoolService'
import { useTemperatureStatistic } from 'services/FermentationService'
import { twMerge } from 'tailwind-merge'
import useCalculateDataForLastHour from 'hooks/Fermentation/useCalculateDataForLastHour'

const TemperatureDisplay = ({ brewtool }: { brewtool: Brewtool }) => {
  const { t } = useTranslation()

  const { data } = useTemperatureStatistic(brewtool.id)

  const { changeType, changesForLastHour: temperatureChangesForLastHour } =
    useCalculateDataForLastHour(data, 'temperature')

  const shouldBe: 'minus' | 'plus' | 'neutral' = useMemo(() => {
    if (!brewtool.config?.temperature || !brewtool.statistic?.temperature) {
      return 'neutral'
    }
    if (brewtool.statistic?.temperature < brewtool.config?.temperature) {
      return 'plus'
    } else {
      return 'minus'
    }
  }, [brewtool])

  const getTextColor = () => {
    if (shouldBe === 'neutral') {
      return 'text-yellow-600'
    } else if (shouldBe === 'plus') {
      return changeType === 'increase' ? 'text-green-600' : 'text-red-600'
    } else {
      return changeType === 'increase' ? 'text-red-600' : 'text-green-600'
    }
  }

  const getIconColor = () => {
    if (shouldBe === 'neutral') {
      return 'text-yellow-500'
    } else if (shouldBe === 'plus') {
      return changeType === 'increase' ? 'text-green-500' : 'text-red-500'
    } else {
      return changeType === 'increase' ? 'text-red-500' : 'text-green-500'
    }
  }

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center">
      {/* Current temperature - larger display */}
      <div className="flex-1 rounded-lg bg-gray-50 p-4">
        <div className="mb-1 flex items-center">
          <ThermometerIcon size={18} className="mr-2 text-red-500" />
          <h3 className="text-sm font-medium text-gray-500">
            {t('fermentation.currentTemperature')}
          </h3>
        </div>
        <div className="flex items-center">
          <p className="text-3xl font-bold text-gray-800 md:text-4xl">
            {formatTemperature(brewtool.statistic?.temperature)}
          </p>
          {temperatureChangesForLastHour && (
            <p
              className={twMerge(getTextColor(), 'ml-2 flex items-baseline text-sm font-semibold')}
              title={t('fermentation.temperatureChangeLastHour', {
                value: temperatureChangesForLastHour,
              })}
            >
              {changeType === 'increase' ? (
                <ArrowUpIcon
                  aria-hidden="true"
                  className={twMerge(getIconColor(), 'size-5 shrink-0 self-center')}
                />
              ) : (
                <ArrowDownIcon
                  aria-hidden="true"
                  className={twMerge(getIconColor(), 'size-5 shrink-0 self-center')}
                />
              )}

              <span className="sr-only">
                {' '}
                {changeType === 'increase' ? 'Increased' : 'Decreased'} by{' '}
              </span>
              {temperatureChangesForLastHour}
            </p>
          )}
        </div>
      </div>
      {/* Target temperature - only shown if there is a target */}
      {brewtool.config?.temperature && (
        <div className="flex-1 rounded-lg bg-gray-50 p-4">
          <div className="mb-1 flex items-center">
            <TargetIcon size={18} className="mr-2 text-blue-500" />
            <h3 className="text-sm font-medium text-gray-500">
              {t('fermentation.targetTemperature')}
            </h3>
          </div>
          <p className="text-2xl font-medium text-gray-700 md:text-3xl">
            {formatTemperature(brewtool.config?.temperature)}
          </p>
          {brewtool.config?.startDate && (
            <p className="mt-2 text-sm text-gray-500">
              {formatDistance(new Date(brewtool.config?.startDate), new Date(), {
                addSuffix: true,
                locale: de,
              })}
            </p>
          )}
        </div>
      )}
      {brewtool.config?.recipeName && (
        <div className="flex-1 rounded-lg bg-gray-50 p-4">
          <div className="mb-1 flex items-center">
            <DocumentTextIcon className="mr-2 size-5 text-orange-500" />
            <h3 className="text-sm font-medium text-gray-500">{t('fermentation.recipeName')}</h3>
          </div>
          <p className="text-2xl font-medium text-gray-700 md:text-3xl">
            {brewtool.config.recipeName}
          </p>
        </div>
      )}
    </div>
  )
}
export default TemperatureDisplay
