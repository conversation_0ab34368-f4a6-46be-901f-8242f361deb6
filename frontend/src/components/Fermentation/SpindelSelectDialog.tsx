import { Device } from '@prisma/client'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

import { createSuccess } from 'hooks/useSimpleNotification'
import { Brewtool } from 'services/BrewtoolService'
import { DeviceType, useConnectDevice, useGetDevicesByType } from 'services/DeviceService'

import Button from 'components/Common/Button'

type SpindelSelectDialogProps = {
  onClose: () => void
  onSubmit: (deviceId: number) => void
  brewtool: Brewtool
}

const SpindelSelectDialog = ({ onClose, onSubmit, brewtool }: SpindelSelectDialogProps) => {
  const { t } = useTranslation()
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)

  const { data: devices, isLoading } = useGetDevicesByType(DeviceType.ISPINDEL)

  const { mutate: connectDevice } = useConnectDevice()

  const handleConnect = async () => {
    if (!selectedDevice) return

    try {
      connectDevice({ deviceId: selectedDevice.id, deviceConnectedId: brewtool.id })

      createSuccess(t('fermentation.spindleConnected'))
      onSubmit(selectedDevice.id)
    } catch (error) {
      console.error('Error connecting spindle:', error)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg bg-white p-6">
        <h2 className="mb-4 text-xl font-semibold">{t('fermentation.selectSpindle')}</h2>

        {isLoading ? (
          <div className="py-4 text-center">{t('common.loading')}</div>
        ) : devices && devices.length > 0 ? (
          <div className="mb-4 max-h-60 overflow-y-auto">
            <ul className="divide-y divide-gray-200">
              {devices.map(device => (
                <li key={device.id} className="py-2">
                  <button
                    className={`w-full rounded-md p-2 text-left ${
                      selectedDevice?.id === device.id
                        ? 'bg-blue-500 text-white'
                        : 'hover:bg-gray-100'
                    }`}
                    onClick={() => setSelectedDevice(device)}
                  >
                    <p className="font-medium">{device.name}</p>
                    <p
                      className={`text-sm ${
                        selectedDevice?.id === device.id ? ' text-gray-100' : 'text-gray-500'
                      }`}
                    >
                      {device.deviceConnected
                        ? t('fermentation.spindelAlreadyConnected', { deviceName: device.name })
                        : t('fermentation.selectableSpindle')}
                    </p>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="py-4 text-center text-gray-500">
            {t('fermentation.noAvailableSpindles')}
          </div>
        )}

        <div className="mt-4 flex justify-end gap-2">
          <Button onClick={onClose} style="secondary">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleConnect} disabled={!selectedDevice || isLoading}>
            {t('common.connect')}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default SpindelSelectDialog
