import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'

import { formatTemperature } from 'helpers/format'
import useDeviceStatus from 'hooks/Fermentation/useDeviceStatus'
import { Brewtool } from 'services/BrewtoolService'

import StatusBadge from 'components/Fermentation/StatusBadge'

const DeviceCard = ({ device }: { device: Brewtool }) => {
  const { t } = useTranslation()

  const status = useDeviceStatus(device)

  return (
    <Link to={`/fermentation/device/${device.id}`} className="block">
      <div className="rounded-lg bg-white p-4 shadow transition-shadow hover:shadow-md">
        <div className="flex items-start justify-between">
          <h2 className="truncate text-lg font-semibold text-gray-800" title={device.name}>
            {device.name || t('fermentation.unnamedDevice')}
          </h2>
          <StatusBadge status={status} />
        </div>
        <div className="mt-3 space-y-2">
          <div className="flex justify-between">
            <span className="text-gray-600">{t('fermentation.currentTemperature')}:</span>
            <span className="font-medium">{formatTemperature(device.statistic?.temperature)}</span>
          </div>
          {device.config?.temperature && (
            <div className="flex justify-between">
              <span className="text-gray-600">{t('fermentation.targetTemperature')}:</span>
              <span>{formatTemperature(device.config.temperature)}</span>
            </div>
          )}
          {device.plato && (
            <div className="flex justify-between">
              <span className="text-gray-600">{t('fermentation.plato')}:</span>
              <span>{device.plato}°P</span>
            </div>
          )}
        </div>
      </div>
    </Link>
  )
}
export default DeviceCard
