import { XIcon } from 'lucide-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

type FermentationDialogProps = {
  onClose: () => void
  onSubmit: (data: { title: string; targetTemp: string }) => void
  initialTitle?: string
}

const FermentationDialog = ({ onClose, onSubmit, initialTitle = '' }: FermentationDialogProps) => {
  const { t } = useTranslation()
  const [title, setTitle] = useState(initialTitle)
  const [targetTemp, setTargetTemp] = useState('19.0')
  const handleSubmit = e => {
    e.preventDefault()
    onSubmit({
      title,
      targetTemp,
    })
  }
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="w-full max-w-md rounded-lg bg-white shadow-lg">
        <div className="flex items-center justify-between border-b p-4">
          <h2 className="text-lg font-semibold">{t('fermentation.startFermentation')}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XIcon size={20} />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-4">
          <div className="mb-4">
            <label htmlFor="targetTemp" className="mb-1 block font-medium text-gray-700">
              {t('fermentation.targetTemperature')} (°C)
            </label>
            <input
              type="number"
              id="targetTemp"
              value={targetTemp}
              onChange={e => setTargetTemp(e.target.value)}
              className="w-full rounded-lg border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              step="0.1"
              min="0"
              max="40"
              required
            />
          </div>
          <div className="mb-6">
            <label htmlFor="title" className="mb-1 block font-medium text-gray-700">
              {t('fermentation.recipeName')}
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={e => setTitle(e.target.value)}
              className="w-full rounded-lg border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g. Indian Pale Ale"
              required={false}
            />
          </div>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700"
            >
              {t('common.start')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
export default FermentationDialog
