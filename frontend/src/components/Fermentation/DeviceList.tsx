import { useTranslation } from 'react-i18next'

import { Brewtool } from 'services/BrewtoolService'

import DeviceCard from 'components/Fermentation/DeviceCard'

const DeviceList = ({ devices }: { devices?: Brewtool[] }) => {
  const { t } = useTranslation()

  if (!devices || devices.length === 0) {
    return (
      <div className="rounded-lg bg-white py-10 text-center shadow">
        <p className="text-gray-600">{t('fermentation.noDevicesFound')}</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {devices.map(device => (
        <DeviceCard key={device.id} device={device} />
      ))}
    </div>
  )
}
export default DeviceList
