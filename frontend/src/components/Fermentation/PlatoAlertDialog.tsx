import { XIcon } from 'lucide-react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'

const PlatoAlertDialog = ({ onClose, onSubmit, currentPlato }) => {
  const { t } = useTranslation()
  const [platoThreshold, setPlatoThreshold] = useState(
    currentPlato ? (currentPlato - 1).toFixed(1) : '10.0'
  )
  const handleSubmit = e => {
    e.preventDefault()
    onSubmit(parseFloat(platoThreshold))
  }
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
      <div className="w-full max-w-md rounded-lg bg-white shadow-lg">
        <div className="flex items-center justify-between border-b p-4">
          <h2 className="text-lg font-semibold">{t('fermentation.setPlatoAlertTitle')}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XIcon size={20} />
          </button>
        </div>
        <form onSubmit={handleSubmit} className="p-4">
          <div className="mb-4">
            <p className="mb-2 text-gray-600">{t('fermentation.platoAlertDescription')}</p>
            <label htmlFor="platoThreshold" className="mb-1 block font-medium text-gray-700">
              {t('fermentation.platoThreshold')}
            </label>
            <input
              type="number"
              id="platoThreshold"
              value={platoThreshold}
              onChange={e => setPlatoThreshold(e.target.value)}
              className="w-full rounded-lg border px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              step="0.1"
              min="0"
              max={currentPlato || 20}
              required
            />
            {currentPlato && (
              <p className="mt-1 text-sm text-gray-500">
                {t('fermentation.currentPlato')}: {currentPlato}°P
              </p>
            )}
          </div>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100"
            >
              {t('common.cancel')}
            </button>
            <button
              type="submit"
              className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              {t('common.save')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
export default PlatoAlertDialog
