import { Device } from '@prisma/client'
import { formatDistance } from 'date-fns'
import { de } from 'date-fns/locale'
import { DropletIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

import { formatGravity } from 'helpers/format'
import { useGetSpindelStatistic, useGetSpindelStatistics } from 'services/Device/SpindelService'

import Button from 'components/Common/Button'
import { ArrowDownIcon, ArrowUpIcon } from '@heroicons/react/24/solid'
import { twMerge } from 'tailwind-merge'
import useCalculateDataForLastHour from 'hooks/Fermentation/useCalculateDataForLastHour'

type SpindelDisplayProps = {
  spindle: Device | null | undefined
  onConnectSpindle: () => void
}

const SpindelDisplay = ({ spindle, onConnectSpindle }: SpindelDisplayProps) => {
  const { t } = useTranslation()

  const { data: statistic } = spindle?.id ? useGetSpindelStatistic(spindle?.id) : { data: {} }
  const { data: spindleData } = useGetSpindelStatistics(spindle?.id ?? 0)

  const { changeType, changesForLastHour } = useCalculateDataForLastHour(
    spindleData ?? [],
    'gravity'
  )

  return (
    <div className="mb-6 rounded-lg bg-gray-50 p-4">
      <div className="mb-2 flex items-center">
        <DropletIcon size={18} className="mr-2 text-blue-500" />
        <h3 className="font-medium">{t('fermentation.spindleInformation')}</h3>
      </div>

      {spindle != null ? (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div>
            <p className="text-sm text-gray-500">{t('fermentation.plato')}</p>
            <div className="flex items-center">
              <p className="font-semibold">{formatGravity(statistic?.gravity)}</p>
              {changesForLastHour && (
                <p
                  className={twMerge(
                    changeType === 'decrease' ? 'text-green-600' : 'text-red-600',
                    'ml-2 flex items-baseline text-sm font-semibold'
                  )}
                  title={t('fermentation.gravityChangeLastHour', {
                    value: changesForLastHour,
                  })}
                >
                  {changeType === 'increase' ? (
                    <ArrowUpIcon
                      aria-hidden="true"
                      className="size-5 shrink-0 self-center text-red-500"
                    />
                  ) : (
                    <ArrowDownIcon
                      aria-hidden="true"
                      className="size-5 shrink-0 self-center text-green-500"
                    />
                  )}

                  <span className="sr-only">
                    {' '}
                    {changeType === 'increase' ? 'Increased' : 'Decreased'} by{' '}
                  </span>
                  {changesForLastHour}
                </p>
              )}
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">{t('fermentation.spindleName')}</p>
            <p className="text-gray-700">{spindle?.name || t('fermentation.notAvailable')}</p>
            {statistic?.createdAt && (
              <p className="text-sm text-gray-500">
                {formatDistance(new Date(statistic?.createdAt), new Date(), {
                  addSuffix: true,
                  locale: de,
                })}
              </p>
            )}
          </div>
        </div>
      ) : (
        <div>
          <p className="text-gray-500">{t('fermentation.noSpindleConnected')}</p>
          <Button onClick={onConnectSpindle} className="mt-2">
            {t('fermentation.connectSpindle')}
          </Button>
        </div>
      )}
    </div>
  )
}

export default SpindelDisplay
