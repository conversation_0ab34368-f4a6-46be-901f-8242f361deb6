import { SnowflakeIcon, WifiOffIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

import { DeviceStatus } from 'hooks/Fermentation/useDeviceStatus'

type StatusProps = {
  status: DeviceStatus
  large?: boolean
}

const StatusBadge = ({ status, large = false }: StatusProps) => {
  const { t } = useTranslation()

  let bgColor, textColor, label, icon
  switch (status) {
    case 'fermentation':
      bgColor = 'bg-green-100'
      textColor = 'text-green-800'
      label = t('fermentation.name')
      break
    case 'coldCrash':
      bgColor = 'bg-blue-100'
      textColor = 'text-blue-800'
      icon = <SnowflakeIcon className="mr-1 inline" size={large ? 16 : 12} />
      label = t('fermentation.coldCrash')
      break
    case 'standby':
      bgColor = 'bg-gray-100'
      textColor = 'text-gray-800'
      label = t('common.standBy')
      break
    case 'offline':
    default:
      bgColor = 'bg-red-100'
      textColor = 'text-red-800'
      label = t('common.offline')
      icon = <WifiOffIcon className="mr-1 inline" size={large ? 16 : 12} />
      break
  }
  const sizeClasses = large ? 'px-3 py-1.5 text-sm' : 'px-2 py-1 text-xs'
  return (
    <span
      className={`${bgColor} ${textColor} ${sizeClasses} flex items-center rounded-full font-medium`}
    >
      {icon}
      {label}
    </span>
  )
}
export default StatusBadge
