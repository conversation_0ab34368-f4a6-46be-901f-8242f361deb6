import { Transition } from '@headlessui/react'
import { BellIcon, CheckCircleIcon } from '@heroicons/react/24/solid'
import { useMemo, useState } from 'react'
import { Fragment } from 'react/jsx-runtime'

import { markNotificationReaded, useGetNotifications } from 'services/NotificationService'

export default function NotificationPopUp() {
  const [showNotification, setShowNotification] = useState<boolean>(false)

  const { data: notifications } = useGetNotifications()

  const { mutate: markNotificationAsReaded } = markNotificationReaded()

  const openNotifications = () => {
    setShowNotification(!showNotification)
    notifications?.forEach(item => {
      if (!item.read) {
        markNotificationAsReaded({ notificationId: item.id })
      }
    })
  }

  const countUnreadNotifications = useMemo(
    () => notifications?.filter(n => !n.read).length ?? 0,
    [notifications]
  )

  return (
    <>
      <button
        type="button"
        onClick={() => openNotifications()}
        className="relative rounded-full bg-gray-800 p-1 text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800"
      >
        <span className="absolute -inset-1.5" />
        <span className="sr-only">View notifications</span>
        <BellIcon className="h-6 w-6" aria-hidden="true" />
        {countUnreadNotifications > 0 && (
          <div className="absolute bottom-0 right-0 block size-4 rounded-full bg-red-500 text-xs text-white">
            {countUnreadNotifications}
          </div>
        )}
      </button>

      <div className="relative ml-3">
        <Transition
          show={showNotification}
          as={Fragment}
          enter="transition ease-out duration-100"
          enterFrom="transform opacity-0 scale-95"
          enterTo="transform opacity-100 scale-100"
          leave="transition ease-in duration-75"
          leaveFrom="transform opacity-100 scale-100"
          leaveTo="transform opacity-0 scale-95"
        >
          <div className="absolute right-0 z-10 mt-2 w-96 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
            {notifications?.map(item => (
              <div className="p-4" key={item.id}>
                <div className="flex items-center justify-center">
                  <div className="shrink-0">
                    <CheckCircleIcon aria-hidden="true" className="size-6 text-green-400" />
                  </div>
                  <div className="ml-3 w-0 flex-1 pt-0.5">
                    <p className="text-sm font-medium text-gray-900">{item.title}</p>
                    <p className="mt-1 text-sm text-gray-500">{item.text}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Transition>
      </div>
    </>
  )
}
