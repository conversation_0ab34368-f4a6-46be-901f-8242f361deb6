import { UserCircleIcon, ViewfinderCircleIcon } from '@heroicons/react/20/solid'
import { iconUrl } from 'constants'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { twMerge } from 'tailwind-merge'

import { createBrewingFromRecipe } from 'helpers/createBrewingFromRecipe'
import { post, useHttpNotifications } from 'helpers/http'
import useSimpleNotification from 'hooks/useSimpleNotification'
import { activateFermentation } from 'services/FermentationService'
import { RecipeRequestResponse } from 'services/RecipeService'

import Card from 'components/Common/Card'
import HeaderButtonMenu from 'components/Common/HeaderButtonMenu'
import HopsTable from 'components/RecipeView/HopsTable'
import IngredientsCard from 'components/RecipeView/IngredientsCard'
import YeastTable from 'components/RecipeView/YeastTable'

export default function BaseView({
  recipe,
  onUpdate,
}: Readonly<{
  recipe: RecipeRequestResponse
  onUpdate: () => void
}>) {
  const { t } = useTranslation()
  const { createSuccess, createError } = useSimpleNotification()
  const resolveResponse = useHttpNotifications()
  const navigate = useNavigate()

  const [fermentationTemp, setFermentationTemp] = useState<number | null>(null)

  const startFermenation = async () => {
    if (!fermentationTemp) {
      createError(t('recipe.noFermentationTemperatureExist'))
      return
    }

    await activateFermentation({
      temperature: fermentationTemp,
      recipeId: recipe.id,
      sendPush: false,
    })
    createSuccess(t('recipe.messageFermentationStarted', { temperature: fermentationTemp }))
  }

  const startBrewing = async () => {
    if (!fermentationTemp) {
      createError(t('recipe.noFermentationTemperatureExist'))
      return
    }

    const brewing = createBrewingFromRecipe(recipe, fermentationTemp)

    brewing.startTime = new Date()

    const response = (await resolveResponse(() =>
      post('brewing', {
        ...brewing,
        mashSteps: { create: brewing.mashSteps },
        hops: { create: brewing.hops },
        yeasts: { create: brewing.yeasts },
        fermentables: { create: brewing.fermentables },
      })
    )) as Response

    const brewItem = await response.json()

    navigate(`/brewing/${brewItem.id}`)
  }

  return (
    <main>
      <header className="relative isolate pt-16">
        <div className="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
          <div className="absolute left-16 top-full -mt-16 transform-gpu opacity-50 blur-3xl xl:left-1/2 xl:-ml-80">
            <div
              className="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]"
              style={{
                clipPath:
                  'polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)',
              }}
            />
          </div>
          <div className="absolute inset-x-0 bottom-0 h-px bg-gray-900/5" />
        </div>
        <div className="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
          <div className="mx-auto flex max-w-2xl items-center justify-between gap-x-8 lg:mx-0 lg:max-w-none">
            <div className="flex items-center gap-x-6">
              <img
                src={iconUrl}
                alt=""
                className="h-16 w-16 flex-none rounded-full ring-1 ring-gray-900/10"
              />
              <h1>
                <div className="text-sm leading-6 text-gray-500">{t('recipe.single')}</div>
                <div className="mt-1 text-base font-semibold leading-6 text-gray-900">
                  {recipe.name}
                </div>
              </h1>
            </div>
            <HeaderButtonMenu
              main={{ children: t('recipe.cook'), onClick: startBrewing }}
              items={[
                {
                  children: t('recipe.fermentate'),
                  onClick: startFermenation,
                },
                {
                  children: t('common.edit'),
                  to: '#',
                },
              ]}
            />
          </div>
        </div>
      </header>

      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="mx-auto grid max-w-2xl grid-cols-1 grid-rows-1 items-start gap-x-8 gap-y-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          <div className="lg:col-start-3 lg:row-end-1">
            <h2 className="sr-only">Übersicht</h2>
            <div className="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
              <dl className="flex flex-wrap">
                <div className="mt-6 flex w-full flex-none gap-x-4 border-t border-gray-900/5 px-6 pt-6">
                  <dt className="flex-none">
                    <span className="sr-only">{t('recipe.brewer')}</span>
                    <UserCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                  </dt>
                  <dd className="text-sm font-medium leading-6 text-gray-900">{recipe.brewer}</dd>
                </div>
                {recipe.ibu && (
                  <div className="mt-4 flex w-full flex-none gap-x-4 px-6">
                    <dt className="flex-none">
                      <span className="sr-only">{t('recipe.ibu')}</span>
                      <ViewfinderCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                    </dt>
                    <dd className="text-sm leading-6 text-gray-500">
                      <time dateTime="2023-01-31">{recipe.ibu}</time>
                    </dd>
                  </div>
                )}
                {recipe.abv && (
                  <div className="mt-4 flex w-full flex-none gap-x-4 px-6">
                    <dt className="flex-none">
                      <span className="sr-only">{t('recipe.alcohol')}</span>
                      <ViewfinderCircleIcon className="h-6 w-5 text-gray-400" aria-hidden="true" />
                    </dt>
                    <dd className="text-sm leading-6 text-gray-500">{recipe.abv}</dd>
                  </div>
                )}
              </dl>
              <div className="mt-6 border-t border-gray-900/5 px-6 py-6">
                <a href="#" className="text-sm font-semibold leading-6 text-gray-900">
                  {t('recipe.downloadOrPrint')} <span aria-hidden="true">&rarr;</span>
                </a>
              </div>
            </div>
            <span className="text-xs text-gray-500">
              {t('recipe.createdAt', { date: recipe.createdAt })}
            </span>
          </div>

          <div className="grid gap-y-3 lg:col-span-2 lg:row-span-2 lg:row-end-2">
            <IngredientsCard recipe={recipe} />
            <Card title={t('recipe.boil')}>
              <ul role="list" className="space-y-6">
                {recipe.mashs[0].steps.map((step, idx) => (
                  <li key={step.id} className="relative flex gap-x-4">
                    <div
                      className={twMerge(
                        idx === recipe.mashs[0].steps.length - 1 ? 'h-6' : '-bottom-6',
                        'absolute left-0 top-0 flex w-6 justify-center'
                      )}
                    >
                      <div className="w-px bg-gray-200" />
                    </div>
                    <div className="relative flex h-6 w-6 flex-none items-center justify-center bg-white">
                      <div className="h-1.5 w-1.5 rounded-full bg-gray-100 ring-1 ring-gray-300" />
                    </div>
                    <p className="flex-auto py-0.5 text-sm leading-5 text-gray-500">
                      <span className="font-medium text-gray-900">{step?.name}</span>
                      {t('recipe.mashStep', {
                        temp: step.temperature,
                        time: step.time,
                        type: step.type,
                      })}
                    </p>
                  </li>
                ))}
              </ul>
            </Card>
            <Card title={t('recipe.hops')}>
              <HopsTable hops={recipe.hops} />
            </Card>
            <Card title={t('recipe.fermentation')}>
              <YeastTable
                yeasts={recipe.yeasts}
                onUpdate={onUpdate}
                setFermentationTemp={temperature => setFermentationTemp(temperature)}
              />
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
}
