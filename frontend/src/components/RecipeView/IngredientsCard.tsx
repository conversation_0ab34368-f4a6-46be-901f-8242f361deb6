import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

import { getTypeById } from 'hooks/useInventoryTypes'
import { RecipeRequestResponse } from 'services/RecipeService'

import Card from 'components/Common/Card'

export default function IngredientsCard(props: { recipe: RecipeRequestResponse }) {
  const { t } = useTranslation()

  const calculateAmountForKg = (amount: number) => {
    return amount.toFixed(2) + ' kg'
  }

  const calculateAmountForG = (amount: number) => {
    return amount.toFixed(2) + ' g'
  }

  const calculateProzentOfFermentable = (amount: number) => {
    return ((amount / fermentableAmountSummary) * 100).toFixed(2) + ' %'
  }

  const summarizedHops = useMemo(() => {
    if (!props.recipe.hops) {
      return []
    }

    const response = []
    for (const hop of props.recipe.hops) {
      const index = response.findIndex(item => item.name === hop.name)
      if (index === -1) {
        response.push({ ...hop, inventoryAmount: '' })
      } else {
        response[index].amount += hop.amount
      }
    }

    return response
  }, [props.recipe.hops])

  const fermentableAmountSummary = useMemo(
    () => props.recipe.fermentables.reduce((acc, fermentable) => acc + fermentable.amount, 0),
    [props.recipe.fermentables]
  )

  return (
    <Card title={t('recipe.ingredients')}>
      <div className="grid grid-cols-1 grid-rows-1 items-start gap-x-8 gap-y-8 text-sm leading-6 lg:mx-0 lg:max-w-none lg:grid-cols-3">
        <div className="">
          <h3 className="align-center flex">
            <div className="mr-3 h-5 w-5">{getTypeById('malt').icon}</div> {t('malt.title')}
          </h3>
          <ul role="list" className="divide-y divide-gray-100">
            {props.recipe.fermentables.map(fermentable => (
              <li key={fermentable.id} className="flex justify-between gap-x-6 py-2">
                <div className="flex min-w-0 gap-x-4">
                  <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">
                      {fermentable.name}
                    </p>
                    <p className="mt-1 truncate text-xs leading-5 text-gray-500">
                      {calculateProzentOfFermentable(fermentable.amount)}
                    </p>
                  </div>
                </div>
                <div className="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
                  <p className="mt-1 text-xs leading-5 text-gray-500">
                    {calculateAmountForKg(fermentable.amount)}
                  </p>
                </div>
              </li>
            ))}
          </ul>
        </div>
        <div className="">
          <h3 className="align-center flex">
            <div className="mr-3 h-5 w-5">{getTypeById('hop').icon}</div> {t('hop.title')}
          </h3>
          <ul role="list" className="divide-y divide-gray-100">
            {summarizedHops.map(hop => (
              <li key={hop.id} className="flex justify-between gap-x-6 py-2">
                <div className="flex min-w-0 gap-x-4">
                  <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">
                      {hop.name} ({hop.alpha} %a)
                    </p>
                  </div>
                </div>
                <div className="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
                  <p className="mt-1 text-xs leading-5 text-gray-500">
                    {calculateAmountForG(hop.amount)}
                  </p>
                </div>
              </li>
            ))}
          </ul>
        </div>
        <div className="">
          <h3 className="align-center flex">
            <div className="mr-3 h-5 w-5">{getTypeById('yeast').icon}</div> {t('yeast.title')}
          </h3>
          <ul role="list" className="divide-y divide-gray-100">
            {props.recipe.yeasts.map(yeast => (
              <li key={yeast.id} className="flex justify-between gap-x-6 py-2">
                <div className="flex min-w-0 gap-x-4">
                  <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">{yeast.name}</p>
                  </div>
                </div>
                <div className="hidden shrink-0 sm:flex sm:flex-col sm:items-end">
                  <p className="mt-1 text-xs leading-5 text-gray-500">{yeast.amount ?? ''}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </Card>
  )
}
