import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid'
import type { InventaryItem, RecipeYeast, SearchIndex } from '@prisma/client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { get, put, useHttpNotifications } from 'helpers/http'
import useEditDialog from 'hooks/Dialog/useEditDialog'

import EditYeastForm from 'components/Recipe/EditYeastForm'

type Yeast = RecipeYeast & { inInventory?: boolean; optimalTemperature: number }

export default function YeastTable(props: {
  yeasts: RecipeYeast[]
  onUpdate: () => void
  setFermentationTemp: (temp: number) => void
}) {
  const { t } = useTranslation()
  const [yeasts, setYeasts] = useState<Yeast[]>([])
  const resolve = useHttpNotifications()

  const { openEditDialog } = useEditDialog<RecipeYeast>(
    t('yeast.title'),
    EditYeastForm,
    async (data, initItem) => {
      if (!initItem) {
        return
      }

      resolve(() => put(`recipe/${initItem.recipeId}/yeast/${initItem.id}`, data))
      props.onUpdate()
    }
  )

  async function loadHopsPerLevenshtein(yeast: RecipeYeast) {
    const res = await get<SearchIndex[]>(`yeast/levenshtein?q=${yeast.name}`)
    if (res && res.length === 1) {
      setYeasts(yeasts => {
        const index = yeasts.findIndex(item => item.id == yeast.id)
        if (index !== -1) {
          const temperature = Number(res[0].extraData?.temperature) ?? null
          yeasts[index].optimalTemperature = temperature
          if (temperature) {
            props.setFermentationTemp(temperature)
          }
        }
        return [...yeasts]
      })
    }
  }

  useEffect(() => {
    const loadYeast = async () => {
      if (props.yeasts) {
        const response = [...props.yeasts] as Yeast[]

        for (const yeast of response) {
          const data = await get<InventaryItem[]>(`inventory/search?q=${yeast.name}&type=yeast`)
          if (data && data.length > 0) {
            yeast.inInventory = true
          }

          const search = await get<SearchIndex[]>(`yeast/search?q=${yeast.name}`)
          if (search && search.length === 1) {
            yeast.optimalTemperature = Number(search[0].extraData?.temperature) ?? null
            if (yeast.optimalTemperature) {
              props.setFermentationTemp(yeast.optimalTemperature)
            }
          } else {
            loadHopsPerLevenshtein(yeast)
          }
        }

        setYeasts(response)
      }
    }
    loadYeast()
  }, [props.yeasts])

  return (
    <table className="w-full whitespace-nowrap text-left text-sm leading-6">
      <colgroup>
        <col className="w-full" />
        <col />
        <col />
        <col />
      </colgroup>
      <thead className="border-b border-gray-200 text-gray-900">
        <tr>
          <th scope="col" className="px-0 py-3 font-semibold">
            {t('yeast.title')}
          </th>
          <th scope="col" className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell">
            {t('recipe.optimalTemperature')}
          </th>
          <th scope="col" className="py-3 pl-8 pr-0 text-right font-semibold">
            {t('recipe.inInventory')}
          </th>
        </tr>
      </thead>
      <tbody>
        {yeasts?.map(item => (
          <tr key={item.id} className="border-b border-gray-100">
            <td className="max-w-0 px-0 py-5 align-top">
              <div className="truncate font-medium text-gray-900">{item.name}</div>
              {!item.optimalTemperature && (
                <button
                  className="truncate text-xs text-yellow-600 hover:text-yellow-800 hover:underline focus:outline-none"
                  onClick={() => openEditDialog(item)}
                >
                  {t('recipe.yeastNotFoundSelectAnother')}
                </button>
              )}
            </td>
            <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
              {item.optimalTemperature ? item.optimalTemperature + ' °C' : '-'}
            </td>
            <td className="py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700">
              {item.inInventory ? (
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              ) : (
                <XCircleIcon className="h-6 w-6 text-red-600" />
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}
