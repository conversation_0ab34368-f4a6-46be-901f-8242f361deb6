import type { RecipeHop } from '@prisma/client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

export default function HopsTable(props: { hops: RecipeHop[] }) {
  const { t } = useTranslation()
  const [hops, setHops] = useState<RecipeHop[]>([])

  useEffect(() => {
    if (!props.hops) {
      setHops([])
    }

    const response = [...props.hops].sort((a, b) => b.time - a.time)
    setHops(response)
  }, [props.hops])

  const showReadableAmount = (amount: number) => {
    if (!amount) {
      return ''
    } else if (amount.toFixed() == '0') {
      return (amount * 1000).toFixed(2) + ' g'
    }

    return amount + ' kg'
  }

  const showReadableBoilTime = (hop: RecipeHop, index: number) => {
    if (
      index > 0 &&
      hop.time === props.hops[index - 1].time &&
      hop.use === props.hops[index - 1].use
    ) {
      return ''
    }

    if (hop.use === 'First Wort') {
      return t('recipe.firstWort')
    } else if (hop.use === 'Dry Hop') {
      return t('recipe.dryHop')
    } else if (hop.time == 0) {
      return t('recipe.hopOnWhirlpool')
    } else {
      return t('recipe.hopOnBoil', { time: hop.time })
    }
  }

  // TODO: add a graphic with hops amount

  return (
    <table className="w-full whitespace-nowrap text-left text-sm leading-6">
      <colgroup>
        <col />
        <col />
        <col />
        <col />
      </colgroup>
      <thead className="border-b border-gray-200 text-gray-900">
        <tr>
          <th scope="col" className="px-0 py-3 font-semibold">
            {t('recipe.time')}
          </th>
          <th scope="col" className="px-0 py-3 font-semibold">
            {t('hop.title')}
          </th>
          <th scope="col" className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell">
            {t('inventory.amount')}
          </th>
          <th scope="col" className="hidden py-3 pl-8 pr-0 text-right font-semibold sm:table-cell">
            {t('hop.alpha')}
          </th>
        </tr>
      </thead>
      <tbody>
        {hops?.map((item, index) => (
          <tr key={item.id} className="border-b border-gray-100">
            <td className="px-0 py-5 align-top">
              <div className="truncate font-medium text-gray-900">
                {showReadableBoilTime(item, index)}
              </div>
            </td>
            <td className="px-0 py-5 align-top">
              <div className="truncate font-medium text-gray-900">{item.name}</div>
              <div className="truncate text-gray-500">{/* item.description */}</div>
            </td>
            <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
              {showReadableAmount(item.amount)}
            </td>
            <td className="hidden py-5 pl-8 pr-0 text-right align-top tabular-nums text-gray-700 sm:table-cell">
              {item.alpha + ' %'}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}
