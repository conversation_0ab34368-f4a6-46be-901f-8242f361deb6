import { PushSubscription } from '@prisma/client'
import * as platform from 'platform'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { del, useGet, useHttpNotifications } from 'helpers/http'
import { createPushNotifications, hasPushNotificationPermission } from 'helpers/onPushNotifications'
import useDeleteDialog from 'hooks/Dialog/useDeleteDialog'

import Section from 'components/Settings/Section'

export default function PushSubscriptionSection() {
  const { t } = useTranslation()

  const subscription = useGet<PushSubscription[]>('subscription')
  const resolveResponse = useHttpNotifications()
  const [subscriptions, setSubscriptions] = useState<
    (PushSubscription & { browser: string; os: string; product: string })[]
  >([])

  const openDeleteDialog = useDeleteDialog(async (data: PushSubscription) => {
    await resolveResponse(async () => await del(`subscription/${data.id}`))
    subscription.refetch()
  })

  useEffect(() => {
    if (subscription.data) {
      setSubscriptions(
        subscription.data.map(sub => {
          const info = platform.parse(sub.userAgent)

          return {
            ...sub,
            browser: info.name,
            os: info.os.toString(),
            product: info.product,
          }
        })
      )
    }
  }, [subscription.data])

  const onRecreate = async () => {
    createPushNotifications()
  }

  return (
    <Section title={t('user.pushSubscriptions')} helpText={t('user.pushSubscriptionsHelp')}>
      {hasPushNotificationPermission() && (
        <div className="py-3">
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="sm:flex sm:items-start sm:justify-between">
                <div>
                  <h3 className="text-base font-semibold leading-6 text-gray-900">
                    {t('user.subscriptionSelf')}
                  </h3>
                  <div className="mt-2 max-w-xl text-sm text-gray-500">
                    <p>{t('user.subscriptionSelfHelp')}</p>
                  </div>
                </div>
                <div className="mt-5 sm:ml-6 sm:mt-0 sm:flex sm:flex-shrink-0 sm:items-center">
                  <button
                    type="button"
                    onClick={() => onRecreate()}
                    className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    {t('common.recreate')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <table className="mt-3 min-w-full divide-y divide-gray-300">
        <thead>
          <tr>
            <th
              scope="col"
              className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
            >
              {t('user.browser')}
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              {t('user.os')}
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              {t('user.product')}
            </th>
            <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
              {t('user.createdAt')}
            </th>
            <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
              <span className="sr-only">{t('common.delete')}</span>
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {subscriptions.map(sub => (
            <tr key={sub.id}>
              <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                {sub.browser}
              </td>
              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{sub.os}</td>
              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{sub.product}</td>
              <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{sub.createdAt}</td>
              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                <button
                  onClick={() => openDeleteDialog(sub)}
                  className="text-indigo-600 hover:text-indigo-900"
                >
                  {t('common.delete')}
                  <span className="sr-only"> {/* sub.name */}</span>
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </Section>
  )
}
