import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { twMerge } from 'tailwind-merge'

import Loading from 'components/Common/Loading'

export default function IntegrationStatus(props: { request: any }) {
  const { t } = useTranslation()

  if (props.request.isLoading) {
    return <Loading />
  }

  return (
    <div className="mt-3 inline-flex items-center gap-x-3 rounded-md bg-white px-6 py-3">
      <div
        className={twMerge(
          props.request.data ? 'bg-green-400/10 text-green-400' : 'bg-gray-100/10 text-gray-500',
          'flex-none rounded-full p-1'
        )}
      >
        {props.request.data ? (
          <CheckCircleIcon className="h-6 w-6" />
        ) : (
          <XCircleIcon className="h-6 w-6" />
        )}
      </div>
      <h2 className="min-w-0 truncate text-sm font-semibold leading-6 text-gray-900">
        {props.request.data ? t('user.integrationActive') : t('user.integrationDeactivated')}
      </h2>
    </div>
  )
}
