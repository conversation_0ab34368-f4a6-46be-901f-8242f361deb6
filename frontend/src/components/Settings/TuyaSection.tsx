import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { post, useGet, useHttpNotifications } from 'helpers/http'

import Button from 'components/Common/Button'
import TextInput from 'components/Common/Form/TextInput'
import IntegrationStatus from 'components/Settings/IntegrationStatus'
import Section from 'components/Settings/Section'

export default function TuyaSection() {
  const { t } = useTranslation()

  const account = useGet('integration/tuya/account')
  const resolveResponse = useHttpNotifications()

  const methods = useForm({
    resolver: zodResolver(z.object({ accessKey: z.string(), secretKey: z.string() })),
    defaultValues: account.data ?? {
      accessKey: '',
      secretKey: '',
    },
  })

  const onSave = data => {
    resolveResponse(() => post('integration/tuya/account', data))
  }

  return (
    <Section title={t('user.tuya.sectionTitle')} helpText={t('user.tuya.help')}>
      <FormProvider {...methods}>
        <IntegrationStatus request={account} />

        <form className="md:col-span-2" onSubmit={methods.handleSubmit(onSave)}>
          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
            <TextInput
              className="col-span-full"
              label={t('user.tuya.accessKey')}
              name="accessKey"
            />

            <TextInput
              className="col-span-full"
              label={t('user.tuya.secretKey')}
              name="secretKey"
              type="password"
            />
          </div>

          <div className="mt-8 flex">
            <Button type="submit">{t('common.save')}</Button>
          </div>
        </form>
      </FormProvider>
    </Section>
  )
}
