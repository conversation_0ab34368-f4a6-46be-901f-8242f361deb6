import { zod<PERSON><PERSON><PERSON><PERSON> } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import Button from 'components/Common/Button'
import TextInput from 'components/Common/Form/TextInput'
import Section from 'components/Settings/Section'

export default function ChangePasswordSection() {
  const { t } = useTranslation()

  const methods = useForm({
    resolver: zodResolver(z.object({ confirm: z.string(), password: z.string() })),
    defaultValues: {
      password: '',
      confirm: '',
    },
  })

  const onSaveAccount = data => {
    console.log(data)
  }

  return (
    <Section title={t('user.changePassword')} helpText={t('user.changePasswordHelp')}>
      <FormProvider {...methods}>
        <form className="md:col-span-2" onSubmit={methods.handleSubmit(onSaveAccount)}>
          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
            <TextInput
              className="col-span-full"
              type="password"
              label={t('common.password')}
              name="password"
            />
            <TextInput
              className="col-span-full"
              type="password"
              label={t('user.confirmPassword')}
              name="confirm"
            />
          </div>

          <div className="mt-8 flex">
            <Button type="submit">{t('common.save')}</Button>
          </div>
        </form>
      </FormProvider>
    </Section>
  )
}
