import { QRCodeSVG } from 'qrcode.react'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'

import { post, useHttpNotifications } from 'helpers/http'
import { FormComponentProps } from 'hooks/Dialog/useEditDialog'

import Button from 'components/Common/Button'
export default function SmartHomeQrCodeDialog(
  props: FormComponentProps<{ userId: string; qrcode: string }>
): ReactElement {
  console.log(props)

  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()

  const onRegisterFinished = async () => {
    if (props.data === undefined) {
      return
    }

    const res = await post('integration/smartlife/register', props.data)

    const result = await res.json()

    if (result.success === false) {
      console.error('Failed to register SmartLife account: Response Success is false')
      return
    }

    resolveResponse(async () => res)

    props.onSubmit({ userId: props.data.userId, result })
  }

  return (
    <div>
      <p>{t('user.smartlife.registerHelpText')}</p>
      {props.data?.qrcode && (
        <div className="my-5 flex flex-col justify-center">
          <div className="my-5 flex justify-center">
            <QRCodeSVG value={'tuyaSmart--qrLogin?token=' + props.data.qrcode} />
          </div>
          <div className="mt-5 flex justify-center">
            <Button onClick={onRegisterFinished}>{t('smartlife.registerFinished')}</Button>
          </div>
        </div>
      )}
    </div>
  )
}

// {"success":true,"tid":"55bd581ceb6711eeb631eadbf7f8866e","t":*************,"result":{"qrcode":"EU1711453954779xMKZoWpFHNncxWlToyRYfpmUhi171145425477903"}}
