export default function Section(props: {
  title: string
  helpText: string
  list?: {
    title: string
    value: string
    button?: string
  }[]
  children?: React.ReactNode
}) {
  return (
    <div>
      <h2 className="text-base font-semibold leading-7 text-gray-900">{props.title}</h2>
      <p className="mt-1 text-sm leading-6 text-gray-500">{props.helpText}</p>

      {/* {props.list && props.list.length > 0 && (
        <ul
          role="list"
          className="mt-6 divide-y divide-gray-100 border-t border-gray-200 text-sm leading-6"
        >
          {props.list.map(item => (
            <li key={item.text} className="flex justify-between gap-x-6 py-6">
              <div className="font-medium text-gray-900">{item.value}</div>
              <button type="button" className="font-semibold text-indigo-600 hover:text-indigo-500">
                {item.button}
              </button>
            </li>
          ))}
        </ul>
      )} */}

      {props.list && props.list.length > 0 && (
        <dl className="mt-6 space-y-6 divide-y divide-gray-100 border-t border-gray-200 text-sm leading-6">
          {props.list.map(item => (
            <div key={item.value} className="pt-6 sm:flex">
              <dt className="font-medium text-gray-900 sm:w-64 sm:flex-none sm:pr-6">
                {item.title}
              </dt>
              <dd className="mt-1 flex justify-between gap-x-6 sm:mt-0 sm:flex-auto">
                <div className="text-gray-900">{item.value}</div>
                <button
                  type="button"
                  className="font-semibold text-indigo-600 hover:text-indigo-500"
                >
                  {item.button}
                </button>
              </dd>
            </div>
          ))}
        </dl>
      )}

      {props.children}

      {/* TODO: Link button<div className="flex border-t border-gray-100 pt-6">
        <button
          type="button"
          className="text-sm font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
        >
          <span aria-hidden="true">+</span> Add another bank
        </button>
      </div> */}
    </div>
  )
}
