import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { post, useGet } from 'helpers/http'
import useEditDialog from 'hooks/Dialog/useEditDialog'

import Button from 'components/Common/Button'
import TextInput from 'components/Common/Form/TextInput'
import IntegrationStatus from 'components/Settings/IntegrationStatus'
import Section from 'components/Settings/Section'
import SmartLifeQrCodeDialog from 'components/Settings/SmartLifeQrCodeDialog'

export default function SmartLifeSection() {
  const { t } = useTranslation()

  const account = useGet<{ userId: string }>('integration/smartlife/account')

  const { openEditDialog } = useEditDialog(
    t('user.smartlife.registerDialog'),
    SmartLifeQrCodeDialog,
    (data, initData) => {}
  )

  const methods = useForm({
    resolver: zodResolver(z.object({ userId: z.string() })),
    defaultValues: {
      userId: account.data?.userId ?? '',
    },
  })

  useEffect(() => {
    methods.setValue('userId', account.data?.userId ?? '')
  }, [account.data?.userId])

  const onUserIdAdded = async data => {
    console.log(data)

    const response = await post('integration/smartlife/qrcode', {
      userId: data.userId,
    })

    const qrCode = (await response.json()) as {
      success: boolean
      tid: string
      t: string
      result: {
        qrcode: string
      }
    }

    if (!qrCode.success) {
      console.error('Failed to get QR code: Response Success is false')
      return
    }

    openEditDialog({ ...data, qrcode: qrCode.result.qrcode })
  }

  return (
    <Section title={t('user.smartlife.sectionTitle')} helpText={t('user.smartlife.help')}>
      <FormProvider {...methods}>
        <IntegrationStatus request={account} />

        <form className="md:col-span-2" onSubmit={methods.handleSubmit(onUserIdAdded)}>
          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
            <TextInput className="col-span-full" label={t('user.smartlife.userId')} name="userId" />
          </div>

          <div className="mt-8 flex">
            <Button type="submit">{t('common.save')}</Button>
          </div>
        </form>
      </FormProvider>
    </Section>
  )
}
