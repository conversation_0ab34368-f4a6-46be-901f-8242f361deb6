import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'

import { post, useGet, useHttpNotifications } from 'helpers/http'
import useUser from 'hooks/useUser'

import Button from 'components/Common/Button'
import TextInput from 'components/Common/Form/TextInput'
import IntegrationStatus from 'components/Settings/IntegrationStatus'
import Section from 'components/Settings/Section'

export default function BraurekaSection() {
  const { t } = useTranslation()

  const user = useUser()

  const account = useGet('integration/braureka/account')
  const resolveResponse = useHttpNotifications()

  const methods = useForm({
    resolver: zodResolver(z.object({ username: z.string(), password: z.string() })),
    defaultValues: account.data ?? {
      username: user.email,
      password: '',
    },
  })

  const onSaveBaureka = data => {
    resolveResponse(() => post('integration/braureka/account', data))
  }

  return (
    <Section title={t('user.braureka.sectionTitle')} helpText={t('user.braureka.help')}>
      <FormProvider {...methods}>
        <IntegrationStatus request={account} />

        <form className="md:col-span-2" onSubmit={methods.handleSubmit(onSaveBaureka)}>
          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:max-w-xl sm:grid-cols-6">
            <TextInput className="col-span-full" label={t('common.username')} name="username" />

            <TextInput
              className="col-span-full"
              type="password"
              label={t('common.password')}
              name="password"
              placeholder="••••••••"
            />
          </div>

          <div className="mt-8 flex">
            <Button type="submit">{t('common.save')}</Button>
          </div>
        </form>
      </FormProvider>
    </Section>
  )
}
