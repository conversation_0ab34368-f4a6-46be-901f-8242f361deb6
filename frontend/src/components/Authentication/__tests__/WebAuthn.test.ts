import {
  isWebAuthnSupported,
  isPlatformAuthenticatorAvailable,
  registerWebAuthnCredential,
  authenticateWebAuthnCredential,
} from '../WebAuthn'

// Mock the global objects
const mockNavigator = {
  credentials: {
    create: jest.fn(),
    get: jest.fn(),
  },
}

const mockPublicKeyCredential = {
  isUserVerifyingPlatformAuthenticatorAvailable: jest.fn(),
}

// Mock the post function
jest.mock('helpers/http', () => ({
  post: jest.fn(),
}))

import { post } from 'helpers/http'
const mockPost = post as jest.MockedFunction<typeof post>

describe('WebAuthn Service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup global mocks
    Object.defineProperty(window, 'navigator', {
      value: mockNavigator,
      writable: true,
    })
    
    Object.defineProperty(window, 'PublicKeyCredential', {
      value: mockPublicKeyCredential,
      writable: true,
    })
  })

  describe('isWebAuthnSupported', () => {
    it('should return true when WebAuthn is supported', () => {
      const result = isWebAuthnSupported()
      expect(result).toBe(true)
    })

    it('should return false when WebAuthn is not supported', () => {
      Object.defineProperty(window, 'PublicKeyCredential', {
        value: undefined,
        writable: true,
      })

      const result = isWebAuthnSupported()
      expect(result).toBe(false)
    })
  })

  describe('isPlatformAuthenticatorAvailable', () => {
    it('should return true when platform authenticator is available', async () => {
      mockPublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable.mockResolvedValue(true)

      const result = await isPlatformAuthenticatorAvailable()
      expect(result).toBe(true)
    })

    it('should return false when platform authenticator is not available', async () => {
      mockPublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable.mockResolvedValue(false)

      const result = await isPlatformAuthenticatorAvailable()
      expect(result).toBe(false)
    })

    it('should return false when WebAuthn is not supported', async () => {
      Object.defineProperty(window, 'PublicKeyCredential', {
        value: undefined,
        writable: true,
      })

      const result = await isPlatformAuthenticatorAvailable()
      expect(result).toBe(false)
    })
  })

  describe('registerWebAuthnCredential', () => {
    const mockEmail = '<EMAIL>'
    const mockRegistrationOptions = {
      challenge: 'dGVzdC1jaGFsbGVuZ2U',
      rp: { name: 'Test App', id: 'localhost' },
      user: {
        id: 'dGVzdC11c2VyLWlk',
        name: mockEmail,
        displayName: 'Test User',
      },
      pubKeyCredParams: [{ type: 'public-key' as const, alg: -7 }],
    }

    const mockCredential = {
      id: 'test-credential-id',
      rawId: new ArrayBuffer(16),
      response: {
        clientDataJSON: new ArrayBuffer(32),
        attestationObject: new ArrayBuffer(64),
      },
      type: 'public-key',
    }

    it('should successfully register a WebAuthn credential', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockRegistrationOptions),
      }
      
      const mockVerificationResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          user: { id: '1', email: mockEmail, name: 'Test User' },
          token: 'test-token',
        }),
      }

      mockPost
        .mockResolvedValueOnce(mockResponse as any)
        .mockResolvedValueOnce(mockVerificationResponse as any)

      mockNavigator.credentials.create.mockResolvedValue(mockCredential)

      const result = await registerWebAuthnCredential(mockEmail)

      expect(result).toEqual({
        user: { id: '1', email: mockEmail, name: 'Test User' },
        token: 'test-token',
      })

      expect(mockPost).toHaveBeenCalledTimes(2)
      expect(mockPost).toHaveBeenCalledWith('auth/webauthn/register/begin', { email: mockEmail })
    })

    it('should throw error when WebAuthn is not supported', async () => {
      Object.defineProperty(window, 'PublicKeyCredential', {
        value: undefined,
        writable: true,
      })

      await expect(registerWebAuthnCredential(mockEmail)).rejects.toThrow(
        'WebAuthn is not supported in this browser'
      )
    })

    it('should throw error when credential creation fails', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockRegistrationOptions),
      }

      mockPost.mockResolvedValueOnce(mockResponse as any)
      mockNavigator.credentials.create.mockResolvedValue(null)

      await expect(registerWebAuthnCredential(mockEmail)).rejects.toThrow(
        'Failed to create credential'
      )
    })
  })

  describe('authenticateWebAuthnCredential', () => {
    const mockEmail = '<EMAIL>'
    const mockAuthenticationOptions = {
      challenge: 'dGVzdC1jaGFsbGVuZ2U',
      rpId: 'localhost',
      allowCredentials: [{ id: 'dGVzdC1jcmVkZW50aWFs', type: 'public-key' as const }],
    }

    const mockCredential = {
      id: 'test-credential-id',
      rawId: new ArrayBuffer(16),
      response: {
        clientDataJSON: new ArrayBuffer(32),
        authenticatorData: new ArrayBuffer(64),
        signature: new ArrayBuffer(32),
        userHandle: new ArrayBuffer(8),
      },
      type: 'public-key',
    }

    it('should successfully authenticate with WebAuthn credential', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockAuthenticationOptions),
      }
      
      const mockVerificationResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          user: { id: '1', email: mockEmail, name: 'Test User' },
          token: 'test-token',
        }),
      }

      mockPost
        .mockResolvedValueOnce(mockResponse as any)
        .mockResolvedValueOnce(mockVerificationResponse as any)

      mockNavigator.credentials.get.mockResolvedValue(mockCredential)

      const result = await authenticateWebAuthnCredential(mockEmail)

      expect(result).toEqual({
        user: { id: '1', email: mockEmail, name: 'Test User' },
        token: 'test-token',
      })

      expect(mockPost).toHaveBeenCalledTimes(2)
      expect(mockPost).toHaveBeenCalledWith('auth/webauthn/authenticate/begin', { email: mockEmail })
    })

    it('should throw error when WebAuthn is not supported', async () => {
      Object.defineProperty(window, 'PublicKeyCredential', {
        value: undefined,
        writable: true,
      })

      await expect(authenticateWebAuthnCredential(mockEmail)).rejects.toThrow(
        'WebAuthn is not supported in this browser'
      )
    })
  })
})
