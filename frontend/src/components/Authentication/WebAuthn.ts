import { post } from 'helpers/http'
import { User } from 'stores/AuthenticationStore'

// Types for WebAuthn
export interface WebAuthnRegistrationOptions {
  challenge: string
  rp: {
    name: string
    id: string
  }
  user: {
    id: string
    name: string
    displayName: string
  }
  pubKeyCredParams: Array<{
    type: 'public-key'
    alg: number
  }>
  authenticatorSelection?: {
    authenticatorAttachment?: 'platform' | 'cross-platform'
    userVerification?: 'required' | 'preferred' | 'discouraged'
    residentKey?: 'required' | 'preferred' | 'discouraged'
    requireResidentKey?: boolean
  }
  timeout?: number
  attestation?: 'none' | 'indirect' | 'direct'
  excludeCredentials?: Array<{
    id: string
    type: 'public-key'
  }>
}

export interface WebAuthnAuthenticationOptions {
  challenge: string
  rpId?: string
  allowCredentials?: Array<{
    id: string
    type: 'public-key'
  }>
  userVerification?: 'required' | 'preferred' | 'discouraged'
  timeout?: number
}

// Helper functions for base64url encoding/decoding
function base64urlToBuffer(base64url: string): ArrayBuffer {
  const base64 = base64url.replace(/-/g, '+').replace(/_/g, '/')
  const padded = base64.padEnd(base64.length + ((4 - (base64.length % 4)) % 4), '=')
  const binary = atob(padded)
  const buffer = new ArrayBuffer(binary.length)
  const bytes = new Uint8Array(buffer)
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i)
  }
  return buffer
}

function bufferToBase64url(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '')
}

// Check if WebAuthn is supported
export function isWebAuthnSupported(): boolean {
  return !!(
    window.PublicKeyCredential &&
    navigator.credentials &&
    navigator.credentials.create &&
    navigator.credentials.get
  )
}

// Check if platform authenticator is available (Face ID, Touch ID, Windows Hello, etc.)
export async function isPlatformAuthenticatorAvailable(): Promise<boolean> {
  if (!isWebAuthnSupported()) return false

  try {
    return await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
  } catch {
    return false
  }
}

// Register a new WebAuthn credential
export async function registerWebAuthnCredential(
  email: string
): Promise<{ user: User; token: string }> {
  if (!isWebAuthnSupported()) {
    throw new Error('WebAuthn is not supported in this browser')
  }

  try {
    // Get registration options from server
    const optionsResponse = await post('auth/webauthn/register/begin', { email })
    const options: WebAuthnRegistrationOptions = await optionsResponse.json()

    // Convert base64url strings to ArrayBuffers
    const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions = {
      challenge: base64urlToBuffer(options.challenge),
      rp: options.rp,
      user: {
        id: base64urlToBuffer(options.user.id),
        name: options.user.name,
        displayName: options.user.displayName,
      },
      pubKeyCredParams: options.pubKeyCredParams,
      authenticatorSelection: options.authenticatorSelection,
      timeout: options.timeout || 60000,
      attestation: options.attestation || 'none',
      excludeCredentials: options.excludeCredentials?.map(cred => ({
        id: base64urlToBuffer(cred.id),
        type: cred.type,
      })),
    }

    // Create credential
    const credential = await navigator.credentials.create({
      publicKey: publicKeyCredentialCreationOptions,
    })

    if (!credential || !(credential instanceof PublicKeyCredential)) {
      throw new Error('Failed to create credential')
    }

    const response = credential.response as AuthenticatorAttestationResponse

    // Prepare registration response
    const registrationResponse = {
      id: credential.id,
      rawId: bufferToBase64url(credential.rawId),
      response: {
        clientDataJSON: bufferToBase64url(response.clientDataJSON),
        attestationObject: bufferToBase64url(response.attestationObject),
      },
      type: credential.type,
    }

    // Send registration response to server
    const verificationResponse = await post('auth/webauthn/register/finish', {
      email,
      registrationResponse,
    })

    const result = await verificationResponse.json()

    if (!verificationResponse.ok) {
      throw new Error(result.message || 'WebAuthn registration failed')
    }

    return result
  } catch (error) {
    console.error('WebAuthn registration error:', error)
    throw error
  }
}

// Authenticate with WebAuthn credential
export async function authenticateWebAuthnCredential(
  email: string
): Promise<{ user: User; token: string }> {
  if (!isWebAuthnSupported()) {
    throw new Error('WebAuthn is not supported in this browser')
  }

  try {
    // Get authentication options from server
    const optionsResponse = await post('auth/webauthn/authenticate/begin', { email })
    const options: WebAuthnAuthenticationOptions = await optionsResponse.json()

    // Convert base64url strings to ArrayBuffers
    const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions = {
      challenge: base64urlToBuffer(options.challenge),
      rpId: options.rpId,
      allowCredentials: options.allowCredentials?.map(cred => ({
        id: base64urlToBuffer(cred.id),
        type: cred.type,
      })),
      userVerification: options.userVerification || 'preferred',
      timeout: options.timeout || 60000,
    }

    // Get credential
    const credential = await navigator.credentials.get({
      publicKey: publicKeyCredentialRequestOptions,
    })

    if (!credential || !(credential instanceof PublicKeyCredential)) {
      throw new Error('Failed to get credential')
    }

    const response = credential.response as AuthenticatorAssertionResponse

    // Prepare authentication response
    const authenticationResponse = {
      id: credential.id,
      rawId: bufferToBase64url(credential.rawId),
      response: {
        clientDataJSON: bufferToBase64url(response.clientDataJSON),
        authenticatorData: bufferToBase64url(response.authenticatorData),
        signature: bufferToBase64url(response.signature),
        userHandle: response.userHandle ? bufferToBase64url(response.userHandle) : null,
      },
      type: credential.type,
    }

    // Send authentication response to server
    const verificationResponse = await post('auth/webauthn/authenticate/finish', {
      email,
      authenticationResponse,
    })

    const result = await verificationResponse.json()

    if (!verificationResponse.ok) {
      throw new Error(result.message || 'WebAuthn authentication failed')
    }

    return result
  } catch (error) {
    console.error('WebAuthn authentication error:', error)
    throw error
  }
}
