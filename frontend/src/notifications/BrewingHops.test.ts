import container from 'notifications/Container'

import 'notifications/BrewingHops'
import i18n from 'helpers/i18n/serviceWorker'

vi.mock('helpers/i18n/serviceWorker')

describe('BrewingHops', () => {
  it('should send notifications', async () => {
    i18n.t.mockImplementation((key: string) => key)
    // const _t = vi.fn()
    // _t.mockImplementation((key: string) => key)

    const notification = await container.executeNotification('brewingHops', {
      hops: [
        {
          id: 1,
          name: 'Hop 1',
          amount: 15,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BOIL',
        },
        {
          id: 2,
          name: 'Hop 2',
          amount: 10,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BoIL',
        },
      ],
    })

    expect(notification).toBeDefined()
    expect(notification.title).toBe('brew.notifications.hopsTitle')
    expect(notification.body).toBe('brew.notifications.hopsText')
  })

  it('should add hops to the notifications', async () => {
    i18n.t.mockImplementation((key: string) => key)
    // const _t = vi.fn()
    // _t.mockImplementation((key: string) => key)

    const notification = await container.executeNotification('brewingHops', {
      hops: [
        {
          id: 1,
          name: 'Hop 1',
          amount: 15,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BOIL',
        },
        {
          id: 2,
          name: 'Hop 2',
          amount: 10,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BoIL',
        },
      ],
    })

    expect(i18n.t).toHaveBeenLastCalledWith('brew.notifications.hopsText', {
      hop: 'Hop 1 15g (1%), Hop 2 10g (1%)',
    })
  })
})
