import type { BrewingHop } from '@prisma/client'
import container from 'notifications/Container'

container.addNotification('brewingHops', async (payload, i18n) => {
  const data = payload as unknown as { hops: BrewingHop[] }
  return {
    title: i18n.t('brew.notifications.hopsTitle'),
    body: i18n.t('brew.notifications.hopsText', {
      hop: data.hops.map(h => `${h.name} ${h.amount}g (${h.alpha}%)`).join(', '),
    }),
  }
})
