import type { i18n as iI18N } from 'i18next'

import i18n from 'helpers/i18n/serviceWorker'

type Notification = { title: string } & NotificationOptions

type NotificationFn = <T extends Record<string, unknown>>(
  payload: T,
  i18n: iI18N
) => Promise<Notification | undefined>

class NotificationContainer {
  private notifications: Map<string, NotificationFn>

  constructor() {
    this.notifications = new Map()
  }

  public addNotification(messageType: string, notificationFn: NotificationFn) {
    this.notifications.set(messageType, notificationFn)
  }

  public getNotifications() {
    return this.notifications
  }

  public async executeNotification<T extends Record<string, unknown>>(type: string, payload: T) {
    const fn = this.notifications.get(type)
    if (fn) {
      const notification = await fn(payload, i18n)
      return notification
    }
  }
}

const container = new NotificationContainer()

export default container
