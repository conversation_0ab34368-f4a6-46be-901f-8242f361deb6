import { create } from 'zustand'
import { persist } from 'zustand/middleware'

import { get } from 'helpers/http'

type IntegrationResponse = {
  tuya: boolean
  braureka: boolean
  smartlife: boolean
  mqtt: {
    clientId: string | null
    username: string
    password: string
  }
  loadIntregration: () => void
  clear: () => void
}

const defaultIntegration = {
  tuya: false,
  braureka: false,
  mqtt: {
    clientId: null,
    username: '',
    password: '',
  },
  smartlife: false,
}

const useIntegrationStore = create<IntegrationResponse>()(
  persist(
    set => {
      const loadIntregration = async () => {
        const request = await get<IntegrationResponse>('integration')
        if (request) {
          set(request)
        }
      }

      return {
        ...defaultIntegration,
        loadIntregration,
        clear: () => set(defaultIntegration),
      }
    },
    { name: 'philun-integration' }
  )
)

export default useIntegrationStore
