import useIntegrationStore from 'stores/IntegrationStore'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface User {
  id: string
  name: string
  email: string
  imageUrl?: string
}

interface AuthenticationStates {
  isAuthenticated: boolean
  user?: User
  token: string
  login: (user: User, token: string) => void
  logout: () => void
}

const useAuthenticationStore = create<AuthenticationStates>()(
  persist(
    set => ({
      isAuthenticated: false,
      user: undefined,
      token: '',
      login: (user: User, token: string) => {
        set({ isAuthenticated: true, user, token })
        useIntegrationStore.getState().loadIntregration()
      },
      logout: () => {
        set({ isAuthenticated: false, user: undefined, token: '' })
        useIntegrationStore.getState().clear()
      },
    }),
    {
      name: 'philun-authentication',
      // storage: createJSONStorage(),
    }
  )
)

export default useAuthenticationStore
