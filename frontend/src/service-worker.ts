/// <reference lib="webworker" />

import { PushData } from '@backend/types/shared'
import container from 'notifications/Container'

import LogoUrl from 'assets/logo.svg'

import i18n from 'helpers/i18n/serviceWorker'

import 'notifications'

const sw = self as unknown as ServiceWorkerGlobalScope & typeof globalThis

sw.addEventListener('message', event => {
  if (event.data && event.data.type === 'MESSAGE_LANGUAGE') {
    i18n.changeLanguage(event.data.language)
  }
})

sw.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json() as PushData

    if ('messageType' in data) {
      container.executeNotification(data.messageType, data).then(notification => {
        if (notification) {
          const { title, ...options } = notification

          const promiseChain = sw.registration.showNotification(title, {
            icon: LogoUrl,
            ...options,
          })

          event.waitUntil(promiseChain)
        }
      })
    } else {
      const promiseChain = sw.registration.showNotification(i18n.t(data.title), {
        body: i18n.t(data.key, data.data) ?? data.text,
        icon: LogoUrl,
      })

      event.waitUntil(promiseChain)
    }

    // const onReachedPlaton = {
    //   actions: [
    //     {
    //       action: 'cold-crush-action',
    //       type: 'button',
    //       title: i18n.t('fermentation.coldCrush'),
    //       icon: '/images/demos/action-1-128x128.png',
    //     },
    //   ],
    // }
  }
})
