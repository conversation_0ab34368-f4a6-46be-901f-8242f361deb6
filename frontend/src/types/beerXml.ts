// https://beerxml.com/beerxml.htm

export type BeerXml = {
  RECIPES: Recipe[]
}

type Recipe = {
  VERSION: string
  NAME: string
  TYPE: string
  BREWER: string
  BATCH_SIZE: string
  BOIL_SIZE: string
  BOIL_TIME: string
  EFFICIENCY?: string
  NOTES?: string
  TASTE_NOTES?: string
  TASTE_RATING?: string
  OG?: string
  FG?: string
  FERMENTATION_STAGES?: string
  PRIMARY_AGE?: string
  PRIMARY_TEMP?: string
  SECONDARY_AGE?: string
  SECONDARY_TEMP?: string
  TERTIARY_AGE?: string
  TERTIARY_TEMP?: string
  AGE?: string
  AGE_TEMP?: string
  DATE?: string
  CARBONATION?: string
  FORCED_CARBONATION?: string
  PRIMING_SUGAR_NAME?: string
  CARBONATION_TEMP?: string
  PRIMING_SUGAR_EQUIV?: string
  KEG_PRIMING_FACTOR?: string
  EST_OG?: string
  EST_FG?: string
  EST_COLOR?: string
  IBU?: string
  IBU_METHOD?: string
  EST_ABV?: string
  ABV?: string
  ACTUAL_EFFICIENCY?: string
  CALORIES?: string
  CARBONATION_USED?: string
  STYLE: {
    VERSION: string
    NAME: string
    CATEGORY: string
    CATEGORY_NUMBER: string
    STYLE_LETTER: string
    STYLE_GUIDE: string
    TYPE: string
    OG_MIN: string
    OG_MAX: string
    FG_MIN: string
    FG_MAX: string
    IBU_MIN: string
    IBU_MAX: string
    COLOR_MIN: string
    COLOR_MAX: string
    CARB_MIN: string
    CARB_MAX: string
  }
  HOPS: Hop[]
  FERMENTABLES: Fermentable[]
  MISCS: unknown[]
  YEASTS: Yeast[]
  MASHS: Mash[]
  MASH?: Mash
}

type Mash = {
  NAME: string
  VERSION: string
  GRAIN_TEMP?: string
  TUN_TEMP?: string
  SPARGE_TEMP?: string
  PH?: string
  TUN_WEIGHT?: string
  TUN_SPECIFIC_HEAT?: string
  EQUIP_ADJUST?: 'false' | 'true'
  NOTES?: string
  DISPLAY_GRAIN_TEMP?: string
  DISPLAY_TUN_TEMP?: string
  DISPLAY_SPARGE_TEMP?: string
  DISPLAY_TUN_WEIGHT?: string
  MASH_STEPS: MashStep[]
}

type MashStep = {
  NAME: string
  VERSION: string
  TYPE?: string
  INFUSE_AMOUNT?: string
  STEP_TIME: string
  STEP_TEMP: string
  RAMP_TIME?: string
  END_TEMP?: string
  DESCRIPTION?: string
  WATER_GRAIN_RATIO?: string
  DECOCTION_AMT?: string
  INFUSE_TEMP?: string
  DISPLAY_STEP_TEMP?: string
  DISPLAY_INFUSE_AMT?: string
}

type Hop = {
  VERSION: string
  NAME: string
  ALPHA: string
  AMOUNT: string
  USE: string
  TIME: string
  TYPE: string
  FORM: string
}

type Fermentable = {
  VERSION: string
  NAME: string
  TYPE: string
  AMOUNT: string
  YIELD: string
  COLOR: string
  ADD_AFTER_BOIL?: string
  NOTES?: string
}

type Yeast = {
  VERSION: string
  NAME: string
  TYPE: string
  FORM: string
  AMOUNT: string
}
