{"name": "@unrup1-gae<PERSON>rank/frontend", "version": "0.1.1", "author": {"name": "<PERSON>", "url": "https://philun.de"}, "license": "UNLICENSED", "main": "index.ts", "engines": {"yarn": "1.22.x"}, "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "serve": "vite preview", "lint": "yarn run lint:script & yarn run lint:pretty", "lint:script": "eslint ./src/**/*.{ts,tsx}", "lint:pretty": "prettier ./src/**/*.{ts,tsx} --check", "test": "vitest", "test:coverage": "vitest run --coverage", "lint-staged": "lint-staged"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.2", "@simplewebauthn/browser": "^13.1.2", "@simplewebauthn/types": "^12.0.0", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.0.5", "autoprefixer": "^10.4.16", "axios": "^1.5.1", "class-variance-authority": "^0.7.0", "date-fns": "^4.1.0", "handlebars": "^4.7.8", "i18next": "^23.5.1", "immutability-helper": "^3.1.1", "lucide-react": "^0.507.0", "luxon": "^3.4.3", "msw": "^2.0.1", "platform": "^1.3.6", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "react-i18next": "^14.1.2", "react-router-dom": "^6.17.0", "recharts": "^2.15.3", "tailwind-merge": "^2.0.0", "tailwindcss": "^4.1.5", "zod": "^3.22.4", "zod-i18n-map": "^2.20.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@faker-js/faker": "^8.2.0", "@rjsf/core": "5.13.2", "@tailwindcss/forms": "^0.5.10", "@tanstack/eslint-plugin-query": "^5.0.5", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.1", "@types/color": "^3.0.5", "@types/git-rev-sync": "^2.0.2", "@types/jest": "^29.5.6", "@types/luxon": "^3.3.2", "@types/node": "^20.8.6", "@types/react": "^18.2.28", "@types/react-dom": "^18.2.13", "@types/react-router-dom": "^5.3.3", "@types/sanitize-html": "^2.9.2", "@types/web-bluetooth": "^0.0.20", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "@vitejs/plugin-react": "^4.1.0", "@vitest/coverage-v8": "^0.34.6", "camelcase": "^8.0.0", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-import-helpers": "^1.3.1", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-no-relative-import-paths": "^1.5.3", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.1.0", "git-rev-sync": "^3.0.2", "jsdom": "^24.1.0", "lint-staged": "^15.0.1", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "react-app-polyfill": "^3.0.0", "recursive-copy": "^2.0.14", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3", "workbox-window": "^7.3.0", "xhr-mock": "^2.5.1"}}