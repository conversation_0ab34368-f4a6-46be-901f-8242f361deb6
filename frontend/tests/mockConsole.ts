const mocks = {}

type ConsoleFnNames = (keyof Console)[]

export const mockConsole = (fnNames: ConsoleFnNames) => {
  fnNames.forEach(fnName => {
    if (mocks[fnName]) {
      return
    }

    mocks[fnName] = console[fnName]
    console[fnName] = vi.fn()
  })

  return () => unmockConsole(fnNames)
}

export const unmockConsole = (fnNames: ConsoleFnNames) => {
  fnNames.forEach(fnName => {
    if (!mocks[fnName]) {
      return
    }

    console[fnName] = mocks[fnName]
    mocks[fnName] = undefined
  })
}
