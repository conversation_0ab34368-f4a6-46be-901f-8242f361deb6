ARG VERSION=local
FROM node:20-bullseye-slim AS builder

WORKDIR /package
COPY /backend/ ./backend
COPY /frontend/ ./frontend
COPY /shared/ ./shared
COPY /package.json .
COPY /yarn.lock .

WORKDIR /package
RUN yarn install; \
  yarn build;

RUN yarn install --production

FROM node:20-bullseye-slim AS runner

WORKDIR /app
COPY --from=builder --chown=node:node /package/backend /app
COPY --from=builder --chown=node:node /package/frontend/dist /app/public
COPY --from=builder --chown=node:node /package/node_modules /app/node_modules

EXPOSE 3000

USER node
CMD ["sh", "-c", "npx prisma migrate deploy && node /app/dist/src/app.js"]
