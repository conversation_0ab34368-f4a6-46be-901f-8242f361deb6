{
  "compilerOptions": {
    "sourceMap": true,
    "outDir": "dist",
    "strict": true,
    "lib": ["esnext"],
    "types": ["jest", "node"],
    "esModuleInterop": true,
    "skipLibCheck": true,
    "moduleResolution": "Node16",
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "module": "Node16",
    "target": "ES2022",
    "baseUrl": "src"
  },
  // "include": ["src/**/*.ts"],
  "exclude": ["node_modules", "src/**/*.test.ts", "tests"],
  "compileOnSave": true
}
