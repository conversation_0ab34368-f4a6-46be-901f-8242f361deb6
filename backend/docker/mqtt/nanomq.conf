# NanoMQ Configuration 0.18.0

# #============================================================
# # NanoMQ Broker
# #============================================================

mqtt {
    property_size = 32
    max_packet_size = 10KB
    max_mqueue_len = 2048
    retry_interval = 10s
    keepalive_multiplier = 1.25
    
    # Three of below, unsupported now
    max_inflight_window = 2048
    max_awaiting_rel = 10s
    await_rel_timeout = 10s
}

listeners.tcp {
    bind = "0.0.0.0:1883"
}

# listeners.ssl {
# 	bind = "0.0.0.0:8883"
# 	keyfile = "/etc/certs/key.pem"
# 	certfile = "/etc/certs/cert.pem"
# 	cacertfile = "/etc/certs/cacert.pem"
# 	verify_peer = false
# 	fail_if_no_peer_cert = false
# }

listeners.ws {
    bind = "0.0.0.0:8083/mqtt"
}

http_server {
    port = 8081
    limit_conn = 2
    username = admin
    password = public
    auth_type = basic
}

log {
    to = [file, console]
    level = warn
    dir = "/tmp"
    file = "nanomq.log"
    rotation {
        size = 10MB
        count = 5
    }
}

auth {
    allow_anonymous = false
    no_match = deny
    deny_action = disconnect
    
    cache = {
        max_size = 32
        ttl = 1m
    }

    http_auth = {
      auth_req = {
        url = "http://host.docker.internal:3000/api/mqtt/auth"
        method = "POST"
        headers.content-type = "application/json"
        params = {clientid = "%c", username = "%u", password = "%P"}
      }

      super_req = {
        url = "http://host.docker.internal:3000/api/mqtt/superuser"
        method = "POST"
        headers.content-type = "application/json"
        params = {clientid = "%c", username = "%u", password = "%P"}
      }

      acl_req = {
        url = "http://host.docker.internal:3000/api/mqtt/acl"
        method = "POST"
        headers.content-type = "application/json"
        params = {clientid = "%c", username = "%u", access = "%A", ipaddr = "%a", topic = "%t", mountpoint = "%m"}
      }

      timeout = 5s
      connect_timeout = 5s
      pool_size = 32
    } 
}
