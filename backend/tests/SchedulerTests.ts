import { DateTime } from 'luxon'
import schedule, { gracefulShutdown } from 'node-schedule'

export const getDateTimeFromJob = (job: string) => {
  const jobs = schedule.scheduledJobs

  expect(jobs[job]).toBeDefined()

  const invocation = jobs[job].nextInvocation()
  return DateTime.fromISO(invocation.toISOString())
}

export const mockScheduler = () => {
  afterEach(() => {
    gracefulShutdown()
  })
}
