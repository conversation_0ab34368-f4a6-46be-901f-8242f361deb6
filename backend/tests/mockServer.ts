import { DefaultBodyType, rest } from 'msw'
import { setupServer } from 'msw/node'

const mockServer = (onUnhandledRequest: 'warn' | 'error' = 'error') => {
  const server = setupServer()

  beforeAll(() => {
    server.listen({ onUnhandledRequest })
  })
  afterEach(() => {
    server.resetHandlers()
  })
  afterAll(() => server.close())

  const mockGET = (url: string, response?: DefaultBodyType, status = 200) => {
    server.use(
      rest.get(url, (_req, res, ctx) => {
        return res(ctx.status(status), ctx.json(response))
      })
    )
  }

  const mockOPTIONS = (url: string) => {
    server.use(
      rest.options(url, (_req, res, ctx) => {
        return res(ctx.status(200))
      })
    )
  }

  const mockPOST = (url: string, response?: DefaultBodyType, status = 200) => {
    server.use(
      rest.post(url, (_req, res, ctx) => {
        return res(ctx.status(status), ctx.json(response))
      })
    )
  }

  return { server, mockGET, mockPOST, mockOPTIONS }
}

export default mockServer
