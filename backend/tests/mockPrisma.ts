import { PrismaClient } from '@prisma/client'
import { mockDeep, mockReset, DeepMockProxy } from 'jest-mock-extended'
import prisma from '../src/helpers/prisma'

jest.mock('./../src/helpers/prisma', () => ({
  __esModule: true,
  default: mockDeep<PrismaClient>(),
}))

export default function mockPrisma() {
  const prismaMock = prisma as unknown as DeepMockProxy<PrismaClient>

  beforeEach(() => {
    mockReset(prismaMock)
  })

  return {
    prisma,
    prismaMock,
  }
}
