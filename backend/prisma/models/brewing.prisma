// Brewing
model Brewing {
    id                      Int                  @id @default(autoincrement())
    createdAt               DateTime             @default(now())
    startTime               DateTime?
    endTime                 DateTime?
    mashType                String
    fermentationTemperature Float
    lauteringPauseTime      DateTime?
    boilTime                Int
    boilStartTime           DateTime?
    status                  BrewingStatus?       @default(WAITING)
    recipeId                Int?
    recipe                  Recipe?              @relation(fields: [recipeId], references: [id])
    Temperature             Temperature[]
    mashSteps               BrewingMashStep[]
    hops                    BrewingHop[]
    yeasts                  BrewingYeast[]
    fermentables            BrewingFermentable[]
}

enum BrewingStatus {
    WAITING
    DONE
    PAUSED
    CANCELED
    PREMASHING
    MASHING
    LAUTERING
    BOILING
    FERMENTATION
}

enum BrewingMashStepStatus {
    WAITING
    ACTIVE
    HEATING
    DONE
}

model BrewingMashStep {
    id           Int                   @id @default(autoincrement())
    name         String
    infuseAmount Float
    temperature  Float
    time         Int
    type         String
    startTime    DateTime?
    endTime      DateTime?
    status       BrewingMashStepStatus @default(WAITING)
    order        Int
    brewingId    Int
    heatingStart DateTime?
    brewing      Brewing               @relation(fields: [brewingId], references: [id], onDelete: Cascade)
}

model BrewingHop {
    id                   Int     @id @default(autoincrement())
    name                 String
    amount               Float
    alpha                Float
    form                 String
    time                 Int
    type                 String
    use                  String
    added                Boolean @default(false)
    brewingId            Int
    sendPushNotification Boolean @default(false)
    brewing              Brewing @relation(fields: [brewingId], references: [id], onDelete: Cascade)
}

model BrewingYeast {
    id                      Int     @id @default(autoincrement())
    name                    String
    fermentationTemperature Float
    amount                  Float
    brewingId               Int
    brewing                 Brewing @relation(fields: [brewingId], references: [id], onDelete: Cascade)
}

model BrewingFermentable {
    id           Int      @id @default(autoincrement())
    name         String
    type         String?
    amount       Float
    addAfterBoil Boolean? @default(false)
    brewingId    Int
    added        Boolean  @default(false)
    brewing      Brewing  @relation(fields: [brewingId], references: [id], onDelete: Cascade)
}
