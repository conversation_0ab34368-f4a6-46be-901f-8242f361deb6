// Devices
enum DeviceOperator {
    TUYA
    TASMOTA
    SMARTLIFE
    BREWTOOL
    CUSTOM
}

enum DeviceType {
    TEMPERATURE
    SWITCH
    ISPINDEL
    BREWTOOL
    OTHER
}

model Device {
    id                   Int                    @id @default(autoincrement())
    name                 String
    operator             DeviceOperator
    operatorDeviceId     String?
    tuyaDeviceId         String? // TODO: move to operatorDeviceId
    createdAt            DateTime               @default(now())
    updatedAt            DateTime               @default(now())
    userId               String?
    user                 User?                  @relation(fields: [userId], references: [id])
    Connection           DeviceConnected[]
    mqttClient           MqttClient[]
    DeviceWorkingLog     DeviceWorkingLog[]
    type                 DeviceType
    SpindelLog           SpindelLog[]
    ApiKey               ApiKey?                @relation(fields: [apiKeyId], references: [id])
    apiKeyId             Int?
    StatisticValues      StatisticValues[]
    Temperature          Temperature[]
    deviceConnections    Device[]               @relation("DeviceConnections")
    deviceConnected      Device?                @relation("DeviceConnections", fields: [deviceConnectedId], references: [id])
    deviceConnectedId    Int?
    StatisticElectricity StatisticElectricity[]
}

enum DeviceConnectedUse {
    FERMENTATION
    BREWING
    OTHER
}

model DeviceConnected {
    id        Int                @id @default(autoincrement())
    createdAt DateTime           @default(now())
    updatedAt DateTime           @default(now())
    deviceId  Int
    device    Device             @relation(fields: [deviceId], references: [id])
    type      String
    use       DeviceConnectedUse
}

model DeviceWorkingLog {
    id        Int       @id @default(autoincrement())
    deviceId  Int
    device    Device    @relation(fields: [deviceId], references: [id])
    startTime DateTime
    endTime   DateTime?
    createdAt DateTime  @default(now())
}
