import { Prisma, PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'
import fs from 'fs'

const prisma = new PrismaClient()

async function seed() {
  // await onDev()

  // import von https://obrama.mueggelland.de/
  await prisma.searchIndex.deleteMany({})

  // malts
  const malts = await getDataFromMueggelland<{
    name: string
    category: string
    alias_name: string
    notes: string
  }>('fermentables')

  for (const malt of malts) {
    const type = malt.category === 'Zusatz' || malt.category === 'Zucker' ? 'other' : 'malt'
    await addToSearchIndex(malt.name, type, malt.notes, malt, malt.alias_name)
  }

  // hops
  const hops = await getDataFromMueggelland<{
    name: string
    IHGC_code: string
  }>('hops')

  for (const hop of hops) {
    await addToSearchIndex(hop.name, 'hop', hop.IHGC_code, hop)
  }

  // yeasts
  const yeasts = await getDataFromMueggelland<{
    name: string
    IHGC_code: string
  }>('yeasts')

  for (const yeast of yeasts) {
    await addToSearchIndex(yeast.name, 'yeast', null, yeast)
  }

  // others
  const others = await getDataFromMueggelland<{
    name: string
  }>('adjuncts')

  for (const other of others) {
    await addToSearchIndex(other.name, 'other', null, other)
  }
}

async function getDataFromMueggelland<T>(table: string) {
  const response = fs.readFileSync(`./prisma/searchData/${table}.json`, 'utf8')
  return JSON.parse(response) as T[]
}

async function addToSearchIndex(
  name: string,
  type: 'malt' | 'hop' | 'yeast' | 'other',
  description: string | null,
  data: Prisma.InputJsonValue,
  alias_name?: string
) {
  await prisma.searchIndex.create({
    data: {
      name: name.trim(),
      type,
      description,
      extraData: data,
    },
  })

  if (alias_name) {
    const alias = alias_name.split(',')
    for (const a of alias) {
      await prisma.searchIndex.create({
        data: {
          name: a.trim(),
          type,
          description,
          extraData: data,
        },
      })
    }
  }
}

async function onDev() {
  if (process.env.NODE_ENV === 'production') {
    return
  }

  let now = new Date()
  for (let index = 0; index < 6; index++) {
    now.setMinutes(now.getMinutes() - 5)
    await prisma.statisticValues.create({
      data: {
        temperature: faker.number.int({ min: 0, max: 30 }),
        createdAt: now,
      },
    })
  }
}

seed()
  .then(() => {
    console.log('Seeding done.')
  })
  .catch(e => {
    console.error(e)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
