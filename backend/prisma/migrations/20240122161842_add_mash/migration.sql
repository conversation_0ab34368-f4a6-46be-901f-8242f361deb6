-- AlterTable
ALTER TABLE `RecipeHop` ADD COLUMN `form` VARCHAR(191) NOT NULL DEFAULT 'PELLET',
    ADD COLUMN `time` INTEGER NOT NULL DEFAULT 0,
    ADD COLUMN `type` VARCHAR(191) NOT NULL DEFAULT 'AROMA',
    ADD COLUMN `use` VARCHAR(191) NOT NULL DEFAULT 'BOIL';

-- CreateTable
CREATE TABLE `RecipeMash` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `recipeId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `RecipeMashStep` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `infuseAmount` DOUBLE NOT NULL,
    `temperature` DOUBLE NOT NULL,
    `time` INTEGER NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `recipeMashId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `RecipeMash` ADD CONSTRAINT `RecipeMash_recipeId_fkey` FOREIGN KEY (`recipeId`) REFERENCES `Recipe`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecipeMashStep` ADD CONSTRAINT `RecipeMashStep_recipeMashId_fkey` FOREIGN KEY (`recipeMashId`) REFERENCES `RecipeMash`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
