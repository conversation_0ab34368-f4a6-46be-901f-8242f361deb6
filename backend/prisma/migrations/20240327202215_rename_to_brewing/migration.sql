/*
  Warnings:

  - You are about to drop the `BrewerFermentable` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `BrewerLogYeast` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `BrewerFermentable` DROP FOREIGN KEY `BrewerFermentable_brewingId_fkey`;

-- DropForeignKey
ALTER TABLE `BrewerLogYeast` DROP FOREIGN KEY `BrewerLogYeast_brewingId_fkey`;

-- DropTable
DROP TABLE `BrewerFermentable`;

-- DropTable
DROP TABLE `BrewerLogYeast`;

-- CreateTable
CREATE TABLE `BrewingYeast` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `fermentationTemperature` DOUBLE NOT NULL,
    `amount` DOUBLE NOT NULL,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BrewingFermentable` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NULL,
    `amount` DOUBLE NOT NULL,
    `addAfterBoil` BOOLEAN NULL DEFAULT false,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `BrewingYeast` ADD CONSTRAINT `BrewingYeast_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BrewingFermentable` ADD CONSTRAINT `BrewingFermentable_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
