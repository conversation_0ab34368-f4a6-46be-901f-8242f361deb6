/*
  Warnings:

  - You are about to drop the column `mqttClientId` on the `Device` table. All the data in the column will be lost.
  - You are about to drop the column `mqttPassword` on the `Device` table. All the data in the column will be lost.
  - You are about to drop the column `mqttUsername` on the `Device` table. All the data in the column will be lost.
  - Added the required column `deviceId` to the `MqttClient` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `Device` DROP FOREIGN KEY `Device_mqttClientId_fkey`;

-- DropIndex
DROP INDEX `Device_mqttUsername_key` ON `Device`;

-- AlterTable
ALTER TABLE `MqttClient` ADD COLUMN `deviceId` INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE `MqttClient` ADD CONSTRAINT `MqttClient_deviceId_fkey` FOREIGN KEY (`deviceId`) REFERENCES `Device`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AlterTable
ALTER TABLE `Device` DROP COLUMN `mqttClientId`,
    DROP COLUMN `mqttPassword`,
    DROP COLUMN `mqttUsername`;
