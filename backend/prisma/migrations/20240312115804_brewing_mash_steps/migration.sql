/*
  Warnings:

  - You are about to drop the column `stepsLog` on the `Brewing` table. All the data in the column will be lost.
  - Added the required column `mashType` to the `Brewing` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `Brewing` DROP COLUMN `stepsLog`,
    ADD COLUMN `mashType` VARCHAR(191) NOT NULL;

-- CreateTable
CREATE TABLE `BrewingMashStep` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `infuseAmount` DOUBLE NOT NULL,
    `temperature` DOUBLE NOT NULL,
    `time` INTEGER NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `BrewingMashStep` ADD CONSTRAINT `BrewingMashStep_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
