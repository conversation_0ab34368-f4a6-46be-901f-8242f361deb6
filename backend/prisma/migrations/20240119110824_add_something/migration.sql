-- DropF<PERSON><PERSON><PERSON><PERSON>
ALTER TABLE `RecipeHop` DROP FOREIGN KEY `RecipeHop_recipeId_fkey`;

-- DropForeignKey
ALTER TABLE `RecipeYeast` DROP FOREIGN KEY `RecipeYeast_recipeId_fkey`;

-- AddForeignKey
ALTER TABLE `RecipeHop` ADD CONSTRAINT `RecipeHop_recipeId_fkey` FOREIGN KEY (`recipeId`) REFERENCES `Recipe`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `RecipeYeast` ADD CONSTRAINT `RecipeYeast_recipeId_fkey` FOREIGN KEY (`recipeId`) REFERENCES `Recipe`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
