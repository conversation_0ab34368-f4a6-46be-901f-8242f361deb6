/*
  Warnings:

  - You are about to alter the column `temperature` on the `StatisticValues` table. The data in that column could be lost. The data in that column will be cast from `Int` to `Double`.

*/
-- AlterTable
ALTER TABLE `StatisticValues` MODIFY `temperature` DOUBLE NOT NULL;

-- CreateTable
CREATE TABLE `StatisticElectricity` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `type` ENUM('HEATING', 'COOLING') NOT NULL,
    `step` ENUM('STOP', 'START') NOT NULL,
    `createdAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
