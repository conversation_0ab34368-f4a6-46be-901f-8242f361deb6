-- AlterTable
ALTER TABLE `BrewingHop` ADD COLUMN `sendPushNotification` BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE `PushNotification` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `data` JSON NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `pushSubscriptionId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `PushNotification` ADD CONSTRAINT `PushNotification_pushSubscriptionId_fkey` FOREIGN KEY (`pushSubscriptionId`) REFERENCES `PushSubscription`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
