-- AlterTable
ALTER TABLE `Device` ADD COLUMN `userId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `SpindelLog` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `spindelName` VARCHAR(191) NOT NULL,
    `angle` DOUBLE NOT NULL,
    `temperature` DOUBLE NOT NULL,
    `gravity` DOUBLE NOT NULL,
    `battery` DOUBLE NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `requestId` JSON NOT NULL,
    `deviceId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Device` ADD CONSTRAINT `Device_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON>ignK<PERSON>
ALTER TABLE `SpindelLog` ADD CONSTRAINT `SpindelLog_deviceId_fkey` FOREIGN KEY (`deviceId`) REFERENCES `Device`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
