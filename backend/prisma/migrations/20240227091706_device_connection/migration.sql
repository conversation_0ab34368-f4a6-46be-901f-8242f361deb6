/*
  Warnings:

  - You are about to drop the `FermantationDeviceConnected` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `FermantationDeviceConnected` DROP FOREIGN KEY `FermantationDeviceConnected_deviceId_fkey`;

-- DropTable
DROP TABLE `FermantationDeviceConnected`;

-- CreateTable
CREATE TABLE `DeviceConnected` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `deviceId` INTEGER NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `use` ENUM('FERMENTATION', 'BREWING', 'OTHER') NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `DeviceConnected` ADD CONSTRAINT `DeviceConnected_deviceId_fkey` FOREIGN KEY (`deviceId`) REFERENCES `Device`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
