/*
  Warnings:

  - You are about to drop the column `step` on the `StatisticElectricity` table. All the data in the column will be lost.
  - Added the required column `status` to the `StatisticElectricity` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `StatisticElectricity` DROP COLUMN `step`,
    ADD COLUMN `status` ENUM('STOP', 'START') NOT NULL;

-- CreateTable
CREATE TABLE `InventaryItem` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `amount` INTEGER NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `unit` VARCHAR(191) NOT NULL,
    `inventaryTypeId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `InventaryType` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `InventaryItem` ADD CONSTRAINT `InventaryItem_inventaryTypeId_fkey` FOREIGN KEY (`inventaryTypeId`) REFERENCES `InventaryType`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
