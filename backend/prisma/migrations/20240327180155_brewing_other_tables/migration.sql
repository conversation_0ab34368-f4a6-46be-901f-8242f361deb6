/*
  Warnings:

  - The values [ACTIVE,HEATING] on the enum `Brewing_status` will be removed. If these variants are still used in the database, this will fail.
  - The values [RUNNING] on the enum `BrewingMashStep_status` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterTable
ALTER TABLE `Brewing` MODIFY `status` ENUM('WAITING', 'DONE', 'PAUSED', 'CANCELED', 'PREMASHING', 'MASHING', 'LAUTERING', 'BOILING', 'FERMENTATION') NULL DEFAULT 'WAITING';

-- AlterTable
ALTER TABLE `BrewingMashStep` ADD COLUMN `heatingStart` DATETIME(3) NULL,
    MODIFY `status` ENUM('WAITING', 'ACTIVE', 'HEATING', 'DONE') NOT NULL DEFAULT 'WAITING';

-- CreateTable
CREATE TABLE `BrewingHop` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `amount` DOUBLE NOT NULL,
    `alpha` DOUBLE NOT NULL,
    `form` VARCHAR(191) NOT NULL,
    `time` INTEGER NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `use` VARCHAR(191) NOT NULL,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BrewerLogYeast` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `fermentationTemperature` DOUBLE NOT NULL,
    `amount` DOUBLE NOT NULL,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `BrewerFermentable` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NULL,
    `amount` DOUBLE NOT NULL,
    `addAfterBoil` BOOLEAN NULL DEFAULT false,
    `brewingId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `BrewingHop` ADD CONSTRAINT `BrewingHop_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BrewerLogYeast` ADD CONSTRAINT `BrewerLogYeast_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `BrewerFermentable` ADD CONSTRAINT `BrewerFermentable_brewingId_fkey` FOREIGN KEY (`brewingId`) REFERENCES `Brewing`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
