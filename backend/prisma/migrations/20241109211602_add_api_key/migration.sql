-- AlterTable
ALTER TABLE `Device` ADD COLUMN `apiKeyId` INTEGER NULL,
    ADD COLUMN `operatorDeviceId` VARCHAR(191) NULL,
    MODIFY `operator` ENUM('TUYA', 'T<PERSON><PERSON><PERSON>', '<PERSON>AR<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'CUSTOM') NOT NULL,
    MODIFY `type` ENUM('TEMPERATURE', 'SWITCH', '<PERSON><PERSON><PERSON><PERSON>', 'BREWTOOL', 'OTHER') NOT NULL;

-- AlterTable
ALTER TABLE `PushNotification` ADD COLUMN `userId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `PushSubscription` ADD COLUMN `userId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `StatisticElectricity` ADD COLUMN `userId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `StatisticValues` ADD COLUMN `userId` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `Temperature` ADD COLUMN `userId` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `ApiKey` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `key` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `userId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Temperature` ADD CONSTRAINT `Temperature_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StatisticValues` ADD CONSTRAINT `StatisticValues_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `StatisticElectricity` ADD CONSTRAINT `StatisticElectricity_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PushSubscription` ADD CONSTRAINT `PushSubscription_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PushNotification` ADD CONSTRAINT `PushNotification_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ApiKey` ADD CONSTRAINT `ApiKey_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Device` ADD CONSTRAINT `Device_apiKeyId_fkey` FOREIGN KEY (`apiKeyId`) REFERENCES `ApiKey`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
