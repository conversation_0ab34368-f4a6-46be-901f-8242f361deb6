-- CreateTable
CREATE TABLE `RecipeFermentable` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `recipeId` INTEGER NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NULL,
    `amount` DOUBLE NOT NULL,
    `yield` DOUBLE NULL,
    `color` DOUBLE NULL,
    `addAfterBoil` BOOLEAN NULL DEFAULT false,
    `notes` TEXT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `RecipeFermentable` ADD CONSTRAINT `RecipeFermentable_recipeId_fkey` FOREIGN KEY (`recipeId`) REFERENCES `Recipe`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
