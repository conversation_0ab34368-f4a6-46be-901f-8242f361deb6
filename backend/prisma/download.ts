import fs from 'fs'

const dir = './prisma/searchData'

async function getDataFromMueggelland<T>(table: string) {
  const response = await fetch(
    `https://obrama.mueggelland.de/api/obrama.php?table=${table}&format=json`
  )
  const data = (await response.json()) as T[]
  return data
}

async function saveDataToFs<T>(table: string, data: T[]) {
  const stream = fs.createWriteStream(`${dir}/${table}.json`)
  stream.write(JSON.stringify(data))
  stream.end()
}

const tables = ['fermentables', 'hops', 'yeasts', 'adjuncts']

async function main() {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir)
  }

  for (const table of tables) {
    fs.unlinkSync(`${dir}/${table}.json`)

    const data = await getDataFromMueggelland(table)
    await saveDataToFs(table, data)
  }
}
main()
  .then(() => {
    console.log('done')
  })
  .catch(e => {
    console.error(e)
  })
