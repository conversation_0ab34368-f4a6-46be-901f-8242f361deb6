// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider = "zod-prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Fermentation
model Temperature {
  id            Int       @id @default(autoincrement())
  temperature   Int
  startDate     DateTime
  endDate       DateTime?
  sendPush      Boolean   @default(false)
  createdAt     DateTime  @default(now())
  recipeName    String?
  gravityNotify Float?
  brewId        Int?
  brew          Brewing?  @relation(fields: [brewId], references: [id])
  recipeId      Int?
  recipe        Recipe?   @relation(fields: [recipeId], references: [id])
  userId        String?
  user          User?     @relation(fields: [userId], references: [id])
  deviceId      Int?
  device        Device?   @relation(fields: [deviceId], references: [id])
}

model StatisticValues {
  id          Int      @id @default(autoincrement())
  temperature Float
  createdAt   DateTime
  userId      String?
  user        User?    @relation(fields: [userId], references: [id])
  deviceId    Int?
  device      Device?  @relation(fields: [deviceId], references: [id])
}

enum ElectricityType {
  HEATING
  COOLING
}

enum ElectricityStatus {
  STOP
  START
}

// TODO: REMOVE & ROUTES
model StatisticElectricity {
  id        Int               @id @default(autoincrement())
  type      ElectricityType
  status    ElectricityStatus
  createdAt DateTime
  userId    String?
  user      User?             @relation(fields: [userId], references: [id])
  deviceId  Int?
  device    Device?           @relation(fields: [deviceId], references: [id])
}

// Inventary
enum InventaryType {
  HOP
  YEAST
  MALT
  OTHER
}

model InventaryItem {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  amount      Int
  createdAt   DateTime @default(now())
  unit        String
  type        String
  extraData   Json
}

model SearchIndex {
  id          Int      @id @default(autoincrement())
  name        String
  description String?  @db.Text
  updatedAt   DateTime @default(now())
  createdAt   DateTime @default(now())
  type        String
  extraData   Json     @default("{}")

  @@fulltext([name])
}

// PushNotification
model PushSubscription {
  id               Int                @id @default(autoincrement())
  endpoint         String             @db.Text
  userAgent        String             @db.Text
  keys             Json
  createdAt        DateTime           @default(now())
  PushNotification PushNotification[]
  userId           String?
  user             User?              @relation(fields: [userId], references: [id])
}

model PushNotification {
  id                 Int              @id @default(autoincrement())
  data               Json
  createdAt          DateTime         @default(now())
  pushSubscription   PushSubscription @relation(fields: [pushSubscriptionId], references: [id], onDelete: Cascade)
  pushSubscriptionId Int
  userId             String?
  user               User?            @relation(fields: [userId], references: [id])
}

// Recipe
model Recipe {
  id              Int                 @id @default(autoincrement())
  name            String
  brewer          String
  batchSize       Float               @default(0)
  boilSize        Float               @default(0)
  boilTime        Float               @default(0)
  efficiency      Float?
  ibu             String?
  abv             String?
  age             Int?
  ageTemperature  Float?
  preGravity      Float?
  finishedGravity Float?
  createdAt       DateTime            @default(now())
  file            String?             @db.Text
  hops            RecipeHop[]
  yeasts          RecipeYeast[]
  mashs           RecipeMash[]
  Brewing         Brewing[]
  fermentables    RecipeFermentable[]
  Temperature     Temperature[]
}

model RecipeHop {
  id       Int    @id @default(autoincrement())
  name     String
  amount   Float
  alpha    Float
  form     String @default("PELLET")
  time     Int    @default(0)
  type     String @default("AROMA")
  use      String @default("BOIL")
  recipeId Int
  recipe   Recipe @relation(fields: [recipeId], references: [id], onDelete: Cascade)
}

model RecipeYeast {
  id       Int    @id @default(autoincrement())
  name     String
  amount   Float  @default(0)
  recipeId Int
  recipe   Recipe @relation(fields: [recipeId], references: [id], onDelete: Cascade)
}

model RecipeMash {
  id       Int              @id @default(autoincrement())
  name     String
  recipeId Int
  recipe   Recipe           @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  steps    RecipeMashStep[]
}

model RecipeMashStep {
  id           Int        @id @default(autoincrement())
  name         String
  infuseAmount Float
  temperature  Float
  time         Int
  type         String
  recipeMashId Int
  recipe       RecipeMash @relation(fields: [recipeMashId], references: [id], onDelete: Cascade)
}

model RecipeFermentable {
  id           Int      @id @default(autoincrement())
  recipeId     Int
  recipe       Recipe   @relation(fields: [recipeId], references: [id], onDelete: Cascade)
  name         String
  type         String? // "Grain", "Sugar", "Extract", "Dry Extract" or "Adjunct"
  amount       Float
  yield        Float?
  color        Float?
  addAfterBoil Boolean? @default(false)
  notes        String?  @db.Text
}

// ApiKey
model ApiKey {
  id        Int      @id @default(autoincrement())
  key       String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  Device    Device[]
  userId    String
  user      User     @relation(fields: [userId], references: [id])
}

model MqttClient {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String
  clientId  String
  createdAt DateTime @default(now())
  deviceId  Int
  Device    Device   @relation(fields: [deviceId], references: [id])
}

model SpindelLog {
  id          Int      @id @default(autoincrement())
  spindelName String
  angle       Float
  temperature Float
  gravity     Float
  battery     Float
  createdAt   DateTime @default(now())
  request     Json
  deviceId    Int
  Device      Device   @relation(fields: [deviceId], references: [id])
}

// Allgemeine Integration
model IntegrationData {
  id          Int      @id @default(autoincrement())
  integration String
  key         String
  value       Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now())
}

// Authentication
model User {
  id                   String                 @id @default(cuid())
  email                String                 @unique
  password             String
  name                 String?
  image                String?                @db.Text
  isEmailVerified      Boolean                @default(false)
  lastLogin            DateTime?
  sessions             Session[]
  Device               Device[]
  PushSubscription     PushSubscription[]
  Temperature          Temperature[]
  PushNotification     PushNotification[]
  StatisticValues      StatisticValues[]
  StatisticElectricity StatisticElectricity[]
  ApiKey               ApiKey[]
  Notification         Notification[]
}

model Session {
  id        String   @id
  userId    String
  expiresAt DateTime
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)
}

// enum EmailCodeType {
//   login
//   verification
// }

// model EmailCode {
//   id        Int           @id @default(autoincrement())
//   code      String
//   email     String
//   userGuid  String
//   user      User          @relation(fields: [userGuid], references: [guid])
//   expiresAt DateTime
//   type      EmailCodeType
//   createdAt DateTime      @default(now())
// }

model Notification {
  id        Int      @id @default(autoincrement())
  title     String?
  text      String
  data      Json
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
}
