import * as z from "zod"
import { CompletePushSubscription, RelatedPushSubscriptionModel, CompleteUser, RelatedUserModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: <PERSON><PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const PushNotificationModel = z.object({
  id: z.number().int(),
  data: jsonSchema,
  createdAt: z.date(),
  pushSubscriptionId: z.number().int(),
  userId: z.string().nullish(),
})

export interface CompletePushNotification extends z.infer<typeof PushNotificationModel> {
  pushSubscription: CompletePushSubscription
  user?: CompleteUser | null
}

/**
 * RelatedPushNotificationModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedPushNotificationModel: z.ZodSchema<CompletePushNotification> = z.lazy(() => PushNotificationModel.extend({
  pushSubscription: RelatedPushSubscriptionModel,
  user: RelatedUserModel.nullish(),
}))
