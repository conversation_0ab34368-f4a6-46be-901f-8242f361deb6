import * as z from "zod"
import { CompleteRecipeHop, RelatedRecipeHopModel, CompleteRecipeYeast, RelatedRecipeYeastModel, CompleteRecipeMash, RelatedRecipeMashModel, CompleteBrewing, RelatedBrewingModel, CompleteRecipeFermentable, RelatedRecipeFermentableModel, CompleteTemperature, RelatedTemperatureModel } from "./index"

export const RecipeModel = z.object({
  id: z.number().int(),
  name: z.string(),
  brewer: z.string(),
  batchSize: z.number(),
  boilSize: z.number(),
  boilTime: z.number(),
  efficiency: z.number().nullish(),
  ibu: z.string().nullish(),
  abv: z.string().nullish(),
  age: z.number().int().nullish(),
  ageTemperature: z.number().nullish(),
  preGravity: z.number().nullish(),
  finishedGravity: z.number().nullish(),
  createdAt: z.date(),
  file: z.string().nullish(),
})

export interface CompleteRecipe extends z.infer<typeof RecipeModel> {
  hops: CompleteRecipeHop[]
  yeasts: CompleteRecipeYeast[]
  mashs: CompleteRecipeMash[]
  Brewing: CompleteBrewing[]
  fermentables: CompleteRecipeFermentable[]
  Temperature: CompleteTemperature[]
}

/**
 * RelatedRecipeModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeModel: z.ZodSchema<CompleteRecipe> = z.lazy(() => RecipeModel.extend({
  hops: RelatedRecipeHopModel.array(),
  yeasts: RelatedRecipeYeastModel.array(),
  mashs: RelatedRecipeMashModel.array(),
  Brewing: RelatedBrewingModel.array(),
  fermentables: RelatedRecipeFermentableModel.array(),
  Temperature: RelatedTemperatureModel.array(),
}))
