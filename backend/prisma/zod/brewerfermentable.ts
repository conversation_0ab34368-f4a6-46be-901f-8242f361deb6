import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewerFermentableModel = z.object({
  id: z.number().int(),
  name: z.string(),
  type: z.string().nullish(),
  amount: z.number(),
  addAfterBoil: z.boolean().nullish(),
  brewingId: z.number().int(),
})

export interface CompleteBrewerFermentable extends z.infer<typeof BrewerFermentableModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewerFermentableModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewerFermentableModel: z.ZodSchema<CompleteBrewerFermentable> = z.lazy(() => BrewerFermentableModel.extend({
  brewing: RelatedBrewingModel,
}))
