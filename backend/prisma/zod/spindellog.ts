import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: J<PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const SpindelLogModel = z.object({
  id: z.number().int(),
  spindelName: z.string(),
  angle: z.number(),
  temperature: z.number(),
  gravity: z.number(),
  battery: z.number(),
  createdAt: z.date(),
  request: jsonSchema,
  deviceId: z.number().int(),
})

export interface CompleteSpindelLog extends z.infer<typeof SpindelLogModel> {
  Device: CompleteDevice
}

/**
 * RelatedSpindelLogModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedSpindelLogModel: z.ZodSchema<CompleteSpindelLog> = z.lazy(() => SpindelLogModel.extend({
  Device: RelatedDeviceModel,
}))
