import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewingFermentableModel = z.object({
  id: z.number().int(),
  name: z.string(),
  type: z.string().nullish(),
  amount: z.number(),
  addAfterBoil: z.boolean().nullish(),
  brewingId: z.number().int(),
  added: z.boolean(),
})

export interface CompleteBrewingFermentable extends z.infer<typeof BrewingFermentableModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewingFermentableModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewingFermentableModel: z.ZodSchema<CompleteBrewingFermentable> = z.lazy(() => BrewingFermentableModel.extend({
  brewing: RelatedBrewingModel,
}))
