import * as z from "zod"
import { CompleteRecipe, RelatedRecipeModel } from "./index"

export const RecipeHopModel = z.object({
  id: z.number().int(),
  name: z.string(),
  amount: z.number(),
  alpha: z.number(),
  form: z.string(),
  time: z.number().int(),
  type: z.string(),
  use: z.string(),
  recipeId: z.number().int(),
})

export interface CompleteRecipeHop extends z.infer<typeof RecipeHopModel> {
  recipe: CompleteRecipe
}

/**
 * RelatedRecipeHopModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeHopModel: z.ZodSchema<CompleteRecipeHop> = z.lazy(() => RecipeHopModel.extend({
  recipe: RelatedRecipeModel,
}))
