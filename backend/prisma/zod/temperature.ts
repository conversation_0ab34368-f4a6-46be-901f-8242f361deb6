import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel, CompleteRecipe, RelatedRecipeModel, CompleteUser, RelatedUserModel, CompleteDevice, RelatedDeviceModel } from "./index"

export const TemperatureModel = z.object({
  id: z.number().int(),
  temperature: z.number().int(),
  startDate: z.date(),
  endDate: z.date().nullish(),
  sendPush: z.boolean(),
  createdAt: z.date(),
  recipeName: z.string().nullish(),
  gravityNotify: z.number().nullish(),
  brewId: z.number().int().nullish(),
  recipeId: z.number().int().nullish(),
  userId: z.string().nullish(),
  deviceId: z.number().int().nullish(),
})

export interface CompleteTemperature extends z.infer<typeof TemperatureModel> {
  brew?: CompleteBrewing | null
  recipe?: CompleteRecipe | null
  user?: CompleteUser | null
  device?: CompleteDevice | null
}

/**
 * RelatedTemperatureModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedTemperatureModel: z.ZodSchema<CompleteTemperature> = z.lazy(() => TemperatureModel.extend({
  brew: RelatedBrewingModel.nullish(),
  recipe: RelatedRecipeModel.nullish(),
  user: RelatedUserModel.nullish(),
  device: RelatedDeviceModel.nullish(),
}))
