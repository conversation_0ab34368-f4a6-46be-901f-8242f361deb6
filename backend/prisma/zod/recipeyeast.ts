import * as z from "zod"
import { CompleteRecipe, RelatedRecipeModel } from "./index"

export const RecipeYeastModel = z.object({
  id: z.number().int(),
  name: z.string(),
  amount: z.number(),
  recipeId: z.number().int(),
})

export interface CompleteRecipeYeast extends z.infer<typeof RecipeYeastModel> {
  recipe: CompleteRecipe
}

/**
 * RelatedRecipeYeastModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeYeastModel: z.ZodSchema<CompleteRecipeYeast> = z.lazy(() => RecipeYeastModel.extend({
  recipe: RelatedRecipeModel,
}))
