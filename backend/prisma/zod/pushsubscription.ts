import * as z from "zod"
import { CompletePushNotification, RelatedPushNotificationModel, CompleteUser, RelatedUserModel } from "./index"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: <PERSON><PERSON> } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const PushSubscriptionModel = z.object({
  id: z.number().int(),
  endpoint: z.string(),
  userAgent: z.string(),
  keys: jsonSchema,
  createdAt: z.date(),
  userId: z.string().nullish(),
})

export interface CompletePushSubscription extends z.infer<typeof PushSubscriptionModel> {
  PushNotification: CompletePushNotification[]
  user?: CompleteUser | null
}

/**
 * RelatedPushSubscriptionModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedPushSubscriptionModel: z.ZodSchema<CompletePushSubscription> = z.lazy(() => PushSubscriptionModel.extend({
  PushNotification: RelatedPushNotificationModel.array(),
  user: RelatedUserModel.nullish(),
}))
