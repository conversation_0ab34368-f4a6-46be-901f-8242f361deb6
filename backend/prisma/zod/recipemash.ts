import * as z from "zod"
import { CompleteRecipe, RelatedRecipeModel, CompleteRecipeMashStep, RelatedRecipeMashStepModel } from "./index"

export const RecipeMashModel = z.object({
  id: z.number().int(),
  name: z.string(),
  recipeId: z.number().int(),
})

export interface CompleteRecipeMash extends z.infer<typeof RecipeMashModel> {
  recipe: CompleteRecipe
  steps: CompleteRecipeMashStep[]
}

/**
 * RelatedRecipeMashModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeMashModel: z.ZodSchema<CompleteRecipeMash> = z.lazy(() => RecipeMashModel.extend({
  recipe: RelatedRecipeModel,
  steps: RelatedRecipeMashStepModel.array(),
}))
