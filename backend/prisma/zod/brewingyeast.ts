import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewingYeastModel = z.object({
  id: z.number().int(),
  name: z.string(),
  fermentationTemperature: z.number(),
  amount: z.number(),
  brewingId: z.number().int(),
})

export interface CompleteBrewingYeast extends z.infer<typeof BrewingYeastModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewingYeastModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewingYeastModel: z.ZodSchema<CompleteBrewingYeast> = z.lazy(() => BrewingYeastModel.extend({
  brewing: RelatedBrewingModel,
}))
