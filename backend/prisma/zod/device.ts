import * as z from "zod"
import { Device<PERSON>perator, DeviceType } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteDeviceConnected, RelatedDeviceConnectedModel, CompleteMqttClient, RelatedMqttClientModel, CompleteDeviceWorkingLog, RelatedDeviceWorkingLogModel, CompleteSpindelLog, RelatedSpindelLogModel, CompleteApiKey, RelatedApiKeyModel, CompleteStatisticValues, RelatedStatisticValuesModel, CompleteTemperature, RelatedTemperatureModel, CompleteStatisticElectricity, RelatedStatisticElectricityModel } from "./index"

export const DeviceModel = z.object({
  id: z.number().int(),
  name: z.string(),
  operator: z.nativeEnum(DeviceOperator),
  operatorDeviceId: z.string().nullish(),
  tuyaDeviceId: z.string().nullish(),
  createdAt: z.date(),
  updatedAt: z.date(),
  userId: z.string().nullish(),
  type: z.nativeEnum(DeviceType),
  apiKeyId: z.number().int().nullish(),
  deviceConnectedId: z.number().int().nullish(),
})

export interface CompleteDevice extends z.infer<typeof DeviceModel> {
  user?: CompleteUser | null
  Connection: CompleteDeviceConnected[]
  mqttClient: CompleteMqttClient[]
  DeviceWorkingLog: CompleteDeviceWorkingLog[]
  SpindelLog: CompleteSpindelLog[]
  ApiKey?: CompleteApiKey | null
  StatisticValues: CompleteStatisticValues[]
  Temperature: CompleteTemperature[]
  deviceConnections: CompleteDevice[]
  deviceConnected?: CompleteDevice | null
  StatisticElectricity: CompleteStatisticElectricity[]
}

/**
 * RelatedDeviceModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDeviceModel: z.ZodSchema<CompleteDevice> = z.lazy(() => DeviceModel.extend({
  user: RelatedUserModel.nullish(),
  Connection: RelatedDeviceConnectedModel.array(),
  mqttClient: RelatedMqttClientModel.array(),
  DeviceWorkingLog: RelatedDeviceWorkingLogModel.array(),
  SpindelLog: RelatedSpindelLogModel.array(),
  ApiKey: RelatedApiKeyModel.nullish(),
  StatisticValues: RelatedStatisticValuesModel.array(),
  Temperature: RelatedTemperatureModel.array(),
  deviceConnections: RelatedDeviceModel.array(),
  deviceConnected: RelatedDeviceModel.nullish(),
  StatisticElectricity: RelatedStatisticElectricityModel.array(),
}))
