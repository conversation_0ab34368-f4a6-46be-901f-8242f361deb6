import * as z from "zod"
import { ElectricityType, ElectricityStatus } from "@prisma/client"
import { CompleteUser, RelatedUserModel, CompleteDevice, RelatedDeviceModel } from "./index"

export const StatisticElectricityModel = z.object({
  id: z.number().int(),
  type: z.nativeEnum(ElectricityType),
  status: z.nativeEnum(ElectricityStatus),
  createdAt: z.date(),
  userId: z.string().nullish(),
  deviceId: z.number().int().nullish(),
})

export interface CompleteStatisticElectricity extends z.infer<typeof StatisticElectricityModel> {
  user?: CompleteUser | null
  device?: CompleteDevice | null
}

/**
 * RelatedStatisticElectricityModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedStatisticElectricityModel: z.ZodSchema<CompleteStatisticElectricity> = z.lazy(() => StatisticElectricityModel.extend({
  user: RelatedUserModel.nullish(),
  device: RelatedDeviceModel.nullish(),
}))
