import * as z from "zod"
import { BrewingStatus } from "@prisma/client"
import { CompleteRecipe, RelatedRecipeModel, CompleteTemperature, RelatedTemperatureModel, CompleteBrewingMashStep, RelatedBrewingMashStepModel, CompleteBrewingHop, RelatedBrewingHopModel, CompleteBrewingYeast, RelatedBrewingYeastModel, CompleteBrewingFermentable, RelatedBrewingFermentableModel } from "./index"

export const BrewingModel = z.object({
  id: z.number().int(),
  createdAt: z.date(),
  startTime: z.date().nullish(),
  endTime: z.date().nullish(),
  mashType: z.string(),
  fermentationTemperature: z.number(),
  lauteringPauseTime: z.date().nullish(),
  boilTime: z.number().int(),
  boilStartTime: z.date().nullish(),
  status: z.nativeEnum(BrewingStatus).nullish(),
  recipeId: z.number().int().nullish(),
})

export interface CompleteBrewing extends z.infer<typeof BrewingModel> {
  recipe?: CompleteRecipe | null
  Temperature: CompleteTemperature[]
  mashSteps: CompleteBrewingMashStep[]
  hops: CompleteBrewingHop[]
  yeasts: CompleteBrewingYeast[]
  fermentables: CompleteBrewingFermentable[]
}

/**
 * RelatedBrewingModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewingModel: z.ZodSchema<CompleteBrewing> = z.lazy(() => BrewingModel.extend({
  recipe: RelatedRecipeModel.nullish(),
  Temperature: RelatedTemperatureModel.array(),
  mashSteps: RelatedBrewingMashStepModel.array(),
  hops: RelatedBrewingHopModel.array(),
  yeasts: RelatedBrewingYeastModel.array(),
  fermentables: RelatedBrewingFermentableModel.array(),
}))
