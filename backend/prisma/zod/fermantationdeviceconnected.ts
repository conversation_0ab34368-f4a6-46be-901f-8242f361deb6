import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel } from "./index"

export const FermantationDeviceConnectedModel = z.object({
  id: z.number().int(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deviceId: z.number().int(),
  type: z.string(),
})

export interface CompleteFermantationDeviceConnected extends z.infer<typeof FermantationDeviceConnectedModel> {
  device: CompleteDevice
}

/**
 * RelatedFermantationDeviceConnectedModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedFermantationDeviceConnectedModel: z.ZodSchema<CompleteFermantationDeviceConnected> = z.lazy(() => FermantationDeviceConnectedModel.extend({
  device: RelatedDeviceModel,
}))
