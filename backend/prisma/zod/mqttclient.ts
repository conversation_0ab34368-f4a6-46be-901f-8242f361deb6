import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel } from "./index"

export const MqttClientModel = z.object({
  id: z.number().int(),
  username: z.string(),
  password: z.string(),
  clientId: z.string(),
  createdAt: z.date(),
  deviceId: z.number().int(),
})

export interface CompleteMqttClient extends z.infer<typeof MqttClientModel> {
  Device: CompleteDevice
}

/**
 * RelatedMqttClientModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedMqttClientModel: z.ZodSchema<CompleteMqttClient> = z.lazy(() => MqttClientModel.extend({
  Device: RelatedDeviceModel,
}))
