import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewingHopModel = z.object({
  id: z.number().int(),
  name: z.string(),
  amount: z.number(),
  alpha: z.number(),
  form: z.string(),
  time: z.number().int(),
  type: z.string(),
  use: z.string(),
  added: z.boolean(),
  brewingId: z.number().int(),
  sendPushNotification: z.boolean(),
})

export interface CompleteBrewingHop extends z.infer<typeof BrewingHopModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewingHopModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewingHopModel: z.ZodSchema<CompleteBrewingHop> = z.lazy(() => BrewingHopModel.extend({
  brewing: RelatedBrewingModel,
}))
