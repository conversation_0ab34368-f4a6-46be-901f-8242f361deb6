import * as z from "zod"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewerLogYeastModel = z.object({
  id: z.number().int(),
  name: z.string(),
  fermentationTemperature: z.number(),
  amount: z.number(),
  brewingId: z.number().int(),
})

export interface CompleteBrewerLogYeast extends z.infer<typeof BrewerLogYeastModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewerLogYeastModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewerLogYeastModel: z.ZodSchema<CompleteBrewerLogYeast> = z.lazy(() => BrewerLogYeastModel.extend({
  brewing: RelatedBrewingModel,
}))
