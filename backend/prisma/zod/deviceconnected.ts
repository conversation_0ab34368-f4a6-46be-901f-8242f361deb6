import * as z from "zod"
import { DeviceConnectedUse } from "@prisma/client"
import { CompleteDevice, RelatedDeviceModel } from "./index"

export const DeviceConnectedModel = z.object({
  id: z.number().int(),
  createdAt: z.date(),
  updatedAt: z.date(),
  deviceId: z.number().int(),
  type: z.string(),
  use: z.nativeEnum(DeviceConnectedUse),
})

export interface CompleteDeviceConnected extends z.infer<typeof DeviceConnectedModel> {
  device: CompleteDevice
}

/**
 * RelatedDeviceConnectedModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDeviceConnectedModel: z.ZodSchema<CompleteDeviceConnected> = z.lazy(() => DeviceConnectedModel.extend({
  device: RelatedDeviceModel,
}))
