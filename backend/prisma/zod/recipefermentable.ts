import * as z from "zod"
import { CompleteRecipe, RelatedRecipeModel } from "./index"

export const RecipeFermentableModel = z.object({
  id: z.number().int(),
  recipeId: z.number().int(),
  name: z.string(),
  type: z.string().nullish(),
  amount: z.number(),
  yield: z.number().nullish(),
  color: z.number().nullish(),
  addAfterBoil: z.boolean().nullish(),
  notes: z.string().nullish(),
})

export interface CompleteRecipeFermentable extends z.infer<typeof RecipeFermentableModel> {
  recipe: CompleteRecipe
}

/**
 * RelatedRecipeFermentableModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeFermentableModel: z.ZodSchema<CompleteRecipeFermentable> = z.lazy(() => RecipeFermentableModel.extend({
  recipe: RelatedRecipeModel,
}))
