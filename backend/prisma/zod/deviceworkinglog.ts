import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel } from "./index"

export const DeviceWorkingLogModel = z.object({
  id: z.number().int(),
  deviceId: z.number().int(),
  startTime: z.date(),
  endTime: z.date().nullish(),
  createdAt: z.date(),
})

export interface CompleteDeviceWorkingLog extends z.infer<typeof DeviceWorkingLogModel> {
  device: CompleteDevice
}

/**
 * RelatedDeviceWorkingLogModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedDeviceWorkingLogModel: z.ZodSchema<CompleteDeviceWorkingLog> = z.lazy(() => DeviceWorkingLogModel.extend({
  device: RelatedDeviceModel,
}))
