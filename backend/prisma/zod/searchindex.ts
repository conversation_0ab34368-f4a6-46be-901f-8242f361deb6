import * as z from "zod"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const SearchIndexModel = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().nullish(),
  updatedAt: z.date(),
  createdAt: z.date(),
  type: z.string(),
  extraData: jsonSchema,
})
