import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel, CompleteUser, RelatedUserModel } from "./index"

export const ApiKeyModel = z.object({
  id: z.number().int(),
  key: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
  userId: z.string(),
})

export interface CompleteApiKey extends z.infer<typeof ApiKeyModel> {
  Device: CompleteDevice[]
  user: CompleteUser
}

/**
 * RelatedApiKeyModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedApiKeyModel: z.ZodSchema<CompleteApiKey> = z.lazy(() => ApiKeyModel.extend({
  Device: RelatedDeviceModel.array(),
  user: RelatedUserModel,
}))
