import * as z from "zod"
import { CompleteRecipeMash, RelatedRecipeMashModel } from "./index"

export const RecipeMashStepModel = z.object({
  id: z.number().int(),
  name: z.string(),
  infuseAmount: z.number(),
  temperature: z.number(),
  time: z.number().int(),
  type: z.string(),
  recipeMashId: z.number().int(),
})

export interface CompleteRecipeMashStep extends z.infer<typeof RecipeMashStepModel> {
  recipe: CompleteRecipeMash
}

/**
 * RelatedRecipeMashStepModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedRecipeMashStepModel: z.ZodSchema<CompleteRecipeMashStep> = z.lazy(() => RecipeMashStepModel.extend({
  recipe: RelatedRecipeMashModel,
}))
