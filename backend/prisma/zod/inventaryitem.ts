import * as z from "zod"

// Helper schema for JSON fields
type Literal = boolean | number | string
type Json = Literal | { [key: string]: Json } | Json[]
const literalSchema = z.union([z.string(), z.number(), z.boolean()])
const jsonSchema: z.ZodSchema<Json> = z.lazy(() => z.union([literalSchema, z.array(jsonSchema), z.record(jsonSchema)]))

export const InventaryItemModel = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().nullish(),
  amount: z.number().int(),
  createdAt: z.date(),
  unit: z.string(),
  type: z.string(),
  extraData: jsonSchema,
})
