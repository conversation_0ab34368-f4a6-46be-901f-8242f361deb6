import * as z from "zod"
import { BrewingMashStepStatus } from "@prisma/client"
import { CompleteBrewing, RelatedBrewingModel } from "./index"

export const BrewingMashStepModel = z.object({
  id: z.number().int(),
  name: z.string(),
  infuseAmount: z.number(),
  temperature: z.number(),
  time: z.number().int(),
  type: z.string(),
  startTime: z.date().nullish(),
  endTime: z.date().nullish(),
  status: z.nativeEnum(BrewingMashStepStatus),
  order: z.number().int(),
  brewingId: z.number().int(),
  heatingStart: z.date().nullish(),
})

export interface CompleteBrewingMashStep extends z.infer<typeof BrewingMashStepModel> {
  brewing: CompleteBrewing
}

/**
 * RelatedBrewingMashStepModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedBrewingMashStepModel: z.ZodSchema<CompleteBrewingMashStep> = z.lazy(() => BrewingMashStepModel.extend({
  brewing: RelatedBrewingModel,
}))
