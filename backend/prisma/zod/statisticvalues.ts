import * as z from "zod"
import { CompleteUser, RelatedUserModel, CompleteDevice, RelatedDeviceModel } from "./index"

export const StatisticValuesModel = z.object({
  id: z.number().int(),
  temperature: z.number(),
  createdAt: z.date(),
  userId: z.string().nullish(),
  deviceId: z.number().int().nullish(),
})

export interface CompleteStatisticValues extends z.infer<typeof StatisticValuesModel> {
  user?: CompleteUser | null
  device?: CompleteDevice | null
}

/**
 * RelatedStatisticValuesModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedStatisticValuesModel: z.ZodSchema<CompleteStatisticValues> = z.lazy(() => StatisticValuesModel.extend({
  user: RelatedUserModel.nullish(),
  device: RelatedDeviceModel.nullish(),
}))
