import * as z from "zod"
import { CompleteDevice, RelatedDeviceModel } from "./index"

export const FermantationModel = z.object({
  id: z.number().int(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export interface CompleteFermantation extends z.infer<typeof FermantationModel> {
  devices: CompleteDevice[]
}

/**
 * RelatedFermantationModel contains all relations on your model in addition to the scalars
 *
 * NOTE: Lazy required in case of potential circular dependencies within schema
 */
export const RelatedFermantationModel: z.ZodSchema<CompleteFermantation> = z.lazy(() => FermantationModel.extend({
  devices: RelatedDeviceModel.array(),
}))
