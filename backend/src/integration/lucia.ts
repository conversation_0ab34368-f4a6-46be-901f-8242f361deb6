import prisma from '../helpers/prisma'
import { User } from '@prisma/client'

// TODO: https://github.com/lucia-auth/examples/tree/main/express
// TODO: https://thecopenhagenbook.com

let _lucia: any = null

export type SessionUser = {
  id: string
  isEmailVerified: boolean
  email: string
}

export const getLucia = async () => {
  if (_lucia) {
    return _lucia
  }

  const { PrismaAdapter } = await import('@lucia-auth/adapter-prisma')
  const { Lucia } = await import('lucia')

  const adapter = new PrismaAdapter(prisma.session, prisma.user)

  const lucia = new Lucia(adapter, {
    sessionCookie: {
      attributes: {
        // set to `true` when using HTTPS
        secure: process.env.NODE_ENV === 'production',
      },
    },
    getUserAttributes: attributes => {
      const user = attributes as User
      return {
        id: user.id,
        // isEmailVerified: user.isEmailVerified,
        email: user.email,
      } as SessionUser
    },
  })

  _lucia = lucia

  return lucia
}

// !important
// TODO: verschieben
// declare module 'lucia' {
//   interface Register {
//     Lucia: Awaited<typeof import('lucia')>['Lucia']
//     DatabaseUserAttributes: {
//       email: string
//       email_verified: boolean
//     }
//   }
// }

// export const EXPIRED_CODE_TIME = 30

// export async function generateEmailVerificationCode(user: User): Promise<string> {
//   const { TimeSpan, createDate } = await import('oslo')
//   const { generateRandomString, alphabet } = await import('oslo/crypto')

//   await prisma.emailCode.deleteMany({
//     where: {
//       userGuid: user.guid,
//       type: 'login',
//     },
//   })

//   const code = generateRandomString(6, alphabet('0-9', 'A-Z'))

//   await prisma.emailCode.create({
//     data: {
//       userGuid: user.guid,
//       email: user.email,
//       code,
//       type: 'login',
//       expiresAt: createDate(new TimeSpan(EXPIRED_CODE_TIME, 'm')),
//     },
//   })

//   return code
// }
