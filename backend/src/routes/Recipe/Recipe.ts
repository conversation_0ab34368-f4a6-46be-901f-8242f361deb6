import { <PERSON><PERSON><PERSON>, Reci<PERSON> } from '@prisma/client'
import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import * as z from 'zod'
import {
  RecipeFermentableModel,
  RecipeYeastModel,
  RecipeMashStepModel,
  RecipeHopModel,
  RecipeMashModel,
  RecipeModel,
} from '../../../prisma/zod'
import { getBraurekaRecipes } from '../../service/Integration/Braureka'
import nodeCache from '../../helpers/nodeCache'

const getRecipes = async (limit?: number) => {
  const model = await prisma.recipe.findMany({
    orderBy: {
      createdAt: 'desc',
    },
    take: limit,
  })

  const cachedRecipes = nodeCache.get('braurekaRecipes')
  if (cachedRecipes === 'EMPTY') {
    return model
  } else if (cachedRecipes !== undefined) {
    return [...model, ...(cachedRecipes as Recipe[])]
  }

  const braurekaRecipes = await getBraurekaRecipes()
  if (braurekaRecipes !== undefined && braurekaRecipes.length > 0) {
    nodeCache.set('braurekaRecipes', braurekaRecipes, 5 * 60)
    return [...model, ...braurekaRecipes]
  } else {
    nodeCache.set('braurekaRecipes', 'EMPTY')
  }

  return model
}

app.get('/recipe', async c => {
  const limit = c.req.query('limit') ? Number(c.req.query('limit')) : undefined

  const data = await getRecipes(limit)

  const sortedData = data.sort((a, b) => {
    return Number(b.createdAt) - Number(a.createdAt)
  })

  if (limit && data.length > limit) {
    return c.json(sortedData.slice(0, limit))
  }

  return c.json(sortedData)
})

app.get('/recipe/:recipeId', async c => {
  const model = await prisma.recipe.findUnique({
    where: {
      id: Number(c.req.param('recipeId')),
    },
    include: {
      hops: true,
      yeasts: true,
      mashs: {
        include: {
          steps: true,
        },
      },
      fermentables: true,
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

app.post('/recipe', async c => {
  const HopModel = RecipeHopModel.omit({ recipeId: true, id: true })
  const YeastModel = RecipeYeastModel.omit({ recipeId: true, id: true })
  const FermentableModel = RecipeFermentableModel.omit({ recipeId: true, id: true })

  const MashModel = RecipeMashModel.omit({ recipeId: true, id: true })
  const StepModel = RecipeMashStepModel.omit({ recipeMashId: true, id: true })

  const recipe = validate<Prisma.RecipeCreateInput>(
    RecipeModel.extend({
      hops: z.object({
        create: z.array(HopModel),
      }),
      mashs: z.object({
        create: z.array(
          MashModel.extend({
            steps: z.object({
              create: z.array(StepModel),
            }),
          })
        ),
      }),
      yeasts: z.object({
        create: z.array(YeastModel),
      }),
      fermentables: z.object({
        create: z.array(FermentableModel),
      }),
    }),
    await c.req.json()
  )

  const response = await prisma.recipe.create({
    data: recipe,
  })

  return c.json(response)
})

app.put('/recipe/:recipeId', async c => {
  RecipeModel

  const data = validate<Prisma.RecipeCreateInput>(RecipeModel, await c.req.json())

  const response = await prisma.recipe.update({
    where: { id: Number(c.req.param('recipeId')) },
    data: data,
  })

  return c.json(response)
})

app.delete('/recipe/:recipeId', async c => {
  const model = await prisma.recipe.delete({
    where: { id: Number(c.req.param('recipeId')) },
  })

  return c.json(model)
})
