// RateLimitError: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.
//   at Function.generate (/Users/<USER>/Projekte/Privat/gaerschrank/node_modules/openai/src/error.ts:94:14)
//   at OpenAI.makeStatusError (/Users/<USER>/Projekte/Privat/gaerschrank/node_modules/openai/src/core.ts:431:21)
//   at OpenAI.makeRequest (/Users/<USER>/Projekte/Privat/gaerschrank/node_modules/openai/src/core.ts:495:24)
//   at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
//   at <anonymous> (/Users/<USER>/Projekte/Privat/gaerschrank/backend/src/routes/Recipe/RecipeFileDetection.ts:69:24)
//   at async dispatch (file:///Users/<USER>/Projekte/Privat/gaerschrank/node_modules/hono/dist/compose.js:29:17)
//   at <anonymous> (/Users/<USER>/Projekte/Privat/gaerschrank/backend/src/middlewares/AuthenticationMiddleware.ts:33:3)
//   at async dispatch (file:///Users/<USER>/Projekte/Privat/gaerschrank/node_modules/hono/dist/compose.js:29:17)
//   at async cors2 (file:///Users/<USER>/Projekte/Privat/gaerschrank/node_modules/hono/dist/middleware/cors/index.js:65:5)
//   at <anonymous> (/Users/<USER>/Projekte/Privat/gaerschrank/backend/src/helpers/hono.ts:17:10)

const error = {
  status: 429,
  headers: {
    'cf-cache-status': 'DYNAMIC',
    'cf-ray': '8ccdca45ce3762e5-HAM',
    connection: 'keep-alive',
    'content-length': '337',
    'content-type': 'application/json; charset=utf-8',
    date: 'Thu, 03 Oct 2024 14:58:24 GMT',
    server: 'cloudflare',
    'set-cookie':
      '__cf_bm=3kagLKQtj_8PtaPrf3HgYUQYZRNz2waoDmvTR78ppX4-1727967504-*******-WynjNqPj_XoEuLVN6FDVYHH7jxynw4YS_PNAH0ZeZSKt7RHyxr6dhtpyWc6sySiJCjlwo2KmaI.rePKXLh3GgA; path=/; expires=Thu, 03-Oct-24 15:28:24 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None, _cfuvid=GKgrUsRsRemRjbb1kJlXyKQ7dILaKyU8m5DPjSD9u80-1727967504452-0.0.1.1-604800000; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None',
    'strict-transport-security': 'max-age=31536000; includeSubDomains; preload',
    vary: 'Origin',
    'x-content-type-options': 'nosniff',
    'x-request-id': 'req_33f41c78e8a116ccd569d57abb636962',
  },
  request_id: 'req_33f41c78e8a116ccd569d57abb636962',
  error: {
    message:
      'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.',
    type: 'insufficient_quota',
    param: null,
    code: 'insufficient_quota',
  },
  code: 'insufficient_quota',
  param: null,
  type: 'insufficient_quota',
}
