import OpenAI, { RateLimitError } from 'openai'
import app from '../../helpers/hono'
import pdf from 'pdf-parse/lib/pdf-parse'

import { zodResponseFormat } from 'openai/helpers/zod'
import { z } from 'zod'
import {
  RecipeFermentableModel,
  RecipeYeastModel,
  RecipeMashStepModel,
  RecipeHopModel,
  RecipeMashModel,
  RecipeModel,
} from '../../../prisma/zod'
import prisma from '../../helpers/prisma'
import { Prisma } from '@prisma/client'

app.post('/file-to-recipe-detection', async c => {
  const formData = await c.req.formData()

  const file = formData.get('file') as File | null

  if (!file) {
    return c.json(
      {
        error: 'No file provided',
      },
      400
    )
  }

  const arrayBuffer = await file.arrayBuffer()
  const buffer = Buffer.from(arrayBuffer)

  const extract = await pdf(buffer)

  const openai = new OpenAI()

  const HopModel = RecipeHopModel.omit({ recipeId: true, id: true })
  const YeastModel = RecipeYeastModel.omit({ recipeId: true, id: true })
  const FermentableModel = RecipeFermentableModel.omit({ recipeId: true, id: true })

  const MashModel = RecipeMashModel.omit({ recipeId: true, id: true })
  const StepModel = RecipeMashStepModel.omit({ recipeMashId: true, id: true })

  const model = RecipeModel.extend({
    hops: z.object({
      create: z.array(HopModel),
    }),
    mashs: z.object({
      create: z.array(
        MashModel.extend({
          steps: z.object({
            create: z.array(StepModel),
          }),
        })
      ),
    }),
    yeasts: z.object({
      create: z.array(YeastModel),
    }),
    fermentables: z.object({
      create: z.array(FermentableModel),
    }),
  }).omit({ createdAt: true, updatedAt: true, id: true, file: true })

  try {
    const format = zodResponseFormat(model, 'Braurezept')

    const completion = await openai.chat.completions.parse({
      model: 'gpt-4o-mini-2024-07-18',
      messages: [
        { role: 'system', content: 'Extrahiere aus dem Text das Braurezept für Bier' },
        { role: 'user', content: extract.text },
      ],
      response_format: format,
    })

    const recipe = completion.choices[0].message.parsed as Prisma.RecipeCreateInput

    const response = await prisma.recipe.create({
      data: recipe,
    })

    return c.json({
      regonizedText: extract.text,
      regonizedRecipe: recipe,
      createdRecipe: response,
    })
  } catch (error) {
    if (error instanceof RateLimitError) {
      return c.json(
        {
          error: 'Rate limit exceeded',
          type: 'rate_limit',
        },
        429
      )
    }

    console.error(error)

    return c.json(
      {
        error: 'AI send an error',
      },
      400
    )
  }
})
