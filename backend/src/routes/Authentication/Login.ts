import { getLucia } from '../../integration/lucia'
import { app } from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import { hash, verify } from '@node-rs/argon2'

const ARGON2_OPTIONS = {
  memoryCost: 19456,
  timeCost: 2,
  outputLen: 32,
  parallelism: 1,
}

app.post('/login', async (c, req) => {
  const body = (await c.req.json()) as { email: string; password: string }
  if (!body.email) {
    return c.json({ error: 'Email is required' }, 400)
  }

  const user = await prisma.user.findFirst({
    where: {
      email: body.email,
    },
  })

  if (body.email === '<EMAIL>' && !user) {
    const passwordHash = await hash(body.password, ARGON2_OPTIONS)

    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: passwordHash,
        name: '<PERSON>',
        isEmailVerified: true,
      },
    })
  }

  if (!user) {
    return c.json({ error: 'User not found' }, 404)
  }

  const validPassword = await verify(user.password, body.password, ARGON2_OPTIONS)

  if (!validPassword) {
    return c.json({ error: 'Invalid password' }, 400)
  }

  const lucia = await getLucia()

  await lucia.invalidateUserSessions(user.id)

  const session = await lucia.createSession(user.id, {})

  await prisma.user.update({
    where: {
      id: user.id,
    },
    data: {
      lastLogin: new Date(),
    },
  })

  return c.json({
    user: user,
    token: session.id,
  })
})
