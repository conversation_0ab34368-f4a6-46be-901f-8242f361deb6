import app from '../helpers/hono'
import prisma from '../helpers/prisma'

app.get('/items/search', async c => {
  const q = c.req.query('q') as string

  if (!q || q.length == 0) {
    return c.json([])
  }

  const model = await prisma.searchIndex.findMany({
    where: {
      OR: [
        {
          name: {
            contains: q,
          },
        },
        {
          description: {
            contains: q,
          },
        },
      ],
    },
  })

  return c.json(model)
})

app.get('/:type/search', async c => {
  const q = c.req.query('q') as string

  if (!q || q.length == 0) {
    return c.json([])
  }

  if (!['hop', 'yeast'].includes(c.req.param('type').toLowerCase())) {
    c.status(400)
    return c.json({ error: 'Invalid type' })
  }

  const model = await prisma.searchIndex.findMany({
    where: {
      type: c.req.param('type').toLowerCase(),
      OR: [
        {
          name: {
            contains: q,
          },
        },
        {
          description: {
            contains: q,
          },
        },
      ],
    },
    take: 10,
  })

  return c.json(model)
})

app.get('/:type/levenshtein', async c => {
  const q = c.req.query('q') as string

  if (!q || q.length == 0) {
    return c.json([])
  }

  if (!['hop', 'yeast'].includes(c.req.param('type').toLowerCase())) {
    c.status(400)
    return c.json({ error: 'Invalid type' })
  }

  const distance = 4

  const data = (await prisma.$queryRawUnsafe(`SELECT id, 
      LEVENSHTEIN(name, '${q}') AS distance
      FROM SearchIndex
      WHERE
      LEVENSHTEIN(name, '${q}') < ${distance}
      ORDER BY distance ASC
      LIMIT 10
  `)) as { id: number; distance: number }[]

  const model = await prisma.searchIndex.findMany({
    where: {
      id: {
        in: data.map(x => x.id),
      },
    },
  })

  return c.json(model)
})
