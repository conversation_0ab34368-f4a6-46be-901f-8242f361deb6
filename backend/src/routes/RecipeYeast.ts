import { Prisma } from '@prisma/client'
import app from '../helpers/hono'
import prisma from '../helpers/prisma'
import validate from '../helpers/validate'
import { RecipeYeastModel } from '../../prisma/zod/recipeyeast'

app.get('/recipe/:recipeId/yeast', async c => {
  const model = await prisma.recipeYeast.findMany()

  return c.json(model)
})

app.get('/recipe/:recipeId/yeast/:yeastId', async c => {
  const model = await prisma.recipeYeast.findUnique({
    where: {
      id: Number(c.req.param('yeastId')),
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

// app.post('/recipe/:recipeId/yeast', async (c) => {
//   const recipe = validate<Prisma.RecipeYeastCreateInput>(
//     RecipeYeastModel.omit({ recipeId: true, id: true }),
//     await c.req.json()
//   )

//   const response = await prisma.recipeYeast.create({
//     data: recipe,
//   })

//   return c.json(response)
// })

app.get('/recipe/:recipeId/yeast/:yeastId', async c => {
  const data = validate<Prisma.RecipeYeastUpdateInput>(RecipeYeastModel, await c.req.json())

  const response = await prisma.recipeYeast.update({
    where: { id: Number(c.req.param('yeastId')) },
    data: data,
  })

  return c.json(response)
})

app.get('/recipe/:recipeId/yeast/:yeastId', async c => {
  const model = await prisma.recipeYeast.delete({
    where: { id: Number(c.req.param('yeastId')) },
  })

  return c.json(model)
})
