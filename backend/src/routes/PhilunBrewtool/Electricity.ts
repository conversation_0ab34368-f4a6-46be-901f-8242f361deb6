import { Prisma } from '@prisma/client'
import authApp from '../../helpers/hono'
import validate from '../../helpers/validate'
import { StatisticElectricityModel } from '../../../prisma/zod/statisticelectricity'
import prisma from '../../helpers/prisma'

authApp.post('/device/:deviceId/electricity', async c => {
  const model = await c.req.json()

  if (!model.createdAt) {
    model.createdAt = new Date()
  }

  const data = validate<Prisma.StatisticElectricityCreateInput>(StatisticElectricityModel, model)

  await prisma.statisticElectricity.create({
    data: data,
  })

  return c.json(data)
})
