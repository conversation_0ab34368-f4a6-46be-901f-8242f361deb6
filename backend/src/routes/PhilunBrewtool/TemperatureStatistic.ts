import authApp, { app, getUserId } from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { Prisma } from '@prisma/client'
import { StatisticValuesModel } from '../../../prisma/zod/statisticvalues'

authApp.get('/brewtool/:deviceId/temperature', async c => {
  const now = new Date()
  now.setDate(now.getDate() - 5)

  const device = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('deviceId')),
      userId: getUserId(c),
    },
  })

  if (!device) {
    return c.notFound()
  }

  const models = await prisma.statisticValues.findMany({
    where: {
      deviceId: Number(c.req.param('deviceId')),
      createdAt: {
        gte: now,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  return c.json(models)
})

authApp.get('/brewtool/:deviceId/temperature/actual', async c => {
  const models = await prisma.statisticValues.findFirst({
    orderBy: {
      createdAt: 'desc',
    },
  })

  return c.json(models)
})

// TODO: remove after update
app.post('/statistic', async c => {
  const model = await c.req.json()

  if (!model.createdAt) {
    model.createdAt = new Date()
  }

  const data = validate<Prisma.StatisticValuesCreateInput>(StatisticValuesModel, model)

  await prisma.statisticValues.create({
    data: data,
  })

  return c.json(model)
})
