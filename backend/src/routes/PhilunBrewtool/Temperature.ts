import { Prisma } from '@prisma/client'
import authApp, { app, getUserId } from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { TemperatureModel } from '../../../prisma/zod/temperature'

const finishActualTemperature = async () => {
  const actualTemperatute = await prisma.temperature.findFirst({
    where: { endDate: null },
  })

  if (actualTemperatute) {
    await prisma.temperature.update({
      where: { id: actualTemperatute.id },
      data: { endDate: new Date() },
    })
  }
}

authApp.post('/brewtool/:deviceId/config', async c => {
  const model = await c.req.json()

  model.startDate ??= new Date()

  const data = validate<Prisma.TemperatureCreateInput>(TemperatureModel, model)

  const userId = getUserId(c)
  if (!userId) {
    c.status(401)
    return c.json({ message: 'Unauthorized' })
  }

  await finishActualTemperature()

  const created = await prisma.temperature.create({
    data: {
      ...data,
      user: { connect: { id: userId } },
      device: { connect: { id: Number(c.req.param('deviceId')) } },
    },
  })

  return c.json(created)
})

authApp.put('/brewtool/:deviceId/config', async c => {
  const config = await prisma.temperature.findFirst({
    where: { endDate: null },
  })

  if (!config) {
    return c.notFound()
  }

  const data = validate<Prisma.TemperatureUpdateInput>(TemperatureModel, await c.req.json())

  const model = await prisma.temperature.update({
    where: { id: config?.id },
    data: data,
  })

  return c.json(model)
})
