import prisma from '../../helpers/prisma'
import app from '../../helpers/hono'
import { findActualTemperature } from '../../service/Temperature'

const getTemperatureConfig = async (deviceId: number) => findActualTemperature(deviceId)

const getTemperatureStatistic = async (deviceId: number) =>
  prisma.statisticValues.findFirst({
    where: {
      deviceId,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 1,
  })

app.get('/brewtool', async c => {
  const devices = await prisma.device.findMany({
    where: {
      userId: c.get('user')?.id,
      type: 'BREWTOOL',
    },
    include: {
      deviceConnections: true,
    },
  })

  const devicesWithExtras = await Promise.all(
    devices.map(async device => {
      const res = await Promise.all([
        getTemperatureConfig(device.id),
        getTemperatureStatistic(device.id),
      ])

      return {
        ...device,
        config: res[0],
        statistic: res[1],
      }
    })
  )

  return c.json(devicesWithExtras)
})

app.get('/brewtool/:deviceId', async c => {
  const device = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('deviceId')),
      userId: c.get('user')?.id,
      type: 'BREWTOOL',
    },
    include: {
      deviceConnections: true,
    },
  })

  if (!device) {
    return c.notFound()
  }

  const res = await Promise.all([
    getTemperatureConfig(device.id),
    prisma.statisticValues.findFirst({
      where: {
        deviceId: device.id,
      },
      take: 1,
      orderBy: {
        createdAt: 'desc',
      },
    }),
  ])

  return c.json({
    ...device,
    config: res[0],
    statistic: res[1],
  })
})
