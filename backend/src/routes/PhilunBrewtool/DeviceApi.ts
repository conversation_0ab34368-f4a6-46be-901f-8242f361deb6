import { Hono } from 'hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { Prisma, StatisticElectricity, StatisticValues } from '@prisma/client'
import { StatisticValuesModel } from '../../../prisma/zod/statisticvalues'
import { notifyOnFermentationTemperatureReached } from '../../service/Notification/FermentationTemperatureReached'
import { findActualTemperature } from '../../service/Temperature'
import { StatisticElectricityModel } from '../../../prisma/zod/statisticelectricity'

const app = new Hono()

app.get('/time', async c => {
  return c.text(String(new Date().getTime()))
})

app.get('/:deviceId/config', async c => {
  const device = await prisma.device.findFirst({
    where: {
      operatorDeviceId: c.req.param('deviceId'),
    },
  })

  if (!device) {
    return c.text('', 404)
  }

  const model = await findActualTemperature(device.id)

  if (!model) {
    return c.text('', 404)
  }

  return c.text([model.temperature, model.endDate].join(';'))
})

app.post('/:deviceId/temperature', async c => {
  const text = await c.req.text()
  const model = {
    temperature: parseFloat(text),
  } as Partial<StatisticValues>

  const device = await prisma.device.findFirst({
    where: {
      operatorDeviceId: c.req.param('deviceId'),
    },
  })

  if (!device) {
    return c.notFound()
  }

  // TODO: authentication for device

  model.createdAt = new Date()
  model.userId = device.userId
  model.deviceId = device.id

  const data = validate<Prisma.StatisticValuesCreateInput>(StatisticValuesModel, model)

  data.temperature = Number(data.temperature?.toFixed(2))

  await prisma.statisticValues.create({
    data: data,
  })

  notifyOnFermentationTemperatureReached(device, model as StatisticValues)

  return c.json(model)
})

app.post('/:deviceId/electricity', async c => {
  const text = await c.req.text()

  const device = await prisma.device.findFirst({
    where: {
      operatorDeviceId: c.req.param('deviceId'),
    },
  })

  if (!device) {
    return c.notFound()
  }

  const entries = text.split(';')

  const model = {
    type: entries[0],
    status: entries[1],
    createdAt: new Date(),
    userId: device.userId,
  } as Partial<StatisticElectricity>

  const data = validate<Prisma.StatisticElectricityCreateInput>(StatisticElectricityModel, model)

  await prisma.statisticElectricity.create({
    data: data,
  })

  return c.json(data)
})

export default app
