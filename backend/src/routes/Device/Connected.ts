import { Prisma, DeviceConnectedUse } from '@prisma/client'
import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { DeviceConnectedModel } from '../../../prisma/zod'
import { Context } from 'hono'
import { HTTPException } from 'hono/http-exception'

const checkIfUseExists = (c: Context) => {
  if (!c.req.param('use')) {
    throw new HTTPException(404, { message: 'Use is required' })
  }

  const use = c.req.param('use').toUpperCase() as DeviceConnectedUse
  if (DeviceConnectedUse[use] !== undefined) {
    return use
  }

  return false
}

app.get('/:use/device', async (c, next) => {
  const use = checkIfUseExists(c)
  if (!use) {
    next()
    return
  }

  const model = await prisma.deviceConnected.findMany({
    where: {
      use,
    },
    orderBy: {
      createdAt: 'desc',
    },
    include: {
      device: true,
    },
  })

  return c.json(model.map(m => m.device))
})

app.post('/:use/device', async c => {
  const use = checkIfUseExists(c)
  if (!use) {
    return c.notFound()
  }

  const device = validate<Prisma.DeviceConnectedCreateInput>(
    DeviceConnectedModel.omit({ id: true, createdAt: true, updatedAt: true, use: true }),
    await c.req.json()
  )

  const response = await prisma.deviceConnected.create({
    data: {
      ...device,
      use,
    },
  })

  return c.json(response)
})

app.get('/:use/device/:deviceId', async (c, next) => {
  const use = checkIfUseExists(c)
  if (!use) {
    return c.notFound()
  }

  const data = validate<Prisma.DeviceConnectedUpdateInput>(
    DeviceConnectedModel.omit({ id: true, createdAt: true, updatedAt: true, use: true }),
    await c.req.json()
  )

  const connection = await prisma.deviceConnected.findFirst({
    where: { deviceId: Number(c.req.param('deviceId')), use: use },
  })

  if (!connection) {
    return c.notFound()
  }

  const response = await prisma.deviceConnected.update({
    where: { id: connection.id },
    data: data,
  })

  return c.json(response)
})

app.get('/:use/device/:deviceId', async c => {
  const use = checkIfUseExists(c)
  if (!use) {
    return c.notFound()
  }

  const connection = await prisma.deviceConnected.findFirst({
    where: { deviceId: Number(c.req.param('deviceId')), use: use },
  })

  if (!connection) {
    return c.notFound()
  }

  const model = await prisma.deviceConnected.delete({
    where: { id: connection.id },
  })

  return c.json(model)
})
