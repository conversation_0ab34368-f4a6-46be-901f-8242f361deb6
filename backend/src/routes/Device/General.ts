import { DeviceType, Prisma } from '@prisma/client'
import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { DeviceModel } from '../../../prisma/zod'
import * as z from 'zod'
import { zValidator } from '@hono/zod-validator'

app.get('/device', async c => {
  const model = await prisma.device.findMany({
    where: {
      userId: c.get('user')?.id,
      type: c.req.query('type')?.toUpperCase() as DeviceType | undefined,
    },
    orderBy: {
      createdAt: 'desc',
    },
    include: {
      deviceConnected: true,
    },
  })

  return c.json(model)
})

app.get('/device/:deviceId', async c => {
  const model = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('deviceId')),
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

app.post('/device', async c => {
  const device = validate<Prisma.DeviceCreateInput>(DeviceModel, await c.req.json())

  if (device.type === DeviceType.BREWTOOL) {
    if (!device.operatorDeviceId) {
      return c.json({ message: 'Operator device id is required' }, 400)
    }

    const brewtool = await prisma.device.findFirst({
      where: {
        type: DeviceType.BREWTOOL,
        operatorDeviceId: device.operatorDeviceId,
      },
    })

    if (brewtool) {
      return c.json({ message: 'Brewtool already exists' }, 400)
    }
  }

  const response = await prisma.device.create({
    data: { ...device, user: { connect: { id: c.get('user')?.id ?? '' } } },
  })

  return c.json(response)
})

app.put('/device/:deviceId', async c => {
  const data = validate<Prisma.DeviceUpdateInput>(DeviceModel, await c.req.json())

  const response = await prisma.device.update({
    where: { id: Number(c.req.param('deviceId')) },
    data: data,
  })

  return c.json(response)
})

app.delete('/device/:deviceId', async c => {
  const model = await prisma.device.delete({
    where: { id: Number(c.req.param('deviceId')) },
  })

  return c.json(model)
})

app.post('/device/:deviceId/api-key', async c => {
  const model = await prisma.device.findUnique({ where: { id: Number(c.req.param('deviceId')) } })

  const userId = c.get('user')?.id
  if (!model || !userId || userId !== model.userId) {
    return c.notFound()
  }

  const key =
    Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

  const data = await prisma.apiKey.create({
    data: {
      key,
      Device: { connect: { id: model.id } },
      userId: userId,
    },
  })

  return c.json(data)
})

const DeviceConnectModel = z.object({
  deviceConnectedId: z.number().int(),
})

app.post('/device/:deviceId/connect', zValidator('json', DeviceConnectModel), async c => {
  const model = await prisma.device.findUnique({
    where: { id: Number(c.req.param('deviceId')) },
  })

  const userId = c.get('user')?.id
  if (!model || !userId || userId !== model.userId) {
    return c.notFound()
  }

  const body = c.req.valid('json')

  await prisma.device.update({
    where: { id: Number(c.req.param('deviceId')) },
    data: body,
  })

  return c.json({ message: 'Device connected' })
})
