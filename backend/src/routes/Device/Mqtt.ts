import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import shortid from 'shortid'

const route = '/device/:id/mqtt'

app.get(route, async c => {
  const device = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('id')),
    },
  })

  if (!device) {
    return c.notFound()
  }

  const model = await prisma.mqttClient.findFirst({
    where: {
      deviceId: Number(c.req.param('id')),
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

app.get(route, async c => {
  const device = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('id')),
    },
  })

  if (!device) {
    return c.notFound()
  }

  const response = await prisma.mqttClient.create({
    data: {
      deviceId: Number(c.req.param('id')),
      clientId: `client-${shortid.generate()}`,
      username: `device-${shortid.generate()}`,
      password: shortid.generate(),
    },
  })

  return c.json(response)
})

app.get(route, async c => {
  const device = await prisma.device.findUnique({
    where: {
      id: Number(c.req.param('id')),
    },
  })

  if (!device) {
    return c.notFound()
  }

  const model = await prisma.mqttClient.findFirst({
    where: {
      deviceId: Number(c.req.param('id')),
    },
  })

  if (!model) {
    return c.notFound()
  }

  await prisma.mqttClient.delete({
    where: { id: model.id },
  })

  return c.json(model)
})
