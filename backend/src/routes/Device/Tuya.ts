import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import {
  INTEGRATION_TUYA_CLOUD,
  TuyaCloudAccess,
  getStatusValues,
  getTuyaCloud,
  getTuyaContext,
  setSwitch,
} from '../../service/Integration/Tuya'
import validate from '../../helpers/validate'
import * as z from 'zod'
import { saveIntegrationData } from '../../service/Integration'
import { HTTPException } from 'hono/http-exception'
import { Hono } from 'hono'

const findTuyaDevice = async (deviceId: string) => {
  const model = await prisma.device.findUnique({
    where: {
      id: Number(deviceId),
    },
  })

  if (!model) {
    throw new HTTPException(404, { message: 'Device not found' })
  }

  if (!model.tuyaDeviceId) {
    throw new HTTPException(404, { message: 'Device not from Tuya' })
  }

  return model
}

app.get('/device/:deviceId/tuya', async c => {
  const model = await findTuyaDevice(c.req.param('deviceId'))

  const tuya = await getTuyaContext()

  const device = await tuya.device.detail({
    device_id: model.tuyaDeviceId as string,
  })

  return c.json(device)
})

app.post('/device/:deviceId/tuya/start', async c => {
  const model = await findTuyaDevice(c.req.param('deviceId'))

  const result = await setSwitch(model.tuyaDeviceId as string, true)

  return c.json(result)
})

app.post('/device/:deviceId/tuya/status', async c => {
  const device = await findTuyaDevice(c.req.param('deviceId'))

  const result = getStatusValues(device.tuyaDeviceId as string)

  return c.json(result)
})

const appTuya = new Hono().basePath('/integration/tuya')

appTuya.get('/account', async c => {
  const account = await getTuyaCloud()

  if (!account) {
    throw new HTTPException(404, { message: 'Account not exist' })
  }

  return c.json({ ...account, password: undefined })
})

appTuya.post('/account', async c => {
  const account = validate<TuyaCloudAccess>(
    z.object({
      accessKey: z.string(),
      secretKey: z.string(),
    }),
    await c.req.json()
  )

  await saveIntegrationData('tuya', INTEGRATION_TUYA_CLOUD, account)

  return c.json(account)
})

appTuya.get('/device', async c => {
  const tuya = await getTuyaContext()

  // const device = await tuya.device.list({ device_ids: [] })

  // https://openapi.tuyaeu.com/v1.0/iot-03/device

  const response = await tuya.request({
    path: '/v2.0/cloud/thing/space/device',
    method: 'GET',
  })

  // https://eu.iot.tuya.com/micro-app/cloud/v5_1/project/device/list

  console.log(response)

  return c.json(response)
})

appTuya.get('/device/:tuyaDeviceId', async c => {
  const tuya = await getTuyaContext()

  const device = await tuya.device.detail({
    device_id: c.req.param('tuyaDeviceId') as string,
  })

  if (!device.success) {
    throw new HTTPException(404, { message: 'Device not found' })
  }

  return c.json(device)
})

app.route('/', appTuya)
