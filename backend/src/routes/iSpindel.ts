import prisma from '../helpers/prisma'
import authApp, { app } from '../helpers/hono'
import { iSpindelData } from '../types/spindel'
import { notifyOnFermentationGravityReached } from '../service/Notification/FermentationGravityReached'

authApp.get('/device/:deviceId/spindel', async c => {
  const logEntry = await prisma.spindelLog.findFirst({
    where: {
      deviceId: Number(c.req.param('deviceId')),
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (!logEntry) {
    return c.notFound()
  }

  return c.json(logEntry)
})

authApp.get('/device/:deviceId/spindel/statistic', async c => {
  const entries = await prisma.spindelLog.findMany({
    where: {
      deviceId: Number(c.req.param('deviceId')),
    },
    select: {
      id: true,
      deviceId: true,
      spindelName: true,
      temperature: true,
      angle: true,
      battery: true,
      gravity: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 1000,
  })

  return c.json(entries)
})

app.post('/device/:deviceId/spindel', async c => {
  const request = await c.req.json<iSpindelData>()

  // const tilt = request.angle
  // const gravity = 0.004415613 * tilt * tilt + 0.120848707 * tilt - 6.159197377

  const logEntry = await prisma.spindelLog.create({
    data: {
      deviceId: Number(c.req.param('deviceId')),
      spindelName: request.name,
      temperature: request.temperature,
      angle: request.angle,
      battery: request.battery,
      gravity: request.gravity,
      request: request,
    },
  })

  notifyOnFermentationGravityReached(logEntry.deviceId, logEntry)

  return c.json(logEntry)
})
