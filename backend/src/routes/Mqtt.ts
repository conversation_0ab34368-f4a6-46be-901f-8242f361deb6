import prisma from '../helpers/prisma'
import app from '../helpers/hono'

type MqttAuthUserReq = { clientid?: string; username?: string; password?: string }

type MqttAuthAclReq = {
  clientid?: string
  username?: string
  access?: string
  ipaddr?: string
  topic?: string
  mountpoint?: string
}

app.post('/mqtt/auth', async c => {
  try {
    const data = (await c.req.json()) as MqttAuthUserReq

    if (!data.username || !data.password) {
      throw new Error('No username or password provided')
    }

    if (data.username.startsWith('device-')) {
      const device = await prisma.mqttClient.findUnique({
        where: {
          username: data.username,
        },
      })

      if (!device) {
        throw new Error('No device found')
      }

      if (device.password !== data.password) {
        throw new Error('Password is incorrect')
      }
    }

    if (
      data.username === process.env.MQTT_USERNAME &&
      process.env.MQTT_PASSWORD !== data.password
    ) {
      throw new Error('Server Credentials are incorrect')
    }

    // TOOD: login user

    return c.json({
      result: 'allow',
      is_superuser: false,
    })
  } catch (e) {
    console.error('POST /mqtt/auth: error:', e)
    c.status(403)
    return c.json({
      result: 'deny',
    })
  }
})

app.post('/mqtt/superuser', async c => {
  const data = (await c.req.json()) as MqttAuthUserReq

  // TODO: login superuser

  c.status(403)
  return c.json({ status: 'ok' })
})

app.post('/mqtt/acl', async c => {
  const data = (await c.req.json()) as MqttAuthAclReq

  // TODO: create acl method

  return c.json({ status: 'ok' })
})
