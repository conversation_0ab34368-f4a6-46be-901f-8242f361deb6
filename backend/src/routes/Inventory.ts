import { Prisma } from '@prisma/client'
import app from '../helpers/hono'
import prisma from '../helpers/prisma'
import validate from '../helpers/validate'
import { InventaryItemModel } from '../../prisma/zod/inventaryitem'

app.get('/inventory', async c => {
  const model = await prisma.inventaryItem.findMany()

  return c.json(model)
})

app.get('/inventory/search', async c => {
  const model = await prisma.inventaryItem.findMany({
    where: {
      name: {
        contains: c.req.query('q') as string,
      },
      type: c.req.query('type') as string,
    },
  })

  return c.json(model)
})

app.get('/inventory/:inventoryId', async c => {
  const model = await prisma.inventaryItem.findUnique({
    where: {
      id: Number(c.req.param('inventoryId')),
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

app.post('/inventory', async c => {
  const data = validate<Prisma.InventaryItemCreateInput>(InventaryItemModel, await c.req.json())

  const response = await prisma.inventaryItem.create({
    data: data,
  })

  return c.json(response)
})

app.put('/inventory/:inventoryId', async c => {
  const data = validate<Prisma.InventaryItemUpdateInput>(InventaryItemModel, await c.req.json())

  const response = await prisma.inventaryItem.update({
    where: { id: Number(c.req.param('inventoryId')) },
    data: data,
  })

  return c.json(response)
})

app.delete('/inventory/:inventoryId', async c => {
  const model = await prisma.inventaryItem.delete({
    where: { id: Number(c.req.param('inventoryId')) },
  })

  return c.json(model)
})
