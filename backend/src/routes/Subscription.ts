import { Prisma } from '@prisma/client'
import app from '../helpers/hono'
import validate from '../helpers/validate'
import { PushSubscriptionModel } from '../../prisma/zod/pushsubscription'
import prisma from '../helpers/prisma'

app.get('/subscription', async c => {
  const model = await prisma.pushSubscription.findMany()

  return c.json(model)
})

app.post('/subscription', async c => {
  const model = await c.req.json()

  const data = validate<Prisma.PushSubscriptionCreateInput>(PushSubscriptionModel, model)

  await prisma.pushSubscription.create({
    data: data,
  })

  return c.json(data)
})

app.delete('/subscription/:id', async c => {
  const model = await prisma.pushSubscription.delete({
    where: {
      id: Number(c.req.param('id')),
    },
  })

  return c.json(model)
})
