import app from '../helpers/hono'
import prisma from '../helpers/prisma'
import validate from '../helpers/validate'
import * as z from 'zod'
import nodeCache from '../helpers/nodeCache'
import {
  BraurekaLoginResponse,
  LABEL_BRAUREKA_COOKIE,
  getBraurekaAccount,
  getBraurekaRecipe,
  loginBraureka,
  parseCookies,
} from '../service/Integration/Braureka'
import { saveIntegrationData } from '../service/Integration'

const prefix = '/integration/braureka'

app.get(prefix + '/account', async c => {
  const account = await getBraurekaAccount()

  if (!account) {
    return c.notFound()
  }

  return c.json({ ...account, password: undefined })
})

app.post(prefix + '/account', async c => {
  const account = validate<{
    username: string
    password: string
  }>(
    z.object({
      username: z.string(),
      password: z.string(),
    }),
    await c.req.json()
  )

  const response = await login<PERSON><PERSON>ureka(account)

  if (response.status > 201) {
    c.status(400)
    return c.json({ error: 'Login failed' })
  }

  const data = (await response.json()) as <PERSON>raurekaLoginResponse

  if (!data.success) {
    c.status(400)
    return c.json({ error: data.data })
  }

  const cookie = parseCookies(response)
  nodeCache.set(LABEL_BRAUREKA_COOKIE, cookie)

  await saveIntegrationData('braureka', 'account', { ...account, wpUsername: data.data.username })

  return c.json({ ...account, wpUsername: data.data.username, password: undefined })
})

app.get(prefix + '/recipe/:id', async c => {
  // @ts-ignore
  const recipe = await getBraurekaRecipe(c.req.param('id') as string)

  return c.json(recipe)
})
