import authApp from '../helpers/hono'
import prisma from '../helpers/prisma'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'

// Schema for notification updates
const NotificationUpdateSchema = z.object({
  read: z.boolean(),
})

// Get all notifications for a user
authApp.get('/user/:userId/notification', async c => {
  const userId = c.req.param('userId')

  // Check if the requesting user has permission to access these notifications
  const currentUserId = c.get('user')?.id
  if (!currentUserId || currentUserId !== userId) {
    return c.json({ error: 'Unauthorized' }, 403)
  }

  const notifications = await prisma.notification.findMany({
    where: {
      userId: userId,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  return c.json(notifications)
})

// Update a notification (mark as read)
authApp.put(
  '/user/:userId/notification/:notificationId',
  zValidator('json', NotificationUpdateSchema),
  async c => {
    const userId = c.req.param('userId')
    const notificationId = Number(c.req.param('notificationId'))

    // Check if the requesting user has permission to update this notification
    const currentUserId = c.get('user')?.id
    if (!currentUserId || currentUserId !== userId) {
      return c.json({ error: 'Unauthorized' }, 403)
    }

    // Get the notification to update
    const notification = await prisma.notification.findFirst({
      where: {
        id: notificationId,
        userId: userId,
      },
    })

    if (!notification) {
      return c.notFound()
    }

    const updateData = c.req.valid('json')

    // Update the notification
    const updatedNotification = await prisma.notification.update({
      where: {
        id: notificationId,
      },
      data: updateData,
    })

    return c.json(updatedNotification)
  }
)
