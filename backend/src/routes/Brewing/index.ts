import { Prisma } from '@prisma/client'
import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import { BrewingFermentableModel, BrewingModel } from '../../../prisma/zod'
import './PostBrewing'
import { deleteBrewing } from '../../service/Brewing'
import { sendPushNotification } from '../../helpers/web-push'
import { sendNotification } from '../../helpers/websocket'
import groupBy from '../../helpers/groupBy'

app.get('/brewing', async c => {
  const model = await prisma.brewing.findMany({
    orderBy: {
      createdAt: 'desc',
    },
    include: {
      mashSteps: {
        where: {
          status: { in: ['ACTIVE', 'HEATING'] },
        },
      },
      recipe: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  })

  return c.json(model)
})

app.get('/brewing/:brewId', async c => {
  const model = await prisma.brewing.findUnique({
    where: {
      id: Number(c.req.param('brewId')),
    },
    include: {
      mashSteps: {
        orderBy: {
          order: 'asc',
        },
      },
      hops: true,
      yeasts: true,
      fermentables: true,
      recipe: true,
    },
  })

  if (!model) {
    return c.notFound()
  }

  return c.json(model)
})

app.get('/brewing/:brewingId/test-hop', async c => {
  const brewingId = Number(c.req.param('brewingId'))

  const model = await prisma.brewing.findUnique({
    where: {
      id: brewingId,
    },
    include: {
      hops: true,
    },
  })

  if (!model) {
    return c.notFound()
  }

  const timedHops = groupBy(model.hops, h => h.time)
  for (const time in timedHops) {
    const hops = timedHops[time]
    sendPushNotification({
      messageType: 'brewingHops',
      hops: hops,
    })

    sendNotification(c.get('user')?.id ?? '', {
      hops: hops,
      message: 'HOP_ADDED',
    })
  }

  return c.json(model)
})

app.put('/brewing/:brewId', async c => {
  const data = validate<Prisma.BrewingCreateInput>(
    BrewingModel.omit({ status: true }),
    await c.req.json()
  )

  const response = await prisma.brewing.update({
    where: { id: Number(c.req.param('brewId')) },
    data: data,
  })

  return c.json(response)
})

app.delete('/brewing/:brewingId', async c => {
  const model = await deleteBrewing(Number(c.req.param('brewingId')))

  return c.json(model)
})

app.put('/brewing/:brewingId/fermentable/:id', async c => {
  const data = validate<Prisma.BrewingFermentableUpdateInput>(
    BrewingFermentableModel.omit({ id: true, brewingId: true, createdAt: true }),
    await c.req.json()
  )

  const response = await prisma.brewingFermentable.update({
    where: { id: Number(c.req.param('id')) },
    data: data,
  })

  return c.json(response)
})
