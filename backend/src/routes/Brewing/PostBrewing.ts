import * as z from 'zod'
import app from '../../helpers/hono'
import prisma from '../../helpers/prisma'
import validate from '../../helpers/validate'
import {
  BrewingFermentableModel,
  BrewingHopModel,
  BrewingMashStepModel,
  BrewingModel,
  BrewingYeastModel,
} from '../../../prisma/zod'
import { HTTPException } from 'hono/http-exception'
import startMashing from '../../service/Brewing/startMashing'
import { startBoiling } from '../../service/Brewing/startBoiling'

const StepModel = BrewingMashStepModel.omit({ brewingId: true, id: true })
const HopModel = BrewingHopModel.omit({ brewingId: true, id: true, sendPushNotification: true })
const YeastModel = BrewingYeastModel.omit({ brewingId: true, id: true })
const FermentableModel = BrewingFermentableModel.omit({ brewingId: true, id: true })
const CompleteBrewingModel = BrewingModel.extend({
  mashSteps: z.object({
    create: z.array(StepModel),
  }),
  hops: z.object({
    create: z.array(HopModel),
  }),
  yeasts: z.object({
    create: z.array(YeastModel),
  }),
  fermentables: z.object({
    create: z.array(FermentableModel),
  }),
})

type CompleteBrewing = z.infer<typeof CompleteBrewingModel>

app.post('/brewing', async c => {
  const brewing = validate<CompleteBrewing>(CompleteBrewingModel, await c.req.json())

  if (brewing.startTime) {
    brewing.status = 'PREMASHING'
  } else {
    brewing.status = 'WAITING'
  }

  const response = await prisma.brewing.create({
    data: brewing,
    include: {
      mashSteps: true,
      hops: true,
      yeasts: true,
      fermentables: true,
    },
  })

  return c.json(response)
})

app.post('/brewing/:brewingId/mashing', async c => {
  const brewing = await prisma.brewing.findUnique({
    where: {
      id: Number(c.req.param('brewingId')),
    },
  })

  if (!brewing) {
    throw new HTTPException(404, { message: 'Brewing not found' })
  }

  const activeBrewing = await prisma.brewing.findFirst({
    where: {
      status: { in: ['MASHING'] },
    },
  })

  if (activeBrewing) {
    throw new HTTPException(404, { message: 'There is already an active brewing' })
  }

  await startMashing(brewing.id)

  return c.json({ success: true })
})

app.post('/brewing/:brewingId/boiling', async c => {
  const brewing = await prisma.brewing.findUnique({
    where: {
      id: Number(c.req.param('brewingId')),
    },
  })

  if (!brewing) {
    throw new HTTPException(404, { message: 'Brewing not found' })
  }

  const activeBrewing = await prisma.brewing.findFirst({
    where: {
      status: { in: ['MASHING'] },
    },
  })

  if (activeBrewing) {
    throw new HTTPException(404, { message: 'There is already an active brewing' })
  }

  await startBoiling(brewing.id, c.get('user')?.id ?? '')

  return c.json({ success: true })
})
