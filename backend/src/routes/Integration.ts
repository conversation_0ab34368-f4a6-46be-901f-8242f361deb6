import { has<PERSON><PERSON><PERSON><PERSON> } from '../service/Integration/Braureka'
import app from '../helpers/hono'
import { hasTuya } from '../service/Integration/Tuya'
import {
  SMART_LIFE_STORAGE_ACCOUNT_LABEL,
  SMART_LIFE_STORAGE_LABEL,
  SmartLifeAccount,
  TUYA_CLIENT_ID,
  TUYA_SCHEMA,
  getListOfHomes,
  getSmartLife,
  hasSmartLife,
} from '../service/Integration/SmartLife'
import * as z from 'zod'
import validate from '../helpers/validate'
import { saveIntegrationData } from '../service/Integration'
import { HTTPException } from 'hono/http-exception'

app.get('/integration', async c => {
  // TODO replace with lucia

  return c.json({
    tuya: await hasTuya(),
    braureka: await hasBraureka(),
    smartlife: await hasSmartLife(),
    mqtt: {
      clientId: 'user-1',
      username: 'Test',
      password: 'test',
    },
  })
})

app.get('/integration/smartlife/account', async c => {
  const account = await getSmartLife()

  if (!account) {
    throw new HTTPException(404, { message: 'Account not exist' })
  }

  return c.json({ ...account, password: undefined })
})

app.post('/integration/smartlife/account', async _c => {
  throw new HTTPException(404, { message: 'Not implemented, use register route' })
})

app.post('/integration/smartlife/register', async c => {
  const account = validate<{
    userId: string
    qrcode: string
  }>(
    z.object({
      userId: z.string(),
      qrcode: z.string(),
    }),
    await c.req.json()
  )

  if (!account) {
    throw new HTTPException(404, { message: 'Account not exist' })
  }

  const response = await fetch(
    `https://apigw.iotbing.com/v1.0/m/life/home-assistant/qrcode/tokens/${account.qrcode}?clientid=${TUYA_CLIENT_ID}&usercode=${account.userId}`,
    {
      method: 'GET',
      headers: {
        Accept: 'application/json',
      },
    }
  )

  if (!response.ok) {
    throw new HTTPException(500, { message: 'Request for check registration failed' })
  }

  const result = (await response.json()) as {
    success: boolean
    tid: string
    t: number
    result: {
      access_token: string
      refresh_token: string
      expire_time: number
      terminal_id: string
      uid: string
      username: string
      endpoint: string
    }
  }

  if (!result.success) {
    throw new HTTPException(500, { message: 'Failed to register' })
  }

  const data = {
    userId: account.userId,
    timestamp: result.t,
    needRegister: false,
    ...result.result,
  } as SmartLifeAccount

  await saveIntegrationData(SMART_LIFE_STORAGE_LABEL, SMART_LIFE_STORAGE_ACCOUNT_LABEL, data)

  return c.json(data)
})

app.post('/integration/smartlife/qrcode', async c => {
  const account = validate<SmartLifeAccount>(
    z.object({
      userId: z.string(),
    }),
    await c.req.json()
  )

  if (!account) {
    throw new HTTPException(404, { message: 'Account not exist' })
  }

  const response = await fetch(
    `https://apigw.iotbing.com/v1.0/m/life/home-assistant/qrcode/tokens?clientid=${TUYA_CLIENT_ID}&usercode=${account.userId}&schema=${TUYA_SCHEMA}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )

  if (!response.ok) {
    throw new HTTPException(500, { message: 'Failed to get QR code' })
  }

  const result = (await response.json()) as {
    success: boolean
    tid: string
    t: string
    result: {
      qrcode: string
    }
  }

  return c.json(result)
})

app.get('/integration/smartlife/home', async c => {
  const homes = await getListOfHomes()

  return c.json(homes)
})
