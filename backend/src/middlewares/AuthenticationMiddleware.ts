import { getLucia } from '../integration/lucia'
import app from '../helpers/hono'
import { Session, User } from '@prisma/client'
import prisma from '../helpers/prisma'

const ignorePaths = ['/api/login', '/api/status']

app.use('*', async (c, next) => {
  if (ignorePaths.includes(c.req.path)) {
    return await next()
  }

  const header = c.req.header('Authentication')
  if (!header) {
    return c.json({ error: 'No authentication header found' }, 401)
  }

  if (header === `Bearer ${process.env.DEV_API_KEY}`) {
    const user = await prisma.user.findUnique({
      where: {
        id: process.env.DEV_USER_ID,
      },
    })

    if (!user) {
      return c.json({ error: 'No user for DEV_USER_ID found' }, 401)
    }

    c.set('user', user)
    c.set('session', {
      id: crypto.randomUUID(),
      userId: user.id,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30),
    } as Session)

    await next()
    return
  }

  const lucia = await getLucia()

  const sessionId = lucia.readBearerToken(header ?? '')
  if (!sessionId) {
    return c.json({ error: 'Session not valid' }, 401)
  }

  const { session, user } = await lucia.validateSession(sessionId)

  if (!session || !user) {
    return c.json({ error: 'Session not valid' }, 401)
  }

  c.set('user', user as User)
  c.set('session', session as Session)

  await next()
})
