import './integration/sentry'
import './middlewares/AuthenticationMiddleware'
import './routes/PhilunBrewtool'
import './routes/Subscription'
import './routes/Inventory'
import './routes/Search'
import './routes/Recipe'
import './routes/RecipeYeast'
import './routes/Mqtt'
import './routes/Device'
import './routes/Braureka'
import './routes/Integration'
import './routes/Brewing'
import './routes/Status'
import './routes/Authentication'
import './routes/iSpindel'
import './routes/Notification'
import authApp, { app } from './helpers/hono'
import { serve } from '@hono/node-server'
import onAppBrewingInit from './service/Brewing/onAppInit'
import { startWebsocket } from './helpers/websocket'
import { serveStatic } from '@hono/node-server/serve-static'
import { Hono } from 'hono'

const port = 3000

onAppBrewingInit()

console.log(`Server is running on port ${port}`)

const base = new Hono()

base.use(
  '*',
  serveStatic({
    root: './public',
  })
)

base.route('/', app)
base.route('/api', authApp)

base.get('*', serveStatic({ path: './public/index.html' }))

const server = serve({
  fetch: base.fetch,
  port,
})

startWebsocket(server)
