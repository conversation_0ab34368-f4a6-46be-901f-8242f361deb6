import mockPrisma from '../../../tests/mockPrisma'
import { Brewing, BrewingHop } from '@prisma/client'
import schedule from 'node-schedule'
import { DateTime } from 'luxon'
import { sendData } from '../../helpers/websocket'
import onAppStartBoilingJobs from './onAppStartBoilingJobs'
import { mockScheduler, getDateTimeFromJob } from '../../../tests/SchedulerTests'

const { prismaMock } = mockPrisma()

jest.mock('../../helpers/web-push')

jest.mock('../../helpers/websocket')

jest.mock('./../Device')

mockScheduler()

afterEach(() => {
  jest.clearAllMocks()
})

describe('onAppStartBoilingJobs', () => {
  it('should start boiling jobs', async () => {
    const boilStartTime = DateTime.now().minus({ minutes: 10 })

    prismaMock.brewing.findMany.mockResolvedValue([
      {
        id: 1,
        status: 'BOILING',
        boilStartTime: boilStartTime.toJSDate(),
        boilTime: 80,
        hops: [
          {
            id: 1,
            name: 'Hop 1',
            amount: 15,
            alpha: 1,
            form: 'PELLET',
            time: 5,
            type: 'BITTERING',
            use: 'BOIL',
          },
          {
            id: 2,
            name: 'Hop 2',
            amount: 10,
            alpha: 1,
            form: 'PELLET',
            time: 5,
            type: 'BITTERING',
            use: 'BoIL',
          },
          {
            id: 3,
            name: 'Hop 3',
            amount: 12,
            alpha: 1,
            form: 'PELLET',
            time: 60,
            type: 'BITTERING',
            use: 'Boil',
          },
          {
            id: 4,
            name: 'Hop 4',
            amount: 12,
            alpha: 1,
            form: 'PELLET',
            time: 0,
            type: 'BITTERING',
            use: 'Dry Hop',
          },
        ],
      } as unknown as Brewing & { hops: BrewingHop[] },
    ])

    await onAppStartBoilingJobs()

    const jobs = schedule.scheduledJobs

    expect(jobs['brewing-1-hop-60']).toBeDefined()
    expect(jobs['brewing-1-boiling-finish']).toBeDefined()

    const minutes = getDateTimeFromJob('brewing-1-hop-60').diffNow('minutes').toObject().minutes

    expect(minutes).toBeCloseTo(50, 1)

    const finishMinutes = getDateTimeFromJob('brewing-1-boiling-finish')
      .diffNow('minutes')
      .toObject().minutes

    expect(finishMinutes).toBeCloseTo(70, 1)
  })

  it('should finish boiling jobs, when time is over', async () => {
    const boilStartTime = DateTime.now().minus({ minutes: 100 })

    prismaMock.brewing.findMany.mockResolvedValue([
      {
        id: 1,
        status: 'BOILING',
        boilStartTime: boilStartTime.toJSDate(),
        boilTime: 80,
        hops: [
          {
            id: 1,
            name: 'Hop 1',
            amount: 15,
            alpha: 1,
            form: 'PELLET',
            time: 5,
            type: 'BITTERING',
            use: 'BOIL',
          },
        ],
      } as unknown as Brewing & { hops: BrewingHop[] },
    ])

    await onAppStartBoilingJobs()

    const jobs = schedule.scheduledJobs

    expect(jobs['brewing-1-hop-5']).not.toBeDefined()
    expect(jobs['brewing-1-boiling-finish']).not.toBeDefined()

    expect(prismaMock.brewing.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: { status: 'FERMENTATION' },
    })

    expect(sendData).toHaveBeenCalled()
    expect(sendData).toHaveBeenCalledWith('user/1/brewing/1', {
      status: 'FERMENTATION',
      message: 'STATUS_CHANGED',
    })
  })
})
