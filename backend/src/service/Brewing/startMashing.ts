import { HTTPException } from 'hono/http-exception'
import runMashStep from './runMashStep'
import startMashingJob from './startMashingJob'
import prisma from '../../helpers/prisma'

export default async function startMashing(brewingId: number) {
  await prisma.brewing.update({
    where: {
      id: brewingId,
    },
    data: {
      status: 'MASHING',
    },
  })

  const brewing = await prisma.brewing.findUnique({
    where: {
      id: brewingId,
    },
    include: {
      mashSteps: true,
    },
  })

  if (!brewing) {
    throw new HTTPException(404, { message: 'Brewing not found' })
  }

  if (brewing.mashSteps.length > 0) {
    await runMashStep(brewing.mashSteps[0], new Date())

    startMashingJob(brewing.id)
  }

  return brewing
}
