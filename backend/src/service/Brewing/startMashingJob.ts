import { cancelJob, scheduleJob } from 'node-schedule'
import { getBrewing, getBrewingDevices, setBrewingStatus, setHeating } from '../Brewing'
import runMashStep from './runMashStep'

export const BREWING_MASHING_JOB = (brewingId: number) => `brewing-${brewingId}-mashing`

const MASHING_JOB_CRON = '*/30 * * * * *'

export default function startMashingJob(brewingId: number) {
  scheduleJob(BREWING_MASHING_JOB(brewingId), MASHING_JOB_CRON, createMashJobFn(brewingId))
}

const createMashJobFn = (brewingId: number) => {
  return async (fireDate: Date) => {
    console.debug('Running job for mashing', brewingId)
    try {
      const brewing = await getBrewing(brewingId)

      const devices = await getBrewingDevices()

      if (brewing.status !== 'MASHING') {
        await setHeating(devices, false)
        cancelJob(BREWING_MASHING_JOB(brewingId))
        return
      }

      const activeStep = brewing.mashSteps.find(
        step => step.status === 'ACTIVE' || step.status === 'HEATING'
      )

      if (!activeStep) {
        console.error('No active step found in brewing', brewingId)
        await setHeating(devices, false)
        await setBrewingStatus(brewingId, 'LAUTERING')
        return
      }

      await runMashStep(activeStep, fireDate)
    } catch (error) {
      console.error(`Error in brewing job ${brewingId}:`, error)
      // after 5 errors, stop the job
      // set status to canceled
    }
  }
}
