import { Brewing, BrewingMashStep } from '@prisma/client'
import mockPrisma from '../../../tests/mockPrisma'
import { DateTime } from 'luxon'
import { sendData } from '../../helpers/websocket'
import { getTemperature, setDeviceSwitch } from './../Device'
import runMashStep from './runMashStep'

const { prismaMock } = mockPrisma()

jest.mock('../../helpers/websocket')

jest.mock('./../Device')

afterEach(() => {
  jest.clearAllMocks()
})

const testStep = {
  id: 1,
  name: 'Ein<PERSON>ischen',
  infuseAmount: 20,
  temperature: 20,
  time: 20,
  type: 'MASH',
  startTime: null,
  status: 'WAITING',
  order: 1,
  brewingId: 1,
  heatingStart: null,
} as BrewingMashStep

describe('runMashStep', () => {
  const heatingDevice = { id: 2, name: 'Device 2', use: 'BREWING' }

  beforeEach(() => {
    console.log = jest.fn()

    prismaMock.deviceConnected.findMany.mockResolvedValue([
      {
        id: 1,
        deviceId: 1,
        type: 'TEMPERATURE',
        // @ts-ignore
        device: { id: 1, name: 'Device 1', use: 'BREWING' },
      },
      {
        id: 2,
        deviceId: 2,
        type: 'HEATING',
        // @ts-ignore
        device: heatingDevice,
      },
    ])
  })

  it.each([['WAITING'], ['HEATING'], [null]])(
    'should start mash step when temperature is reached & status = %s',
    async status => {
      // @ts-ignore
      getTemperature.mockResolvedValue(20)

      await runMashStep(
        {
          ...testStep,
          status,
        } as BrewingMashStep,
        new Date()
      )

      expect(sendData).toHaveBeenCalled()
      expect(sendData).toHaveBeenCalledWith('user/1/brewing/1', {
        stepId: 1,
        message: 'STEP_STARTED',
      })

      expect(prismaMock.brewingMashStep.update).toHaveBeenCalledWith({
        where: {
          id: 1,
        },
        data: {
          status: 'ACTIVE',
          startTime: expect.any(Date),
          heatingStart: expect.any(Date),
        },
      })

      expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, false)
    }
  )

  it('should only set device heating off when status is active & temperature reached', async () => {
    // @ts-ignore
    getTemperature.mockResolvedValue(20)

    await runMashStep(
      {
        ...testStep,
        startTime: DateTime.now().minus({ minutes: 10 }).toJSDate(),
        status: 'ACTIVE',
        heatingStart: new Date(),
      } as BrewingMashStep,
      new Date()
    )

    expect(sendData).not.toHaveBeenCalled()
    expect(prismaMock.brewingMashStep.update).not.toHaveBeenCalled()
    expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, false)
  })

  it('should set device heating off when temperature is higher', async () => {
    // @ts-ignore
    getTemperature.mockResolvedValue(50)

    await runMashStep(
      {
        ...testStep,
        startTime: DateTime.now().minus({ minutes: 10 }).toJSDate(),
        status: 'ACTIVE',
        heatingStart: new Date(),
      } as BrewingMashStep,
      new Date()
    )

    expect(sendData).not.toHaveBeenCalled()
    expect(prismaMock.brewingMashStep.update).not.toHaveBeenCalled()
    expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, false)
  })

  it('should set device heating on when temperature is lower', async () => {
    // @ts-ignore
    getTemperature.mockResolvedValue(10)

    await runMashStep(
      {
        ...testStep,
        startTime: DateTime.now().minus({ minutes: 10 }).toJSDate(),
        status: 'ACTIVE',
        heatingStart: new Date(),
      } as BrewingMashStep,
      new Date()
    )

    expect(sendData).not.toHaveBeenCalled()
    expect(prismaMock.brewingMashStep.update).not.toHaveBeenCalled()
    expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, true)
  })

  it.each([['WAITING'], [null]])(
    'should set status on heating when temperature is lower & status = %s',
    async status => {
      // @ts-ignore
      getTemperature.mockResolvedValue(10)

      await runMashStep(
        {
          ...testStep,
          startTime: DateTime.now().minus({ minutes: 10 }).toJSDate(),
          status,
          heatingStart: new Date(),
        } as BrewingMashStep,
        new Date()
      )

      expect(sendData).toHaveBeenCalled()
      expect(sendData).toHaveBeenCalledWith('user/1/brewing/1', {
        stepId: 1,
        message: 'HEATING_STARTED',
      })

      expect(prismaMock.brewingMashStep.update).toHaveBeenCalledWith({
        where: {
          id: 1,
        },
        data: {
          status: 'HEATING',
          heatingStart: expect.any(Date),
        },
      })
      expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, true)
    }
  )

  it('should finish this step and move to next step, when time is reached', async () => {
    // @ts-ignore
    getTemperature.mockResolvedValue(20)

    const time = 20

    const step = {
      ...testStep,
      time,
      startTime: DateTime.now().minus({ minutes: time }).toJSDate(),
      status: 'ACTIVE',
      heatingStart: new Date(),
    } as BrewingMashStep

    // const startTime = DateTime.now().minus({ minutes: 20 }).toJSDate()

    prismaMock.brewing.findUnique.mockResolvedValue({
      id: 1,
      status: 'MASHING',
      mashSteps: [
        step,
        {
          ...testStep,
          id: 2,
          temperature: 25,
          order: 2,
        },
      ],
    } as unknown as Brewing & { mashSteps: BrewingMashStep[] })

    await runMashStep(step, new Date())

    expect(prismaMock.brewingMashStep.update).toHaveBeenCalledTimes(2)
    expect(prismaMock.brewingMashStep.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: { status: 'DONE', endTime: expect.any(Date) },
    })

    expect(prismaMock.brewingMashStep.update).toHaveBeenCalledWith({
      where: { id: 2 },
      data: { status: 'HEATING', heatingStart: expect.any(Date) },
    })

    expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, true)

    expect(sendData).toHaveBeenCalled()
    expect(sendData).toHaveBeenCalledWith('user/1/brewing/1', {
      stepId: 2,
      message: 'HEATING_STARTED',
    })

    expect(prismaMock.brewing.update).not.toHaveBeenCalled()
  })

  it('should finish this step when time is reached and finish mashing and move to lautering', async () => {
    const time = 20

    const step = {
      ...testStep,
      time,
      startTime: DateTime.now().minus({ minutes: time }).toJSDate(),
      status: 'ACTIVE',
      heatingStart: new Date(),
    } as BrewingMashStep

    prismaMock.brewing.findUnique.mockResolvedValue({
      id: 1,
      status: 'MASHING',
      mashSteps: [step],
    } as unknown as Brewing & { mashSteps: BrewingMashStep[] })

    await runMashStep(step, new Date())

    expect(prismaMock.brewingMashStep.update).toHaveBeenCalled()
    expect(prismaMock.brewingMashStep.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: { status: 'DONE', endTime: expect.any(Date) },
    })

    expect(prismaMock.brewing.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: { status: 'LAUTERING' },
    })

    expect(setDeviceSwitch).toHaveBeenCalledWith(heatingDevice, false)
  })
})
