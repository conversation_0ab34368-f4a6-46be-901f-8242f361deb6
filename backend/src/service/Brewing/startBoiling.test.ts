import { Brewing, BrewingHop } from '@prisma/client'
import mockPrisma from '../../../tests/mockPrisma'
import schedule from 'node-schedule'
import { sendData } from '../../helpers/websocket'
import { startBoiling } from './startBoiling'

const { prismaMock } = mockPrisma()

jest.mock('../helpers/web-push')

jest.mock('../helpers/websocket')

afterEach(() => {
  schedule.gracefulShutdown()
  jest.clearAllMocks()
})

describe('startBoiling', () => {
  it('should start boiling', async () => {
    prismaMock.brewing.findUnique.mockResolvedValue({
      id: 1,
      status: 'BOILING',
      hops: [
        {
          id: 1,
          name: 'Hop 1',
          amount: 15,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BOIL',
        },
        {
          id: 2,
          name: 'Hop 2',
          amount: 10,
          alpha: 1,
          form: 'PELLET',
          time: 5,
          type: 'BITTERING',
          use: 'BoIL',
        },
        {
          id: 3,
          name: 'Hop 3',
          amount: 12,
          alpha: 1,
          form: 'PELLET',
          time: 60,
          type: 'BITTERING',
          use: 'Boil',
        },
        {
          id: 4,
          name: 'Hop 4',
          amount: 12,
          alpha: 1,
          form: 'PELLET',
          time: 0,
          type: 'BITTERING',
          use: 'Dry Hop',
        },
      ],
    } as unknown as Brewing & { hops: BrewingHop[] })

    await startBoiling(1)

    expect(prismaMock.brewing.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: { status: 'BOILING', boilStartTime: expect.any(Date) },
    })

    const jobs = schedule.scheduledJobs

    expect(jobs['brewing-1-hop-5']).toBeDefined()
    expect(jobs['brewing-1-hop-60']).toBeDefined()
    expect(jobs['brewing-1-boiling-finish']).toBeDefined()

    expect(sendData).toHaveBeenCalled()
    expect(sendData).toHaveBeenCalledWith('user/1/brewing/1', {
      status: 'BOILING',
      message: 'STATUS_CHANGED',
    })
  })
})

// describe('runTimedBoilHop', () => {
//     it('should run timed boil hop', async () => {
//       const hops = [
//         {
//           id: 1,
//           name: 'Hop 1',
//           amount: 15,
//           alpha: 1,
//           form: 'PELLET',
//           time: 5,
//           type: 'BITTERING',
//           use: 'BOIL',
//         },
//         {
//           id: 2,
//           name: 'Hop 2',
//           amount: 10,
//           alpha: 1,
//           form: 'PELLET',
//           time: 5,
//           type: 'BITTERING',
//           use: 'BoIL',
//         },
//       ] as BrewingHop[]

//       runTimedBoilHop(hops)(new Date())

//       expect(sendPushNotification).toHaveBeenCalled()
//       expect(sendPushNotification).toHaveBeenCalledWith({
//         messageType: 'brewingHops',
//         hops,
//       })
//     })
//   })

//   describe('runBoilingFinish', () => {
//     it('should finish boiling', async () => {
//       prismaMock.brewing.findUnique.mockResolvedValue({
//         id: 1,
//         status: 'BOILING',
//         boilTime: 80,
//         boilStartTime: DateTime.now().minus({ minutes: 80 }).toJSDate(),
//       } as unknown as Brewing)

//       await runBoilingFinish(1)(new Date())

//       expect(prismaMock.brewing.update).toHaveBeenCalledWith({
//         where: { id: 1 },
//         data: { status: 'FERMENTATION' },
//       })

//       expect(publish).toHaveBeenCalled()
//       expect(publish).toHaveBeenCalledWith('user/1/brewing/1', {
//         status: 'FERMENTATION',
//         message: 'STATUS_CHANGED',
//       })
//     })
//   })
