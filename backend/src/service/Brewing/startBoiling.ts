import prisma from '../../helpers/prisma'
import { setBrewingStatus } from '../Brewing'
import { HTTPException } from 'hono/http-exception'
import { cancelJob, scheduleJob } from 'node-schedule'
import { captureException } from '@sentry/node'
import { DateTime } from 'luxon'

import { BrewingHop } from '@prisma/client'
import { sendNotification } from '../../helpers/websocket'
import { sendPushNotification } from '../../helpers/web-push'

export const BREWING_BOILING_JOB = (brewingId: number) => `brewing-${brewingId}-boiling`

const BREWING_BOILING_JOB_CRON = '* * * * * *'

export const startBoiling = async (brewingId: number, userId: string) => {
  const brewing = await prisma.brewing.findUnique({
    where: {
      id: brewingId,
    },
  })

  if (!brewing) {
    throw new HTTPException(404, { message: 'Brewing not found' })
  }

  await setBrewingStatus(brewingId, 'BOILING')
  scheduleJob(
    BREWING_BOILING_JOB(brewingId),
    BREWING_BOILING_JOB_CRON,
    runBoilingJob(brewingId, userId)
  )
}

export const startBoilingJob = async (brewingId: number, userId: string) => {
  scheduleJob(
    BREWING_BOILING_JOB(brewingId),
    BREWING_BOILING_JOB_CRON,
    runBoilingJob(brewingId, userId)
  )
}

const runBoilingJob = (brewingId: number, userId: string) => {
  return async (fireDate: Date) => {
    try {
      const brewing = await prisma.brewing.findUnique({
        where: {
          id: brewingId,
        },
        include: {
          hops: true,
        },
      })

      if (!brewing || brewing.boilStartTime === null) {
        cancelJob(BREWING_BOILING_JOB(brewingId))
        return
      }

      const hops = brewing.hops.filter(h => !h.sendPushNotification)

      const minutesSinceStart = DateTime.fromJSDate(brewing.boilStartTime as Date)
        .diffNow('minutes')
        .toObject().minutes

      if (!minutesSinceStart) {
        return
      }

      const sendPerPushHops: BrewingHop[] = []
      for (const hop of hops) {
        if (hop.time <= minutesSinceStart) {
          sendPerPushHops.push(hop)
        }
      }

      if (sendPerPushHops.length > 0) {
        sendPushNotification({
          messageType: 'brewingHops',
          hops: sendPerPushHops,
        })

        sendNotification(userId, {
          hops: sendPerPushHops,
          message: 'HOP_ADDED',
        })

        for (const hop of sendPerPushHops) {
          await prisma.brewingHop.update({
            where: {
              id: hop.id,
            },
            data: {
              sendPushNotification: true,
            },
          })
        }
      }

      if (minutesSinceStart >= brewing.boilTime) {
        cancelJob(BREWING_BOILING_JOB(brewingId))
        await setBrewingStatus(brewingId, 'FERMENTATION')
      }
    } catch (e) {
      captureException(e)
      cancelJob(BREWING_BOILING_JOB(brewingId))
    }
  }
}
