import prisma from '../../helpers/prisma'
import { startBoilingJob } from './startBoiling'

export default async function onAppStartBoilingJobs() {
  const brewings = await prisma.brewing.findMany({
    where: {
      status: { in: ['BOILING'] },
    },
  })

  for (const brewing of brewings) {
    if (!brewing.boilStartTime) {
      continue
    }

    startBoilingJob(brewing.id, '') // TODO: userId
  }
}
