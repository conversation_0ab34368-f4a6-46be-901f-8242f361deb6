import prisma from '../../helpers/prisma'
import onAppStartBoilingJobs from './onAppStartBoilingJobs'
import startMashingJob from './startMashingJob'

export default function onAppInit() {
  onAppStartBoilingJobs()
  onAppStartBrewingJobs()
}

export const onAppStartBrewingJobs = async () => {
  const brewings = await prisma.brewing.findMany({
    where: {
      status: { in: ['MASHING'] },
    },
    include: {
      mashSteps: true,
    },
  })

  for (const brewing of brewings) {
    console.debug('OnStart: Started brewing job', brewing.id)
    startMashingJob(brewing.id)
  }
}
