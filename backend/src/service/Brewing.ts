import prisma from '../helpers/prisma'
import schedule from 'node-schedule'
import { Devi<PERSON>, DeviceConnected, Brewing, Prisma } from '@prisma/client'
import { setDeviceSwitch } from './Device'
import { sendData } from '../helpers/websocket'
import { BREWING_MASHING_JOB } from './Brewing/startMashingJob'
import { BREWING_BOILING_JOB } from './Brewing/startBoiling'

export type BrewingDevice = DeviceConnected & { device: Device }

export const MQTT_BREWING_TOPIC = (brewingId: number) => `user/1/brewing/${brewingId}`

export const setBrewingStatus = async (brewingId: number, status: Brewing['status']) => {
  const data: Prisma.BrewingUpdateInput = { status }

  if (status === 'DONE' || status === 'LAUTERING') {
    schedule.cancelJob(BREWING_MASHING_JOB(brewingId))
  }

  if (status === 'DONE') {
    data.endTime = new Date()
  }

  if (status === 'BOILING') {
    data.boilStartTime = new Date()
  }

  sendData(MQTT_BREWING_TOPIC(brewingId), {
    status: status,
    message: 'STATUS_CHANGED',
  })

  return await prisma.brewing.update({
    where: {
      id: brewingId,
    },
    data,
  })
}

export const getBrewingDevices = async () => {
  return await prisma.deviceConnected.findMany({
    where: {
      use: 'BREWING',
    },
    include: {
      device: true,
    },
  })
}

export const setHeating = async (devices: BrewingDevice[], status: boolean) => {
  const heatingDevice = devices.filter(device => device.type === 'HEATING')

  if (heatingDevice.length === 0) {
    return
  }

  for (const device of heatingDevice) {
    setDeviceSwitch(device.device, status)
  }
}

export const getBrewing = async (id: number) => {
  const brewing = await prisma.brewing.findUnique({
    where: {
      id,
    },
    include: {
      mashSteps: true,
    },
  })

  if (!brewing) {
    throw new Error('Brewing not found')
  }

  return brewing
}

export const deleteBrewing = async (brewingId: number) => {
  const model = await prisma.brewing.delete({
    where: { id: brewingId },
  })

  schedule.cancelJob(BREWING_MASHING_JOB(model.id))
  schedule.cancelJob(BREWING_BOILING_JOB(model.id))

  return model
}
