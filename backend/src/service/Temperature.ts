import prisma from '../helpers/prisma'

export async function findActualTemperature(deviceId: number) {
  const now = new Date()

  const model = await prisma.temperature.findFirst({
    where: {
      AND: [
        {
          deviceId: deviceId,
        },
        {
          startDate: {
            lte: now,
          },
        },
        {
          OR: [
            {
              endDate: {
                gte: now,
              },
            },
            { endDate: null },
          ],
        },
      ],
    },
  })

  return model
}
