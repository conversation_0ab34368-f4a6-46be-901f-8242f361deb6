import { Device } from '@prisma/client'
import { getStatusValues, setSwitch } from './Integration/Tuya'

export const setDeviceSwitch = async (device: Device, value: boolean) => {
  if (device.operator === 'TUYA') {
    await setSwitch(device.tuyaDeviceId as string, value)
  } else {
    console.error('No operator found', device)
  }
}

export const getTemperature = async (device: Device): Promise<number> => {
  if (device.operator === 'TUYA') {
    const status = await getStatusValues<
      [
        { code: 'va_temperature'; value: number },
        { code: 'temp_unit_convert'; value: 'c' | 'f' },
        { code: 'temp_alarm'; value: 'cancel' | string },
      ]
    >(device.tuyaDeviceId as string)

    const statusIndex = status.find(item => item.code === 'va_temperature') as { value: number }
    if (!statusIndex) {
      throw new Error('No temperature value found')
    }

    return statusIndex.value / 10
  } else {
    // TODO: Implement other operators
    throw new Error('No temperature device integration found')
  }
}
