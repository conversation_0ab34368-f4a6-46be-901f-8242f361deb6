import { Prisma } from '@prisma/client'
import prisma from '../../helpers/prisma'
import { Notification } from 'types/notification'
import { sendWebPush } from './WebPushService'
import { InputJsonValue } from '@prisma/client/runtime/library'
// import i18next from '../integration/i18n'

export default async function notify(userId: string, notification: Notification) {
  // TODO: add i18n support
  //const text = i18next.t(notification.text, { lng: 'de' })

  const data: Prisma.NotificationCreateInput = {
    ...notification,
    data: notification.data as InputJsonValue,
    user: { connect: { id: userId } },
  }

  const subscriptions = await prisma.pushSubscription.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (subscriptions.length > 0) {
    sendWebPush(notification, subscriptions)
  }

  await prisma.notification.create({
    data,
  })
}
