import prisma from '../../helpers/prisma'
import { Device, StatisticValues } from '@prisma/client'
import { findActualTemperature } from '../Temperature'
import notify from './NotificationService'

const TEMPERATURE_TOLERANCE = 0.2
export const notifyOnFermentationTemperatureReached = async (
  device: Device,
  value: StatisticValues
) => {
  const actual = await findActualTemperature(device.id)
  if (!actual?.sendPush) {
    return
  }

  const diff = Math.abs(actual.temperature - value.temperature)
  if (diff >= TEMPERATURE_TOLERANCE) {
    return
  }

  if (!device.userId) {
    console.error('Device has no userId')
    return
  }

  try {
    await notify(device.userId, {
      title: `${device.name} - Temperatur erreicht`,
      text: `Die Temperatur von ${actual.temperature}°C wurde erreicht.`,
      data: {
        deviceName: device.name,
        deviceId: device.id,
        temperature: actual.temperature,
      },
    })

    await prisma.temperature.update({
      where: { id: actual.id },
      data: { sendPush: false },
    })
  } catch (err) {
    console.log('Error sending push notification: ', err)
  }
}
