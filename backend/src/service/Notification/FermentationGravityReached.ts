import prisma from '../../helpers/prisma'
import { DeviceType, SpindelLog } from '@prisma/client'
import { findActualTemperature } from '../Temperature'
import notify from './NotificationService'

export const notifyOnFermentationGravityReached = async (deviceId: number, value: SpindelLog) => {
  const device = await prisma.device.findFirst({
    where: {
      deviceConnections: {
        some: {
          id: deviceId,
        },
      },
      type: DeviceType.BREWTOOL,
    },
  })

  if (!device) {
    // No Brewtool connected
    return
  }

  const config = await findActualTemperature(device.id)
  if (!config?.gravityNotify) {
    return
  }

  const diff = value.gravity - config.gravityNotify
  if (diff <= 0) {
    return
  }

  if (!device.userId) {
    console.error('<PERSON><PERSON> has no userId')
    return
  }

  try {
    await notify(device.userId, {
      title: `${device.name} - <PERSON> erreicht`,
      text: `Der Platowert von ${value.gravity} °P wurde erreicht.`,
      data: {
        deviceName: device.name,
        deviceId: device.id,
        gravity: value.gravity,
      },
    })

    await prisma.temperature.update({
      where: {
        id: config.id,
      },
      data: {
        gravityNotify: null,
      },
    })
  } catch (err) {
    console.log('Error sending push notification: ', err)
  }
}
