import { cancelJob, scheduleJob } from 'node-schedule'
import { getBrewing, getBrewingDevices, setBrewingStatus, setHeating } from '../Brewing'
import runMashStep from './runMashStep'

export const BREWING_MASHING_JOB = (brewingId: number) => `brewing-${brewingId}-mashing`

const MASHING_JOB_CRON = '*/30 * * * * *'

export default function startMashingJob(brewingId: number) {
  scheduleJob(BREWING_MASHING_JOB(brewingId), MASHING_JOB_CRON, createMashJobFn(brewingId))
}

const createMashJobFn = (brewingId: number) => {
  return async (fireDate: Date) => {
    console.debug('Running job for mashing', brewingId)
    try {
      const brewing = await getBrewing(brewingId)

      const devices = await getBrewingDevices()

      if (brewing.status !== 'MASHING') {
        await setHeating(devices, false)
        cancelJob(BREWING_MASHING_JOB(brewingId))
        return
      }

      const activeStep = brewing.mashSteps.find(
        step => step.status === 'ACTIVE' || step.status === 'HEATING'
      )

      if (!activeStep) {
        console.error('No active step found in brewing', brewingId)
        await setHeating(devices, false)
        await setBrewingStatus(brewingId, 'LAUTERING')
        return
      }

      await runMashStep(activeStep, fireDate)
    } catch (error) {
      console.error(`Error in brewing job ${brewingId}:`, error)
      // after 5 errors, stop the job
      // set status to canceled
    }
  }
}

import { BrewingMashStep } from '@prisma/client'
import { sendData } from '../../helpers/websocket'
import prisma from '../../helpers/prisma'
import { DateTime } from 'luxon'
import {
  BrewingDevice,
  MQTT_BREWING_TOPIC,
  getBrewing,
  getBrewingDevices,
  setBrewingStatus,
  setHeating,
} from '../Brewing'
import { getTemperature } from '../Device'

export default async function runMashStep(mashStep: BrewingMashStep, fireDate: Date) {
  const devices = await getBrewingDevices()

  const temperature = await getTemperatureForDevice(devices)

  const isReached = isTemperatureReached(temperature, mashStep.temperature)

  if (mashStep.status === 'ACTIVE') {
    const minutes = Math.abs(
      DateTime.fromJSDate(mashStep.startTime ?? new Date()).diffNow('minutes').minutes
    )

    if (minutes >= mashStep.time) {
      await nextStep(mashStep, fireDate)
      return
    }
  }

  if (
    isReached &&
    (mashStep.status === 'WAITING' || mashStep.status === null || mashStep.status === 'HEATING')
  ) {
    startActiveMashing(mashStep)
  } else if (!isReached && (mashStep.status === 'WAITING' || mashStep.status === null)) {
    startHeating(mashStep)
  }

  await setHeating(devices, temperature < mashStep.temperature)
}

const nextStep = async (step: BrewingMashStep, fireDate: Date) => {
  await prisma.brewingMashStep.update({
    where: {
      id: step.id,
    },
    data: {
      status: 'DONE',
      endTime: new Date(),
    },
  })

  const brewing = await getBrewing(step.brewingId)

  const nextStep = brewing.mashSteps.find(step => step.status === 'WAITING')
  if (!nextStep) {
    await setBrewingStatus(brewing.id, 'LAUTERING')
    const devices = await getBrewingDevices()
    await setHeating(devices, false)
  } else {
    await runMashStep(nextStep, fireDate)
  }
}

const startActiveMashing = async (step: BrewingMashStep) => {
  await prisma.brewingMashStep.update({
    where: {
      id: step.id,
    },
    data: {
      status: 'ACTIVE',
      startTime: new Date(),
      heatingStart: step.heatingStart ?? new Date(),
    },
  })

  const startTime = DateTime.now()
    .plus({ minutes: Math.abs(step.time) })
    .toJSDate()

  console.log(
    `Starting mash step ${step.id} in brewing ${step.brewingId} at ${startTime} for ${step.time} minutes`,
    step
  )

  sendData(MQTT_BREWING_TOPIC(step.brewingId), {
    stepId: step.id,
    message: 'STEP_STARTED',
  })
}

const startHeating = async (step: BrewingMashStep) => {
  await prisma.brewingMashStep.update({
    where: {
      id: step.id,
    },
    data: {
      status: 'HEATING',
      heatingStart: new Date(),
    },
  })

  sendData(MQTT_BREWING_TOPIC(step.brewingId), {
    stepId: step.id,
    message: 'HEATING_STARTED',
  })
}

const TEMPERATURE_TOLERANCE_ON_HEATING = 0.75

const isTemperatureReached = (temperature: number, shouldTemperature: number) => {
  return (
    temperature >= shouldTemperature &&
    temperature <= shouldTemperature + TEMPERATURE_TOLERANCE_ON_HEATING
  )
}

const getTemperatureForDevice = async (devices: BrewingDevice[]): Promise<number> => {
  const temperatureDevice = devices.find(device => device.type === 'TEMPERATURE')

  if (!temperatureDevice) {
    throw new Error('No temperature device found')
  }

  return getTemperature(temperatureDevice.device)
}
