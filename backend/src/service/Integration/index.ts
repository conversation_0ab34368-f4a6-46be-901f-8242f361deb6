import prisma from '../../helpers/prisma'

export async function getIntegrationData<T>(integration: string, key: string) {
  const data = await prisma.integrationData.findFirst({
    where: {
      integration,
      key,
    },
  })

  if (data?.value === undefined) {
    return null
  }

  const account = data.value as T
  return account
}

export async function saveIntegrationData(
  integration: string,
  key: string,
  data: Record<string, any>
) {
  await prisma.integrationData.deleteMany({
    where: {
      integration,
      key,
    },
  })

  await prisma.integrationData.create({
    data: {
      integration,
      key,
      value: data,
    },
  })
}
