import querystring from 'node:querystring'
import nodeCache from '../../helpers/nodeCache'
import { Recipe } from '@prisma/client'
import { getIntegrationData } from '.'
import * as cheerio from 'cheerio'

export const url = 'https://braureka.de/wp-admin/admin-ajax.php'

export const LABEL_BRAUREKA_COOKIE = 'braureka-cookie'

export type BraurekaLoginResponse =
  | {
      success: false
      data: string
    }
  | {
      success: true
      data: { message: string; username: string; roles: string[] }
    }

export type BraurekaAccount = {
  username: string
  password: string
  wpUsername?: string
}

export const hasBraureka = async () => {
  return (await getIntegrationData<BraurekaAccount>('braureka', 'account')) !== null
}

async function fetchBraurekaRecipes(cookie: string) {
  const response = await fetch(
    url +
      '?draw=1&columns[0][data]=0&columns[0][name]=&columns[0][searchable]=true&columns[0][orderable]=true&columns[0][search][value]=&columns[0][search][regex]=false&columns[1][data]=1&columns[1][name]=&columns[1][searchable]=true&columns[1][orderable]=true&columns[1][search][value]=&columns[1][search][regex]=false&columns[2][data]=2&columns[2][name]=&columns[2][searchable]=true&columns[2][orderable]=true&columns[2][search][value]=&columns[2][search][regex]=false&columns[3][data]=3&columns[3][name]=&columns[3][searchable]=true&columns[3][orderable]=true&columns[3][search][value]=&columns[3][search][regex]=false&columns[4][data]=4&columns[4][name]=&columns[4][searchable]=true&columns[4][orderable]=true&columns[4][search][value]=&columns[4][search][regex]=false&columns[5][data]=5&columns[5][name]=&columns[5][searchable]=true&columns[5][orderable]=true&columns[5][search][value]=&columns[5][search][regex]=false&columns[6][data]=6&columns[6][name]=&columns[6][searchable]=true&columns[6][orderable]=true&columns[6][search][value]=&columns[6][search][regex]=false&columns[7][data]=7&columns[7][name]=&columns[7][searchable]=true&columns[7][orderable]=true&columns[7][search][value]=&columns[7][search][regex]=false&columns[8][data]=8&columns[8][name]=&columns[8][searchable]=true&columns[8][orderable]=true&columns[8][search][value]=&columns[8][search][regex]=false&order[0][column]=0&order[0][dir]=desc&start=0&length=10&search[value]=&search[regex]=false&action=wpcm_get_recipe_datatable&public=&all=',
    {
      headers: {
        Accept: 'application/json, */*',
        Cookie: cookie,
        'X-Requested-With': 'XMLHttpRequest',
      },
    }
  )

  return response
}

export async function getBraurekaRecipes() {
  const account = await getBraurekaAccount()
  if (!account) {
    return
  }

  try {
    const cookie = await getBraurekaCookie()
    let response = await fetchBraurekaRecipes(cookie)

    if (response.status === 401) {
      const newCookie = await refreshBraurekaCookie()
      if (newCookie === null) {
        return
      }
      response = await fetchBraurekaRecipes(newCookie)
    }

    if (response.status > 201) {
      // TODO: throw
      return
    }

    const data = (await response.json()) as { data: string[9][] }

    const result = data.data.map((recipe: string[9]) => {
      return {
        id: Number(recipe[0]),
        name: recipe[1],
        brewer: recipe[2],
        batchSize: Number(recipe[4].replace(' l', '')),
        boilSize: 0,
        boilTime: 0,
        efficiency: null,
        ibu: recipe[7],
        abv: recipe[6],
        age: null,
        ageTemperature: null,
        preGravity: Number(recipe[5].replace(' °P', '')),
        finishedGravity: null,
        file: 'braureka',
        createdAt: new Date(),
      } as Recipe
    })

    return result
  } catch (error) {
    return []
  }
}

async function fetchBraurekaRecipe(cookie: string, recipeId: string) {
  const response = await fetch(`https://braureka.de/rezept-export/?format=xml&id=${recipeId}`, {
    headers: {
      Cookie: cookie,
    },
  })

  return response
}

export async function getBraurekaRecipe(recipeId: string) {
  const account = await getBraurekaAccount()
  if (!account) {
    return
  }

  const cookie = await getBraurekaCookie()
  let response = await fetchBraurekaRecipe(cookie, recipeId)

  if (response.status === 401) {
    const newCookie = await refreshBraurekaCookie()
    if (newCookie === null) {
      return
    }
    response = await fetchBraurekaRecipe(newCookie, recipeId)
  }

  if (response.status > 201) {
    // TODO: throw
    return
  }

  const html = await response.text()

  const dom = cheerio.load(html)

  const xml = dom('textarea#export-content').text()

  return {
    recipeId,
    beerXml: xml,
  }
}

async function getBraurekaCookie() {
  const cookie = nodeCache.get(LABEL_BRAUREKA_COOKIE) as string

  if (!cookie) {
    const refreshedCookie = await refreshBraurekaCookie()
    if (refreshedCookie === null) {
      throw new Error('Could not refresh cookie')
    }
    return refreshedCookie
  }

  return cookie
}

export async function loginBraureka(account: BraurekaAccount) {
  const response = await fetch(url, {
    method: 'POST',
    body: querystring.stringify({
      action: 'wpcm_login',
      username: account.username,
      password: account.password,
      rememberme: 'true',
    }),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'X-Requested-With': 'XMLHttpRequest',
    },
  })

  return response
}

export async function getBraurekaAccount() {
  return getIntegrationData<BraurekaAccount>('braureka', 'account')
}

async function refreshBraurekaCookie() {
  const account = await getBraurekaAccount()

  if (!account) {
    return null
  }

  const response = await loginBraureka(account)

  if (response.status > 201) {
    return null
  }

  const loginData = (await response.json()) as BraurekaLoginResponse

  if (!loginData.success) {
    return null
  }

  const cookie = parseCookies(response)
  nodeCache.set(LABEL_BRAUREKA_COOKIE, cookie)

  return cookie
}

export function parseCookies(response: Response) {
  const raw = response.headers.getSetCookie()

  if (!raw) {
    return ''
  }

  return raw
    .map(entry => {
      const parts = entry.split(';')
      const cookiePart = parts[0]
      return cookiePart
    })
    .join(';')
}
