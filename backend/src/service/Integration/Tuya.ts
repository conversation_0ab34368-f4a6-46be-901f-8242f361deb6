import { TuyaContext } from '@tuya/tuya-connector-nodejs'
import { getIntegrationData } from '.'

let _tuya: TuyaContext | null = null

export type TuyaCloudAccess = {
  accessKey: string
  secretKey: string
}

export const INTEGRATION_TUYA_CLOUD = 'cloudAccess'

export const hasTuya = async () => {
  return (await getIntegrationData('tuya', INTEGRATION_TUYA_CLOUD)) !== null
}

export const getTuyaCloud = async () => {
  return getIntegrationData<TuyaCloudAccess>('tuya', INTEGRATION_TUYA_CLOUD)
}

export const getTuyaContext = async () => {
  const cloudAccess = await getIntegrationData<TuyaCloudAccess>('tuya', INTEGRATION_TUYA_CLOUD)

  if (!cloudAccess) {
    throw new Error('Tuya Integration must be set')
  }

  if (_tuya) {
    return _tuya
  }

  const tuya = new TuyaContext({
    baseUrl: 'https://openapi.tuyaeu.com',
    accessKey: cloudAccess.accessKey,
    secretKey: cloudAccess.secretKey,
  })

  _tuya = tuya

  return tuya
}

export const setSwitch = async (deviceId: string, value: boolean) => {
  try {
    const tuya = await getTuyaContext()

    const result = await tuya.request({
      path: '/v1.0/iot-03/devices/' + deviceId + '/commands',
      method: 'POST',
      body: {
        device_id: deviceId,
        commands: [
          {
            code: 'switch_1',
            value: value,
          },
        ],
      },
    })

    return result
  } catch (e) {
    console.error(e)
  }
}

export const getStatusValues = async <T>(deviceId: string) => {
  const tuya = await getTuyaContext()

  const response = await tuya.request({
    path: '/v1.0/iot-03/devices/' + deviceId + '/status',
    method: 'GET',
  })

  return response.result as T
}
