import { Prisma } from '@prisma/client'
import { ZodError, ZodObject } from 'zod'

export type ValidationOptions = {
  removeFields?: string[]
  isImport?: boolean
}

export default function validate<T>(
  model: ZodObject<any>,
  data: any,
  options?: ValidationOptions
): T {
  try {
    return parseModel<T>(model, data)
  } catch (error) {
    if (error instanceof ZodError) {
      const newData: { [key: string]: any } = data as { [key: string]: any }
      error.errors.forEach(err => {
        if (
          err.code === 'invalid_type' &&
          err.expected === 'date' &&
          err.received === 'string' &&
          typeof newData[err.path[0]] === 'string'
        ) {
          newData[err.path[0]] = new Date(newData[err.path[0]])
        }
      })

      return parseModel<T>(model, newData)
    }

    throw error
  }
}

function getRemoveFields(options?: ValidationOptions) {
  if (!options?.removeFields) {
    return {}
  }

  const removeFields: { [key: string]: any } = {}
  options.removeFields.forEach(field => {
    removeFields[field] = true
  })
  return removeFields
}

function parseModel<T>(model: ZodObject<any>, data: any, options?: ValidationOptions) {
  const validateData = model
    .omit(
      options?.isImport
        ? {}
        : {
            id: true,
            guid: true,
            version: true,
            updatedAt: true,
            ...(getRemoveFields(options) as any),
          }
    )
    .partial()
    .parse(data)

  return Prisma.validator<T>()(validateData as any)
}
