import { User } from '@prisma/client'
import { LinearRouter } from 'hono/router/linear-router'
import { getLucia } from '../integration/lucia'
import type { IncomingMessage, Server } from 'node:http'
import type { Http2SecureServer, Http2Server } from 'node:http2'
import WebSocket, { WebSocketServer } from 'ws'

class WsAuthenticationError extends Error {
  constructor() {
    super('Session not valid')
  }
}

const onSocketError = (err: unknown) => {
  console.error(err)
}

const wss = new WebSocketServer({ noServer: true })

const authenticate = async (request: IncomingMessage) => {
  const lucia = await getLucia()

  const protocol = request.headers['sec-websocket-protocol']

  const sessionId = lucia.readBearerToken(`Bearer ${protocol}`)
  if (!sessionId) {
    throw new WsAuthenticationError()
  }

  const { user } = await lucia.validateSession(sessionId)
  if (!user) {
    throw new WsAuthenticationError()
  }

  return user
}

const connections = new Map<string, Set<WebSocket>>()

wss.on('connection', (ws: WebSocket, user: User, pathname: string) => {
  const route = pathname.replace('/ws', '')
  const set = connections.get(route) ?? new Set<WebSocket>()
  set.add(ws)
  connections.set(route, set)

  ws.on('error', console.error)

  ws.on('message', function message(data) {
    console.log('received: %s', data, user)
  })

  ws.on('close', function close() {
    set.delete(ws)
  })
})

export const startWebsocket = (server: Server | Http2Server | Http2SecureServer) => {
  server.on('upgrade', function upgrade(request, socket, head) {
    const { pathname } = new URL(request.url ?? '/', 'http://localhost')

    if (!pathname.startsWith('/ws')) {
      socket.write('HTTP/1.1 400 Bad Request\r\n\r\n')
      socket.destroy()
      return
    }

    socket.on('error', onSocketError)

    authenticate(request)
      .then(user => {
        wss.handleUpgrade(request, socket, head, function done(ws) {
          wss.emit('connection', ws, user, pathname)
        })
      })
      .catch(() => {
        socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n')
        socket.destroy()
      })
  })
}

const subscriber = new LinearRouter()
export const subscribeTopic = (route: string, callback: (data: object, user: User) => void) => {
  subscriber.add('GET', route, callback)
}

export const sendNotification = (userId: string, data: object) => {
  sendData(`/user/${userId}/notification`, data)
}

export const sendData = (route: string, data: object) => {
  const set = connections.get(route)
  if (set) {
    set.forEach(ws => {
      ws.send(JSON.stringify(data))
    })
  }
}
