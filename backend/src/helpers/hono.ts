import { Context, Hono } from 'hono'
import { cors } from 'hono/cors'
import { Session, User } from '@prisma/client'

type AuthenticationEnv = {
  user?: User
  session?: Session
}

export const app = new Hono().basePath('/api')

app.use('*', async (c, next) => {
  const fnCors = cors({
    allowMethods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    origin: process.env.NODE_ENV === 'production' ? 'https://gaerschrank.philun.de' : '*',
  })
  return await fnCors(c, next)
})

const authApp = new Hono<{ Variables: AuthenticationEnv }>()

export default authApp

export const getUserId = (c: Context): string | undefined => {
  const user = c.get('user')
  if (user) {
    return user.id
  }

  return undefined
}
