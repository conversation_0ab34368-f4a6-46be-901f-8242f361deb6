import { Prisma, PushSubscription } from '@prisma/client'
import webpush, { WebPushError } from 'web-push'
import prisma from './prisma'
import { PushData } from 'types/shared'
import { captureException } from '@sentry/node'

let webPushFailed = false
if (process.env.WEB_PUSH_VAPID_PRIVATE_KEY && process.env.WEB_PUSH_VAPID_PUBLIC_KEY) {
  try {
    webpush.setVapidDetails(
      'mailto:<EMAIL>',
      process.env.WEB_PUSH_VAPID_PUBLIC_KEY,
      process.env.WEB_PUSH_VAPID_PRIVATE_KEY
    )
  } catch (err) {
    console.error('Error setting VAPID details: ', err)
    webPushFailed = true
  }
}

export async function sendPushNotification(payload: PushData) {
  if (!process.env.WEB_PUSH_VAPID_PRIVATE_KEY || !process.env.WEB_PUSH_VAPID_PUBLIC_KEY) {
    throw new Error('VAPID keys are not set')
  }

  if (webPushFailed) {
    throw new Error('Web Push failed')
  }

  const subscriptions = await prisma.pushSubscription.findMany({
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (subscriptions === null) {
    throw new Error('No subscriptions found')
  }

  for (const subscription of subscriptions) {
    await prisma.pushNotification.create({
      data: {
        data: payload as Prisma.InputJsonValue,
        pushSubscriptionId: subscription.id,
      },
    })

    sendMessageTpSubscription(subscription, payload)
  }
}

async function sendMessageTpSubscription(subscription: PushSubscription, payload: PushData) {
  const sub: webpush.PushSubscription = {
    endpoint: subscription.endpoint,
    // @ts-ignore
    keys: subscription.keys as PushSubscription['keys'],
  }

  try {
    return await webpush.sendNotification(sub, JSON.stringify(payload))
  } catch (err) {
    console.log('Error sending notification, reason: ', err)
    if (err instanceof WebPushError && (err.statusCode === 404 || err.statusCode === 410)) {
      console.log('Subscription has expired or is no longer valid: ', err)
      await prisma.pushSubscription.delete({
        where: {
          id: subscription.id,
        },
      })
    } else {
      captureException(err)
    }
  }
}
