version: '3.9' # optional since v1.27.0

name: unrup1-gaerschrank-backend
services:
  unrup1-gaerschrank-backend-mysql:
    container_name: unrup1-gaerschrank-backend-mysql
    image: mysql:5.7
    platform: linux/amd64
    environment:
      MYSQL_DATABASE: 'gaerschrank'
      MYSQL_USER: 'gaerschrank'
      MYSQL_PASSWORD: 'password!'
      MYSQL_ROOT_PASSWORD: 'password!'
    restart: always
    ports:
      - 3306:3306
    expose:
      - 3306
    volumes:
      - /var/lib/mysql
    networks:
      - internal

  unrup1-gaerschrank-backend-mqtt:
    container_name: unrup1-gaerschrank-backend-mqtt
    image: emqx/nanomq:latest
    platform: linux/amd64
    restart: always
    ports:
      - 1883:1883
      - 8083:8083
      - 8084:8084
      - 8883:8883
    expose:
      - 1883
      - 8083
      - 8084
      - 8883
    networks:
      - internal
    extra_hosts:
      - "host.docker.internal:host-gateway"


networks:
  internal:
    driver: bridge
