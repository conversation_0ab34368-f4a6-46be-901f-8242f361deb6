name: Linting

on:
  workflow_dispatch:
    # allows to run the workflow manually
  pull_request:
    types: [ready_for_review, synchronize, opened, reopened]
    branches:
      - main
  push:
    branches:
      - main

jobs:
  lint_frontend:
    runs-on: ubuntu-latest
    name: Linting Frontend
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Yarn Setup
        uses: ./.github/actions/yarn-setup
        with:
          node-version-file: .nvmrc

      - name: Lint
        shell: bash
        run: yarn run lint
        working-directory: ./frontend

  # lint_backend:
  #   runs-on: ubuntu-latest
  #   name: Linting Backend
  #   steps:
  #     - name: Checkout
  #       uses: actions/checkout@v4
  #       with:
  #         submodules: true

  #     - name: Yarn Setup
  #       uses: ./.github/actions/yarn-setup
  #       with:
  #         node-version-file: .nvmrc

  #     - name: Lint
  #       shell: bash
  #       run: yarn run lint
  #       working-directory: ./backend
