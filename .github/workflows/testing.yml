name: Testing

on:
  workflow_dispatch:
    # allows to run the workflow manually
  pull_request:
    types: [ready_for_review, synchronize, opened, reopened]
    branches:
      - main
  push:
    branches:
      - main

jobs:
  testing_backend:
    runs-on: ubuntu-latest
    name: Testing Backend
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Yarn Setup
        uses: ./.github/actions/yarn-setup
        with:
          node-version-file: .nvmrc

      - name: Testing
        shell: bash
        run: yarn run test
        working-directory: ./backend

  testing_fontend:
    runs-on: ubuntu-latest
    name: Testing Frontend
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Yarn Setup
        uses: ./.github/actions/yarn-setup
        with:
          node-version-file: .nvmrc

      - name: Testing
        shell: bash
        run: yarn run test
        working-directory: ./frontend
