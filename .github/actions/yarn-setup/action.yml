name: Yarn Setup
description: GitHub Action for setting up a Yarn environment. Caches dependencies for reduced execution times. Then installs the dependencies.
branding:
  icon: package
  color: blue

inputs:
  node-version-file:
    description: The version of Node.js that will be used according to the '.nvmrc' file
    required: true

outputs:
  cache-hit:
    description: Indicates a cache hit
    value: ${{ steps.cache.outputs.cache-hit }}

runs:
  using: composite
  steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: ${{inputs.node-version-file}}

    - name: Enable Corepack
      shell: bash
      run: corepack enable

    - name: Cache ~/.cache
      id: cache
      uses: actions/cache@v4
      with:
        path: ~/.cache
        key: ${{ runner.os }}-cache-${{ hashFiles('./yarn.lock') }}

    - name: Install
      shell: bash
      run: yarn install --frozen-lockfile
