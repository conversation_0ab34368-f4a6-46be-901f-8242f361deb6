{"build": {"arduino": {"ldscript": "esp32_out.ld"}, "core": "esp32", "extra_flags": "-DARDUINO_ESP32_DEV", "f_cpu": "240000000L", "f_flash": "40000000L", "flash_mode": "dio", "mcu": "esp32", "variant": "esp32"}, "connectivity": ["wifi", "bluetooth", "ethernet", "can"], "debug": {"openocd_board": "esp32-wrover.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "Espressif ESP32-WROVER-IE 16MB (ESP32-D0WD-V3)", "upload": {"flash_size": "4MB", "maximum_ram_size": 327680, "maximum_size": 16777216, "require_upload_port": true, "speed": 460800}, "url": "https://en.wikipedia.org/wiki/ESP32", "vendor": "E<PERSON>ress<PERSON>"}