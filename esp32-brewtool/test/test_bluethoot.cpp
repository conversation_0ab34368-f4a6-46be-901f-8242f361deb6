#include <Arduino.h>
#include <unity.h>

void test_allBleVariablesDefined() {
    TEST_ASSERT_EQUAL_STRING(BLE_NAME, "Philun-Brewtool");
    TEST_ASSERT_EQUAL_STRING(BLE_SERVICE_GENERAL_UUID, "91bad492-b950-4226-aa2b-4ede9fa42f59");
    TEST_ASSERT_EQUAL_STRING(BLE_CHARACTERISTIC_WIFI_SSID_UUID, "cba1d466-344c-4be3-ab3f-189f80dd7518");
    TEST_ASSERT_EQUAL_STRING(BLE_CHARACTERISTIC_WIFI_PASSWORD_UUID, "cba1d466-344c-4be3-ab3f-189f80dd7519");
}

void runUnityTestsForBluethoot() {
    // UNITY_BEGIN();

    RUN_TEST(test_allBleVariablesDefined);

    // return UNITY_END();
}
