#include <Arduino.h>
#include <unity.h>
#include <DynamicWifi.hpp>
#include <Http.hpp>
#include "test_api.hpp"
// #include "test_bluethoot.hpp"

void setUp(void) {
    setupWifi();
    onInitHttp();
    
    setWifiSsid(WIFI_SSID);
    setWifiPassword(WIFI_PASSWORD);
    delay(1000);
}

void tearDown(void) {
    // clean stuff up here
}

/**
  * For Arduino framework
  */
void setup() {
    // Wait ~2 seconds before the Unity test runner
    // establishes connection with a board Serial interface
    delay(2000);

    UNITY_BEGIN();

    runUnityTestsForApi();
    // runUnityTestsForBluethoot();

    UNITY_END();
}
void loop() {}
