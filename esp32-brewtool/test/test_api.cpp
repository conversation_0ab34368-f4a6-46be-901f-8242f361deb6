#include <Arduino.h>
#include <unity.h>
#include <Api.hpp>

void test_loadTimeFromApi() {
    long timestamp = loadTimeFromApi();

    TEST_ASSERT_GREATER_THAN(0, timestamp);
}

void test_getTemperature() {
   int temperature = getDestinatedTemperature();

   TEST_ASSERT_EQUAL(10, temperature);
}

void runUnityTestsForApi() {
    RUN_TEST(test_getTemperature);
    // RUN_TEST(test_loadTimeFromApi);
}
