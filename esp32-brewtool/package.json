{"name": "server-test", "scripts": {"server": "tsx test-server/index.ts", "dev": "tsx watch test-server/index.ts", "test": "pio test -e test", "install-board": "mkdir -p ~/.platformio/boards && cp esp32v3.json ~/.platformio/boards/esp32v3.json && (pio boards --installed | grep \"esp32v3\")"}, "dependencies": {"@hono/node-server": "^1.12.1", "hono": "^4.5.8"}, "devDependencies": {"@types/node": "^20.11.17", "tsx": "^4.7.1"}}