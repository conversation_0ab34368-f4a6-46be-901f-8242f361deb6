Import("env")

environment = env.subst("$PIOENV")

print("Current Env: ", environment)

import json
import os

print("Setting BLE variables")

prefix = "BLE_"

ble_path = '../shared/assets/ble.json'
package_path = '../shared/package.json'

def get_ble_vars():
    with open(ble_path) as f:
        d = json.load(f)
        print("Name: " + d["name"])
        return d

def set_general_ble_vars(d):
    name = prefix + "NAME"
    print("Added: " + name)
    env.Append(CPPDEFINES=[(name, env.StringifyMacro(d["name"]))])

def add_ble_characteristics(service):
    for characteristic in service["characteristics"]:
        add_ble_characteristic(characteristic)

def add_ble_characteristic(characteristic):
    name = prefix + "CHARACTERISTIC_" + characteristic["name"].upper() + '_UUID'
    print("Added: " + name)
    env.Append(CPPDEFINES=[(name, env.StringifyMacro(characteristic["uuid"]))])

def set_ble_services(d):
    for service in d["services"]:
        name = prefix + "SERVICE_" + service["name"].upper() + '_UUID'
        print("Added: " + name)
        env.Append(CPPDEFINES=[(name, env.StringifyMacro(service["uuid"]))])
        add_ble_characteristics(service)

def get_version():
    with open(package_path) as f:
        d = json.load(f)
        print("Version: " + d["version"])
        return d["version"]

def get_certificates():
    with open("./gaerschrank_api_philun_de.pem") as f:
        return f.read()
    
def init_test_env():
    from dotenv import load_dotenv

    load_dotenv()

    if (environment == "test"):
        env.Append(CPPDEFINES=[
            ("WIFI_SSID", env.StringifyMacro(os.environ.get("WIFI_SSID"))), 
            ("WIFI_PASSWORD", env.StringifyMacro(os.environ.get("WIFI_PASSWORD")))
        ])


# Add BLE Variables to BUILD
d = get_ble_vars()
set_general_ble_vars(d)
set_ble_services(d)

# Add Version to BUILD
version = get_version()
env.Append(CPPDEFINES=[("VERSION", env.StringifyMacro(version))])

# Add certifcates
# certificate = get_certificates()
# env.Append(CPPDEFINES=[("PHILUN_PEM", env.StringifyMacro(certificate))])

# Http 
host = "https://gaerschrank.philun.de"
firmwarePath = "/esp32-brewtool/firmware.bin"
apiKey = "khdjfdiudsabdjsaffudfjasnl"
# if environment == "test":
#     host = "http://localhost:3000"

env.Append(CPPDEFINES=[
    ("API_HOST", env.StringifyMacro(host)), 
    ("FIRMWARE_UPGRADE_URL", env.StringifyMacro(host + firmwarePath)),
    ("DEFAULT_API_KEY", env.StringifyMacro(apiKey))
])

# Testing
init_test_env()

