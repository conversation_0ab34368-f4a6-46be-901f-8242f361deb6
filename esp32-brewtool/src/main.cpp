/*
  Board:    ESP32 Dev Module
*/
#include <Arduino.h>
#include <OneWire.h>
#include <DallasTemperature.h>
#include "BluethootApi.hpp"
#include <DynamicWifi.hpp>
#include <Api.hpp>

/*
  TODOs:
  - [ ] ota
  - [ ] offline help
  - [ ] update to new api & version
  - [ ] https://github.com/BernhardSchlegel/Brick-32
*/

/*
  Constants
*/
static int second = 1000;
static int minute = second * 60;
static int MaxMinusTemperature = -274;
static float TemperatureTolerance = 0.75;

/*
  Ports
*/
static int pinTempSensor = 26;
static int pinRelayHeat = 18;
static int pinRelayCooling = 19;

/*
  Falls beim Kompelieren ein <PERSON>hler 'void directModeInput(uint32_t)' kommt
  eine frühere Version von OneWire installieren
*/
OneWire oneWire(pinTempSensor);
DallasTemperature sensors(&oneWire);
/*
 Store
*/
bool statusRelayCooling = false;
bool statusRelayHeat = false;
// // Modus modus = FERMENTATION;
int powerOffLastTemp = 0;
long timestamp = 0;
int lastTemperature = MaxMinusTemperature;
// save last temperature, if internet is off
int destTemperature = MaxMinusTemperature;

void loadTime() {
  timestamp = loadTimeFromApi();
}

/*
  Schaltet das Relais ein oder aus
*/
void triggerRelay(int pin, bool status) {
  if (status) {
    digitalWrite(pin, HIGH);
  } else {
    digitalWrite(pin, LOW);
  }
}

/*
  Schaltet die Kühlung ein oder aus
 */
void setRelayCooling(bool status) {
  if (status != statusRelayCooling) {
    sendElectricalData("COOLING", status);
    triggerRelay(pinRelayCooling, status);
    statusRelayCooling = status;
  }
}

/*
  Schaltet die Heizung ein oder aus
 */
void setRelayHeat(bool status) {
  if (status != statusRelayHeat) {
    sendElectricalData("HEATING", status);
    triggerRelay(pinRelayHeat, status);
    statusRelayHeat = status;
  }
}

/*
  Schaltet die Heizung und Kühlung ein oder aus
 */
void triggerTemperatureHardware(float temperature, float destTemperature) {
  Serial.println("Temperature: " + String(temperature));
  if (destTemperature == MaxMinusTemperature || temperature == -127) {
    Serial.println("Fermentation is off");
    setRelayCooling(false);
    setRelayHeat(false);
    return;
  }

  const float maxTemperature = destTemperature + TemperatureTolerance;
  const float minTemperature = destTemperature - TemperatureTolerance;

  if ((destTemperature > temperature && statusRelayHeat == true) || (minTemperature > temperature && statusRelayHeat == false)) {
    Serial.println("Temperature is too low");
    setRelayCooling(false);
    setRelayHeat(true);
  } else if ((destTemperature < temperature && statusRelayCooling == true) || (maxTemperature < temperature && statusRelayCooling == false)) {
    Serial.println("Temperature is too high");
    setRelayCooling(true);
    setRelayHeat(false);
  } else {
    Serial.println("Temperature is right");
    setRelayCooling(false);
    setRelayHeat(false);
  }
}

void sendTemperatureStatistic(float temperature, int destTemperature) {
  // only send every 5 minute if fermentation is not on
  if (destTemperature == MaxMinusTemperature) {
    float difference = millis() - powerOffLastTemp;
    if (difference < minute * 5) {
      return;
    } else {
      powerOffLastTemp = millis();
    }
  }

  if (temperature == -127) {
    // TODO: error with temperature sensor
    return;
  }

  if (!hasInternet) {
    // TODO: save temperature in file system/preferences
    // sendTemperatureStatisticInOffline(temperature);
  } else {
    sendTemperatureStatisticToApi(temperature);
  }
}

void setup() {
  Serial.begin(115200);
  
  /*
    Init Temperature Sensor
  */
  sensors.begin();
  delay(100);
  
  setupBluetoothLE();
  setupWifi();

  /*
    Init Time
  */
  if (!hasInternet) {
    // Serial.println("No internet connection");
  } else {
    loadTime();
  }

  /*
    Init Relais
  */
  pinMode(pinRelayCooling, OUTPUT);
  pinMode(pinRelayHeat, OUTPUT);
}

void loop() {
  const int startTime = millis();

  onLoopWifi();
  // onLoopOta();

  if (hasInternet) {
    destTemperature = getDestinatedTemperature();
  }
  Serial.println("Destination temperature: " + String(destTemperature));
  Serial.println("MAC: " + getMac());

  sensors.requestTemperatures(); 
  float measuredTemperature = sensors.getTempCByIndex(0);

  // send measured temperature to server
  sendTemperatureStatistic(measuredTemperature, destTemperature);

  // check for adjustment in temperature
  triggerTemperatureHardware(measuredTemperature, destTemperature);
  
  // // run every minute
  const int runTime = millis() - startTime;
  timestamp = timestamp + minute;
  const int delayTime = (second*10) - runTime;
  if (delayTime > 0) {
    delay(delayTime);
  } else {
    delay(1);
  }
}