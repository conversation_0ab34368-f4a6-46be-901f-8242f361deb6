#include <vector>
#include <string>
#include "esp_wifi.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include <nvs.h>
#include <nvs_flash.h>
#include <string.h>

#define PREV_WIFI_LABEL "philun-wifi"

/*
  Constants
*/

#define LABEL_WIFI_SSID "ssid"
#define LABEL_WIFI_PASSWORD "password"

static const char *TAG = "DynamicWifi";

extern bool hasInternet = false;

int64_t lastReconnect = 0;

bool connectWifi() {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open(PREV_WIFI_LABEL, NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) {
    ESP_LOGI(TAG, "NVS open failed");
    return false;
  }
  char ssid[33] = {0};
  char password[65] = {0};
  size_t ssid_len = sizeof(ssid);
  size_t password_len = sizeof(password);
  err = nvs_get_str(nvs_handle, LABEL_WIFI_SSID, ssid, &ssid_len);
  if (err != ESP_OK) ssid[0] = '\0';
  err = nvs_get_str(nvs_handle, LABEL_WIFI_PASSWORD, password, &password_len);
  if (err != ESP_OK) password[0] = '\0';
  nvs_close(nvs_handle);

  if (ssid[0] == '\0' || password[0] == '\0') {
    ESP_LOGI(TAG, "No SSID or password stored");
    return false;
  }

  wifi_config_t wifi_config = {};
  strncpy((char *)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid));
  strncpy((char *)wifi_config.sta.password, password, sizeof(wifi_config.sta.password));

  esp_err_t ret = esp_wifi_set_mode(WIFI_MODE_STA);
  if (ret != ESP_OK) {
    ESP_LOGE(TAG, "Failed to set WiFi mode");
    return false;
  }
  ret = esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
  if (ret != ESP_OK) {
    ESP_LOGE(TAG, "Failed to set WiFi config");
    return false;
  }
  ret = esp_wifi_start();
  if (ret != ESP_OK) {
    ESP_LOGE(TAG, "Failed to start WiFi");
    return false;
  }

  ESP_LOGI(TAG, "Connecting to SSID: %s", ssid);
  for (int i = 0; i < 20; i++) {
    vTaskDelay(500 / portTICK_PERIOD_MS);
    wifi_ap_record_t ap_info;
    if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
      ESP_LOGI(TAG, "Connected to %s", (char *)ap_info.ssid);
      return true;
    }
  }
  ESP_LOGW(TAG, "Not connected to %s", ssid);
  return false;
}

void setupWifi() {
  esp_err_t ret = nvs_flash_init();
  if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
    nvs_flash_erase();
    nvs_flash_init();
  }
  tcpip_adapter_init();
  ESP_ERROR_CHECK(esp_event_loop_create_default());
  wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
  ESP_ERROR_CHECK(esp_wifi_init(&cfg));
  hasInternet = connectWifi();
}

void onLoopWifi() {
  wifi_ap_record_t ap_info;
  hasInternet = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

  int64_t now = esp_timer_get_time() / 1000; // ms
  if (!hasInternet && lastReconnect == 0) {
    ESP_LOGW(TAG, "No internet connection");
    lastReconnect = now;
  }

  if (!hasInternet && lastReconnect != 0 && now - lastReconnect > 600000) {
    lastReconnect = 0;
    hasInternet = connectWifi();
  }
}

const char* getWifiPassword() {
  static char password[65] = {0};
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open(PREV_WIFI_LABEL, NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) {
    password[0] = '\0';
    return password;
  }
  size_t password_len = sizeof(password);
  err = nvs_get_str(nvs_handle, LABEL_WIFI_PASSWORD, password, &password_len);
  nvs_close(nvs_handle);
  if (err != ESP_OK) password[0] = '\0';
  return password;
}

bool setWifi(const char* ssid, const char* password) {
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open(PREV_WIFI_LABEL, NVS_READWRITE, &nvs_handle);
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "NVS open failed for write");
    return false;
  }
  err = nvs_set_str(nvs_handle, LABEL_WIFI_SSID, ssid);
  if (err != ESP_OK) {
    nvs_close(nvs_handle);
    return false;
  }
  err = nvs_set_str(nvs_handle, LABEL_WIFI_PASSWORD, password);
  if (err != ESP_OK) {
    nvs_close(nvs_handle);
    return false;
  }
  nvs_commit(nvs_handle);
  nvs_close(nvs_handle);
  return connectWifi();
}

const char* getWifiSsid() {
  static char ssid[33] = {0};
  nvs_handle_t nvs_handle;
  esp_err_t err = nvs_open(PREV_WIFI_LABEL, NVS_READONLY, &nvs_handle);
  if (err != ESP_OK) {
    ssid[0] = '\0';
    return ssid;
  }
  size_t ssid_len = sizeof(ssid);
  err = nvs_get_str(nvs_handle, LABEL_WIFI_SSID, ssid, &ssid_len);
  nvs_close(nvs_handle);
  if (err != ESP_OK) ssid[0] = '\0';
  return ssid;
}


std::vector<std::string> scanWifiNetworks() {
  // Disconnect WiFi before scanning
  esp_wifi_disconnect();
  vTaskDelay(100 / portTICK_PERIOD_MS);

  // Start scan
  wifi_scan_config_t scan_config = {};
  scan_config.ssid = NULL;
  scan_config.bssid = NULL;
  scan_config.channel = 0;
  scan_config.show_hidden = true;
  esp_err_t err = esp_wifi_scan_start(&scan_config, true); // true: block until done
  if (err != ESP_OK) {
    ESP_LOGE(TAG, "WiFi scan start failed");
    return {};
  }

  uint16_t ap_num = 0;
  esp_wifi_scan_get_ap_num(&ap_num);
  wifi_ap_record_t *ap_records = (wifi_ap_record_t *)malloc(sizeof(wifi_ap_record_t) * ap_num);
  if (!ap_records) {
    ESP_LOGE(TAG, "Memory allocation failed for AP records");
    return {};
  }
  err = esp_wifi_scan_get_ap_records(&ap_num, ap_records);
  std::vector<std::string> ssid_list;
  if (err == ESP_OK) {
    for (int i = 0; i < ap_num; ++i) {
      ssid_list.push_back(std::string((char *)ap_records[i].ssid));
    }
  } else {
    ESP_LOGE(TAG, "Failed to get AP records");
  }
  free(ap_records);

  // Reconnect WiFi
  connectWifi();

  return ssid_list;
}