// Ensure u32_t is defined before including esp_http_client.h
#include <arch/cc.h>
#include <IPAddress.h>
#include "esp_http_client.h"
#include <Preferences.h>

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

extern const uint8_t server_cert_pem_start[];
extern const uint8_t server_cert_pem_end[];

void sendPost(String route, String data);
String getData(String route);

void onInitHttp();

void setApiKey(String newApiKey);

String getMac();