// Ensure u32_t is defined before including esp_http_client.h
#include <arch/cc.h>
#include <IPAddress.h>
#include "esp_http_client.h"
#include <Preferences.h>

static char const *LABEL_API_KEY = "api-key";
static char const *LABEL_SERVER_CERTIFICATE = "server_certificate";

#define HTTP_UNKNOW_ERROR "Unknown error"
#define HTTP_NOT_FOUND "Not found"
#define HTTP_SERVER_ERROR "Server error"

extern const uint8_t server_cert_pem_start[] asm("_binary_gaerschrank_philun_de_pem_start");
extern const uint8_t server_cert_pem_end[] asm("_binary_gaerschrank_philun_de_pem_end");

const char* PREF_LABEL_HTTP = "http";

void setApiKey(String newApiKey) {
  Preferences httpPreferences;
  httpPreferences.begin(PREF_LABEL_HTTP, false);
  httpPreferences.putString(LABEL_API_KEY, newApiKey);
  httpPreferences.end();
}

String mac2String(byte ar[]) {
  String s;
  for (byte i = 0; i < 6; ++i)
  {
    char buf[3];
    sprintf(buf, "%02X", ar[i]); // J-M-L: slight modification, added the 0 in the format for padding 
    s += buf;
    if (i < 5) s += ':';
  }
  return s;
}

String getMac() {
  return mac2String((byte*) ESP.getEfuseMac());
}

esp_http_client_handle_t preHttpRequest(const char* route, const char* method, const char* data = nullptr) {
    char serverPath[256];
    snprintf(serverPath, sizeof(serverPath), "%s/%s", API_HOST, route);

    Preferences httpPreferences;
    httpPreferences.begin(PREF_LABEL_HTTP, false);
    String apiKey = httpPreferences.getString(LABEL_API_KEY, DEFAULT_API_KEY);
    httpPreferences.end();

    esp_http_client_config_t config = {};
    config.url = serverPath;
    config.method = (strcmp(method, "POST") == 0) ? HTTP_METHOD_POST : HTTP_METHOD_GET;
    config.use_global_ca_store = false;
    config.skip_cert_common_name_check = true;
    esp_http_client_handle_t client = esp_http_client_init(&config);
    esp_http_client_set_header(client, "X-ApiKey", apiKey.c_str());
    esp_http_client_set_header(client, "X-Device", getMac().c_str());
    if (data && strcmp(method, "POST") == 0) {
        // esp_http_client_set_header(client, "Content-Type", "application/json");
        esp_http_client_set_post_field(client, data, strlen(data));
    }
    return client;
}

// void postHttpRequest(HTTPClient &http) {
//   String receivedVersion = http.header(HTTP_VERSION_HEADER);
//   if (receivedVersion != VERSION && receivedVersion != "") {
//     // startFirmwareUpgrade();
//   }
// }

void sendPost(String route, String data) {
    esp_http_client_handle_t client = preHttpRequest(route.c_str(), "POST", data.c_str());
    esp_err_t err = esp_http_client_perform(client);
    int httpResponseCode = esp_http_client_get_status_code(client);
    Serial.printf("Request: %s => %s\n", route.c_str(), data.c_str());
    Serial.printf("HTTP Response code: %d\n", httpResponseCode);
    if (err != ESP_OK) {
        Serial.printf("Error on sending POST: %s\n", esp_err_to_name(err));
    }
    esp_http_client_cleanup(client);
}

String getData(String route) {
    esp_http_client_handle_t client = preHttpRequest(route.c_str(), "GET");
    esp_err_t err = esp_http_client_perform(client);
    int httpResponseCode = esp_http_client_get_status_code(client);
    Serial.printf("HTTP Response code: %d\n", httpResponseCode);
    if (err != ESP_OK) {
        Serial.println("Error on HTTP request");
        esp_http_client_cleanup(client);
        return HTTP_UNKNOW_ERROR;
    }
    if (httpResponseCode == 404) {
        Serial.println("404 Not Found");
        esp_http_client_cleanup(client);
        return HTTP_NOT_FOUND;
    }
    if (httpResponseCode >= 500) {
        Serial.printf("Server error: %d\n", httpResponseCode);
        esp_http_client_cleanup(client);
        return HTTP_SERVER_ERROR;
    }
    char buffer[1024];
    int content_length = esp_http_client_read(client, buffer, sizeof(buffer) - 1);
    String payload;
    if (content_length > 0) {
        buffer[content_length] = '\0';
        payload = String(buffer);
    } else {
        Serial.println("Received empty payload");
        esp_http_client_cleanup(client);
        return HTTP_UNKNOW_ERROR;
    }
    Serial.println("Payload: " + payload);
    esp_http_client_cleanup(client);
    return payload;
}